(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[680],{2204:(e,t,r)=>{Promise.resolve().then(r.bind(r,8897))},8897:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});var a=r(5155),s=r(2115),l=r(4889),o=r(7468);function i(e){var t,r,i,n;let{t:c,lang:d}=e,[m,u]=(0,s.useState)("general"),[h,x]=(0,s.useState)(!1),[p,g]=(0,s.useState)(null),[f,b]=(0,s.useState)({name:"",email:"",subject:"",message:"",privacy:!1}),v=e=>{let{name:t,value:r}=e.target;b({...f,[t]:r})},w=async e=>{if(e.preventDefault(),!f.email||!f.message){g({success:!1,message:"Per favore, compila tutti i campi obbligatori."});return}if(!f.privacy){g({success:!1,message:"Devi accettare la privacy policy per procedere."});return}x(!0),g(null);try{let e=await fetch("/api/send-email.php",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:f.name,email:f.email,subject:f.subject||"Richiesta informazioni",message:f.message,isEmergency:!1})}),t=await e.json();e.ok?(g({success:!0,message:"Grazie! Il tuo messaggio \xe8 stato inviato con successo. Ti risponderemo al pi\xf9 presto."}),b({name:"",email:"",subject:"",message:"",privacy:!1})):g({success:!1,message:t.error||"Si \xe8 verificato un errore durante l'invio del messaggio. Riprova pi\xf9 tardi."})}catch(e){console.error("Errore nell'invio del form:",e),g({success:!1,message:"Si \xe8 verificato un errore durante l'invio del messaggio. Riprova pi\xf9 tardi."})}finally{x(!1)}};return(0,a.jsxs)("main",{children:[(0,a.jsxs)("section",{className:"relative h-[75vh] min-h-[550px] overflow-hidden bg-gradient-to-br from-inparoblue-900 via-inparoblue-700 to-inparoblue-500 pt-16",children:[(0,a.jsxs)("div",{className:"absolute inset-0 z-0",children:[(0,a.jsx)("div",{className:"absolute w-full h-full opacity-20",style:{backgroundImage:'url("/images/noise.png")'}}),(0,a.jsx)("div",{className:"absolute top-0 left-0 right-0 h-1/3 bg-gradient-to-b from-blue-500/10 to-transparent"}),(0,a.jsx)("div",{className:"absolute inset-0",style:{backgroundImage:"radial-gradient(circle at 30% 50%, rgba(255, 255, 255, 0.1) 0%, transparent 25%), radial-gradient(circle at 70% 30%, rgba(255, 255, 255, 0.08) 0%, transparent 25%)"}}),(0,a.jsx)("div",{className:"absolute -top-24 -right-24 w-96 h-96 bg-red-500 rounded-full mix-blend-soft-light filter blur-3xl opacity-30 animate-blob animation-delay-2000"}),(0,a.jsx)("div",{className:"absolute top-1/3 -left-20 w-72 h-72 bg-blue-500 rounded-full mix-blend-soft-light filter blur-3xl opacity-30 animate-blob animation-delay-4000"}),(0,a.jsx)("div",{className:"absolute -bottom-32 left-1/3 w-80 h-80 bg-purple-500 rounded-full mix-blend-soft-light filter blur-3xl opacity-30 animate-blob"}),(0,a.jsx)("div",{className:"absolute top-1/2 right-1/4 w-64 h-64 bg-pink-500 rounded-full mix-blend-soft-light filter blur-3xl opacity-30 animate-blob animation-delay-3000"}),(0,a.jsxs)("div",{className:"absolute inset-0 overflow-hidden opacity-20",children:[(0,a.jsx)("div",{className:"absolute top-[10%] left-0 right-0 h-[1px] bg-gradient-to-r from-transparent via-blue-400 to-transparent animate-pulse-slow"}),(0,a.jsx)("div",{className:"absolute top-[30%] left-0 right-0 h-[1px] bg-gradient-to-r from-transparent via-red-400 to-transparent animate-pulse-slow animation-delay-2000"}),(0,a.jsx)("div",{className:"absolute top-[70%] left-0 right-0 h-[1px] bg-gradient-to-r from-transparent via-purple-400 to-transparent animate-pulse-slow animation-delay-3000"}),(0,a.jsx)("div",{className:"absolute bottom-[20%] left-0 right-0 h-[1px] bg-gradient-to-r from-transparent via-indigo-400 to-transparent animate-pulse-slow animation-delay-4000"})]})]}),(0,a.jsx)("div",{className:"absolute bottom-0 left-0 right-0 z-10",children:(0,a.jsxs)("div",{className:"relative h-16",children:[(0,a.jsx)("div",{className:"absolute top-0 inset-x-0 h-8 bg-gradient-to-b from-transparent to-white/10"}),(0,a.jsx)("div",{className:"absolute top-8 inset-x-0 h-[1px] bg-white/20"}),(0,a.jsx)("div",{className:"absolute bottom-0 inset-x-0 h-8 bg-white",style:{clipPath:"polygon(0 100%, 100% 100%, 100% 0)"}}),(0,a.jsx)("div",{className:"absolute bottom-0 inset-x-0 h-8 bg-white",style:{clipPath:"polygon(0 100%, 0 0, 100% 100%)"}})]})}),(0,a.jsx)("div",{className:"container max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10 h-full flex flex-col justify-center",children:(0,a.jsxs)("div",{className:"text-center max-w-3xl mx-auto",children:[(0,a.jsx)(l.A,{delay:.2,children:(0,a.jsxs)("div",{className:"inline-flex items-center px-4 py-1.5 rounded-full bg-gradient-to-r from-inparoblue-500 to-inparoblue-600 text-white text-sm font-medium mb-5 shadow-lg border border-white/10 backdrop-blur-sm relative overflow-hidden group",children:[(0,a.jsx)("span",{className:"absolute inset-0 bg-gradient-to-r from-inparoblue-500/20 to-inparoblue-600/20 group-hover:opacity-80 opacity-0 transition-opacity duration-300"}),(0,a.jsx)("span",{className:"absolute top-0 left-0 w-full h-full bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full animate-shimmer"}),(0,a.jsxs)("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:[(0,a.jsx)("path",{d:"M2 5a2 2 0 012-2h7a2 2 0 012 2v4a2 2 0 01-2 2H9l-3 3v-3H4a2 2 0 01-2-2V5z"}),(0,a.jsx)("path",{d:"M15 7v2a4 4 0 01-4 4H9.828l-1.766 1.767c.28.149.599.233.938.233h2l3 3v-3h2a2 2 0 002-2V9a2 2 0 00-2-2h-1z"})]}),(0,a.jsx)("span",{className:"ml-2",children:"Contatti"})]})}),(0,a.jsx)(l.A,{delay:.4,children:(0,a.jsx)("h1",{className:"text-5xl md:text-7xl font-manrope font-bold tracking-tighter leading-tight mb-6 text-white text-shadow-glow",children:(0,a.jsxs)("span",{className:"relative",children:[(0,a.jsx)("span",{className:"relative z-10 inline-block",children:c.contact.title}),(0,a.jsx)("span",{className:"absolute -inset-0.5 bg-gradient-to-r from-inparoblue-500 to-inparoblue-600 blur-2xl opacity-20 rounded-full z-0"})]})})}),(0,a.jsx)(l.A,{delay:.6,children:(0,a.jsxs)("div",{className:"relative h-[3px] w-40 mx-auto my-8 overflow-hidden rounded-full",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-inparoblue-500 via-inparoblue-600 to-inparoblue-700"}),(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-inparoblue-700 via-inparoblue-500 to-inparoblue-400 animate-gradient-x"}),(0,a.jsx)("div",{className:"absolute top-0 left-0 w-full h-full bg-gradient-to-r from-transparent via-white to-transparent -translate-x-full animate-shimmer"})]})}),(0,a.jsx)(l.A,{delay:.8,children:(0,a.jsx)("p",{className:"text-base md:text-xl font-sans leading-relaxed text-white/90 max-w-2xl mx-auto mb-10",children:c.contact.description})}),(0,a.jsx)(l.A,{delay:1,children:(0,a.jsxs)("div",{className:"flex flex-wrap justify-center gap-4 mt-8",children:[(0,a.jsxs)("button",{className:"px-6 py-3 bg-gradient-to-r from-inparoblue-500 to-inparoblue-600 hover:from-inparoblue-600 hover:to-inparoblue-700 text-white rounded-full shadow-lg shadow-inparoblue-500/30 transition-all duration-300 mr-3 flex items-center group",children:[(0,a.jsx)("span",{className:"mr-2",children:c.contact.generalInquiry}),(0,a.jsx)("svg",{className:"w-5 h-5 transform group-hover:translate-x-1 transition-transform",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M13 7l5 5m0 0l-5 5m5-5H6"})})]}),(0,a.jsxs)("button",{className:"px-6 py-3 bg-gradient-to-r from-inparoblue-600 to-inparoblue-700 hover:from-inparoblue-700 hover:to-inparoblue-800 text-white rounded-full shadow-lg shadow-inparoblue-500/30 transition-all duration-300 flex items-center group",children:[(0,a.jsx)("svg",{className:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,a.jsx)("span",{children:c.common.emergency})]})]})})]})})]}),(0,a.jsxs)("section",{className:"bg-white py-20 relative overflow-hidden",children:[(0,a.jsxs)("div",{className:"absolute inset-0 -z-10 opacity-60",children:[(0,a.jsx)("div",{className:"absolute -top-20 -left-20 w-64 h-64 bg-blue-50 rounded-full filter blur-3xl opacity-40"}),(0,a.jsx)("div",{className:"absolute -bottom-20 -right-20 w-64 h-64 bg-red-50 rounded-full filter blur-3xl opacity-40"}),(0,a.jsx)("div",{className:"absolute top-1/2 left-1/4 w-48 h-48 bg-purple-50 rounded-full filter blur-3xl opacity-30"}),(0,a.jsx)("div",{className:"absolute w-full h-full opacity-10",style:{backgroundImage:'url("/images/noise.png")'}})]}),(0,a.jsxs)("div",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsx)("div",{className:"max-w-3xl mx-auto",children:(0,a.jsx)("div",{className:"mb-14 relative z-10",children:(0,a.jsxs)("div",{className:"bg-white p-2 sm:p-2.5 rounded-2xl shadow-2xl border border-gray-100 flex flex-col sm:flex-row justify-center max-w-lg mx-auto overflow-hidden backdrop-blur-sm gap-2",children:[(0,a.jsxs)("button",{onClick:()=>u("general"),className:"\n                    flex-1 rounded-xl px-5 py-4 text-base font-medium transition-all duration-300 flex items-center justify-center gap-3\n                    ".concat("general"===m?"bg-gradient-to-r from-inparoblue-600 to-inparoblue-700 text-white shadow-lg transform scale-[1.02]":"text-gray-700 hover:bg-gray-50 border border-gray-100 hover:border-gray-200","\n                  "),"aria-pressed":"general"===m,"aria-label":"Seleziona richiesta generale",children:[(0,a.jsx)("div",{className:"rounded-full p-1.5 bg-white/10 flex items-center justify-center",children:(0,a.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M8 12h.01M12 12h.01M16 12h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,a.jsx)("span",{className:"font-medium",children:c.contact.generalInquiry})]}),(0,a.jsxs)("button",{onClick:()=>u("emergency"),className:"\n                    flex-1 rounded-xl px-5 py-4 text-base font-medium transition-all duration-300 flex items-center justify-center gap-3\n                    ".concat("emergency"===m?"bg-gradient-to-r from-inparoblue-600 to-inparoblue-700 text-white shadow-lg transform scale-[1.02]":"text-gray-700 hover:bg-gray-50 border border-gray-100 hover:border-gray-200","\n                  "),"aria-pressed":"emergency"===m,"aria-label":"Seleziona richiesta emergenza",children:[(0,a.jsx)("div",{className:"rounded-full p-1.5 bg-white/10 flex items-center justify-center",children:(0,a.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,a.jsx)("span",{className:"font-medium",children:c.contact.emergency})]})]})})}),(0,a.jsxs)("div",{className:"mt-12 relative",children:[(0,a.jsx)("div",{className:"absolute -top-6 -right-6 w-32 h-32 bg-inparoblue-50 rounded-full mix-blend-multiply filter blur-2xl opacity-70"}),(0,a.jsx)("div",{className:"absolute -bottom-6 -left-6 w-32 h-32 bg-inparoblue-100 rounded-full mix-blend-multiply filter blur-2xl opacity-70"}),(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-inparoblue-50 via-white to-inparoblue-50 opacity-40 -z-10 rounded-3xl transform -rotate-1 scale-105"}),(0,a.jsxs)("div",{className:"overflow-hidden rounded-3xl bg-white shadow-xl border border-gray-100 backdrop-blur-sm",children:[(0,a.jsx)("div",{className:"w-full h-2 bg-gradient-to-r from-inparoblue-600 to-inparoblue-700"}),(0,a.jsx)("div",{className:"px-6 py-10 sm:p-12",children:"emergency"===m?(0,a.jsx)(o.I,{t:c,lang:d}):(0,a.jsxs)("form",{className:"space-y-8",onSubmit:e=>e.preventDefault(),children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"col-span-1 group",children:[(0,a.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-2 transition-colors group-focus-within:text-red-600",children:c.contact.form.name}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3.5 flex items-center pointer-events-none transition-colors group-focus-within:text-red-500",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-gray-400 group-focus-within:text-red-500 transition-colors",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z",clipRule:"evenodd"})})}),(0,a.jsx)("input",{type:"text",name:"name",id:"name",className:"pl-10 block w-full rounded-xl border-gray-200 shadow-sm focus:border-red-500 focus:ring-red-500 sm:text-sm transition-all duration-300 py-3 bg-gray-50 focus:bg-white text-gray-800",placeholder:(null===(t=c.contact.form.placeholders)||void 0===t?void 0:t.name)||"",value:f.name,onChange:v})]})]}),(0,a.jsxs)("div",{className:"col-span-1 group",children:[(0,a.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2 transition-colors group-focus-within:text-red-600",children:c.contact.form.email}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3.5 flex items-center pointer-events-none",children:(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-gray-400 group-focus-within:text-red-500 transition-colors",viewBox:"0 0 20 20",fill:"currentColor",children:[(0,a.jsx)("path",{d:"M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"}),(0,a.jsx)("path",{d:"M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"})]})}),(0,a.jsx)("input",{type:"email",name:"email",id:"email",className:"pl-10 block w-full rounded-xl border-gray-200 shadow-sm focus:border-red-500 focus:ring-red-500 sm:text-sm transition-all duration-300 py-3 bg-gray-50 focus:bg-white text-gray-800",placeholder:(null===(r=c.contact.form.placeholders)||void 0===r?void 0:r.email)||"",value:f.email,onChange:v,required:!0})]})]})]}),(0,a.jsxs)("div",{className:"group",children:[(0,a.jsx)("label",{htmlFor:"subject",className:"block text-sm font-medium text-gray-700 mb-2 transition-colors group-focus-within:text-red-600",children:c.contact.form.subject}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3.5 flex items-center pointer-events-none",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-gray-400 group-focus-within:text-red-500 transition-colors",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z",clipRule:"evenodd"})})}),(0,a.jsx)("input",{type:"text",name:"subject",id:"subject",className:"pl-10 block w-full rounded-xl border-gray-200 shadow-sm focus:border-red-500 focus:ring-red-500 sm:text-sm transition-all duration-300 py-3 bg-gray-50 focus:bg-white text-gray-800",placeholder:(null===(i=c.contact.form.placeholders)||void 0===i?void 0:i.subject)||"",value:f.subject,onChange:v})]})]}),(0,a.jsxs)("div",{className:"group",children:[(0,a.jsx)("label",{htmlFor:"message",className:"block text-sm font-medium text-gray-700 mb-2 transition-colors group-focus-within:text-red-600",children:c.contact.form.message}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute top-3 left-3.5 flex items-start pointer-events-none",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-gray-400 group-focus-within:text-red-500 transition-colors",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M18 5v8a2 2 0 01-2 2h-5l-5 4v-4H4a2 2 0 01-2-2V5a2 2 0 012-2h12a2 2 0 012 2zM7 8H5v2h2V8zm2 0h2v2H9V8zm6 0h-2v2h2V8z",clipRule:"evenodd"})})}),(0,a.jsx)("textarea",{id:"message",name:"message",rows:5,className:"pl-10 block w-full rounded-xl border-gray-200 shadow-sm focus:border-red-500 focus:ring-red-500 sm:text-sm transition-all duration-300 bg-gray-50 focus:bg-white text-gray-800",placeholder:(null===(n=c.contact.form.placeholders)||void 0===n?void 0:n.message)||"",value:f.message,onChange:v,required:!0})]})]}),(0,a.jsxs)("div",{className:"flex items-start p-4 bg-gray-50 rounded-xl border border-gray-100",children:[(0,a.jsx)("div",{className:"flex-shrink-0 mt-0.5",children:(0,a.jsxs)("div",{className:"relative flex items-center justify-center h-6 w-6",children:[(0,a.jsx)("input",{id:"privacy",name:"privacy",type:"checkbox",className:"h-5 w-5 text-red-600 focus:ring-red-500 focus:ring-offset-0 border-gray-300 rounded cursor-pointer",checked:f.privacy,onChange:e=>{let{name:t,checked:r}=e.target;b({...f,[t]:r})},required:!0}),(0,a.jsx)("svg",{className:"absolute h-6 w-6 text-red-500 opacity-0 peer-checked:opacity-100 transform scale-90 transition-all duration-200",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("circle",{cx:"12",cy:"12",r:"12",fill:"currentColor",fillOpacity:"0.2"})})]})}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("label",{htmlFor:"privacy",className:"text-sm font-medium text-gray-700 cursor-pointer",children:c.contact.form.privacy}),(0,a.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:["it"===d?"I tuoi dati saranno trattati secondo la nostra ":"fr"===d?"Vos donn\xe9es seront trait\xe9es conform\xe9ment \xe0 notre ":"Ihre Daten werden gem\xe4\xdf unserer ",(0,a.jsx)("a",{href:"/".concat(d,"/privacy-policy"),className:"text-red-600 hover:text-red-800 underline transition-colors",children:"it"===d?"Informativa sulla Privacy":"fr"===d?"Politique de Confidentialit\xe9":"Datenschutzerkl\xe4rung"})]})]})]}),(0,a.jsx)("div",{className:"pt-4",children:(0,a.jsx)("button",{type:"submit",className:"w-full flex justify-center items-center rounded-xl bg-gradient-to-r from-inparoblue-500 to-inparoblue-600 px-6 py-4 text-base font-semibold text-white shadow-lg hover:from-inparoblue-600 hover:to-inparoblue-700 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-inparoblue-600 transition-all duration-300 transform hover:-translate-y-1 hover:shadow-xl disabled:opacity-70 disabled:cursor-not-allowed",disabled:h,onClick:w,children:h?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,a.jsx)("span",{children:"Invio in corso..."})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("span",{children:c.contact.form.submit}),(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"ml-2 h-5 w-5 animate-pulse",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z",clipRule:"evenodd"})})]})})}),p&&(0,a.jsx)("div",{className:"mt-4 p-4 rounded-lg ".concat(p.success?"bg-green-50 text-green-800":"bg-red-50 text-red-800"),children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:p.success?(0,a.jsx)("svg",{className:"h-5 w-5 text-green-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}):(0,a.jsx)("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0z",clipRule:"evenodd"})})}),(0,a.jsx)("div",{className:"ml-3",children:(0,a.jsx)("p",{className:"text-sm font-medium",children:p.message})})]})})]})})]})]})]})]}),(0,a.jsxs)("section",{className:"relative overflow-hidden bg-gradient-to-br from-gray-50 to-white py-20 mt-10",children:[(0,a.jsx)("div",{className:"absolute -top-10 -right-10 w-40 h-40 bg-red-50 rounded-full filter blur-3xl opacity-60"}),(0,a.jsx)("div",{className:"absolute -bottom-10 -left-10 w-40 h-40 bg-blue-50 rounded-full filter blur-3xl opacity-60"}),(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"text-center mb-14",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 sm:text-4xl mb-4",children:c.contact.howToReachUs.title}),(0,a.jsx)("div",{className:"h-1 w-16 mx-auto bg-gradient-to-r from-inparoblue-600 to-inparoblue-700 rounded-full my-4"}),(0,a.jsx)("p",{className:"text-lg text-gray-600 max-w-2xl mx-auto",children:c.contact.howToReachUs.description})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8 xl:gap-12",children:[(0,a.jsxs)("div",{className:"group bg-white p-8 rounded-2xl shadow-lg border border-gray-100 flex flex-col items-center text-center transform transition-all duration-500 hover:shadow-xl hover:-translate-y-2 relative overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute top-0 inset-x-0 h-2 bg-gradient-to-r from-inparoblue-600 to-inparoblue-700 transform transition-transform duration-300 group-hover:scale-x-100"}),(0,a.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-inparoblue-600 to-inparoblue-700 rounded-2xl flex items-center justify-center mb-6 transform rotate-3 group-hover:rotate-6 transition-all duration-300 shadow-md",children:(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-8 w-8 text-white",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:[(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})]})}),(0,a.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-3",children:c.contact.howToReachUs.office}),(0,a.jsxs)("p",{className:"text-gray-600 mb-4",children:["Inparo Gmbh",(0,a.jsx)("br",{}),"Gubelstrasse 15",(0,a.jsx)("br",{}),"6300 Zug, Svizzera"]}),(0,a.jsxs)("a",{href:"https://maps.google.com",target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center text-sm font-medium text-red-600 hover:text-purple-700 transition-colors",children:[(0,a.jsx)("span",{children:c.contact.howToReachUs.viewOnMaps}),(0,a.jsx)("svg",{className:"ml-1 w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z",clipRule:"evenodd"})})]})]}),(0,a.jsxs)("div",{className:"group bg-white p-8 rounded-2xl shadow-lg border border-gray-100 flex flex-col items-center text-center transform transition-all duration-500 hover:shadow-xl hover:-translate-y-2 relative overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute top-0 inset-x-0 h-2 bg-gradient-to-r from-inparoblue-600 to-inparoblue-700 transform transition-transform duration-300 group-hover:scale-x-100"}),(0,a.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-inparoblue-600 to-inparoblue-700 rounded-2xl flex items-center justify-center mb-6 transform -rotate-3 group-hover:rotate-0 transition-all duration-300 shadow-md",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-8 w-8 text-white",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2v-3a2 2 0 00-2-2H5a2 2 0 00-2 2v3a2 2 0 002 2z"})})}),(0,a.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-3",children:c.contact.howToReachUs.email}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"<EMAIL>"}),(0,a.jsxs)("a",{href:"mailto:<EMAIL>",className:"inline-flex items-center text-sm font-medium text-red-600 hover:text-purple-700 transition-colors",children:[(0,a.jsx)("span",{children:c.contact.howToReachUs.sendEmail}),(0,a.jsx)("svg",{className:"ml-1 w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z",clipRule:"evenodd"})})]})]}),(0,a.jsxs)("div",{className:"group bg-white p-8 rounded-2xl shadow-lg border border-gray-100 flex flex-col items-center text-center transform transition-all duration-500 hover:shadow-xl hover:-translate-y-2 relative overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute top-0 inset-x-0 h-2 bg-gradient-to-r from-inparoblue-600 to-inparoblue-700 transform transition-transform duration-300 group-hover:scale-x-100"}),(0,a.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-inparoblue-600 to-inparoblue-700 rounded-2xl flex items-center justify-center mb-6 transform rotate-3 group-hover:-rotate-6 transition-all duration-300 shadow-md",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-8 w-8 text-white",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"})})}),(0,a.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-3",children:c.contact.howToReachUs.phone}),(0,a.jsxs)("p",{className:"text-gray-600 mb-4",children:["+41 76 466 21 22",(0,a.jsx)("br",{}),"Lun-Ven: 9:00-18:00"]}),(0,a.jsxs)("a",{href:"tel:+41764662122",className:"inline-flex items-center text-sm font-medium text-red-600 hover:text-purple-700 transition-colors",children:[(0,a.jsx)("span",{children:c.contact.howToReachUs.callNow}),(0,a.jsx)("svg",{className:"ml-1 w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z",clipRule:"evenodd"})})]}),(0,a.jsxs)("a",{href:"https://wa.me/41764662122",target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center text-sm font-medium text-green-600 hover:text-green-700 transition-colors",children:[(0,a.jsx)("span",{children:c.contact.howToReachUs.whatsapp}),(0,a.jsx)("svg",{className:"ml-1 w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z",clipRule:"evenodd"})})]})]})]})]}),(0,a.jsx)("div",{className:"absolute left-0 right-0 bottom-0 h-8 overflow-hidden",children:(0,a.jsx)("svg",{className:"w-full h-full",viewBox:"0 0 1200 120",preserveAspectRatio:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("path",{d:"M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V120H0V95.72C57.71,118.68,121.41,111.23,165.53,101.1Z",fill:"#ffffff"})})})]})]})}},4889:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var a=r(5155),s=r(3478);function l(e){let{children:t,className:r="",delay:l=0}=e;return(0,a.jsx)(s.P.div,{className:r,initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:l},children:t})}},5683:(e,t,r)=>{"use strict";r.d(t,{N:()=>f});var a=r(5155),s=r(2115),l=r(4710),o=r(9234),i=r(9656),n=r(7249);class c extends s.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,r=e instanceof HTMLElement&&e.offsetWidth||0,a=this.props.sizeRef.current;a.height=t.offsetHeight||0,a.width=t.offsetWidth||0,a.top=t.offsetTop,a.left=t.offsetLeft,a.right=r-a.width-a.left}return null}componentDidUpdate(){}render(){return this.props.children}}function d(e){let{children:t,isPresent:r,anchorX:l}=e,o=(0,s.useId)(),i=(0,s.useRef)(null),d=(0,s.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:m}=(0,s.useContext)(n.Q);return(0,s.useInsertionEffect)(()=>{let{width:e,height:t,top:a,left:s,right:n}=d.current;if(r||!i.current||!e||!t)return;i.current.dataset.motionPopId=o;let c=document.createElement("style");return m&&(c.nonce=m),document.head.appendChild(c),c.sheet&&c.sheet.insertRule('\n          [data-motion-pop-id="'.concat(o,'"] {\n            position: absolute !important;\n            width: ').concat(e,"px !important;\n            height: ").concat(t,"px !important;\n            ").concat("left"===l?"left: ".concat(s):"right: ".concat(n),"px !important;\n            top: ").concat(a,"px !important;\n          }\n        ")),()=>{document.head.removeChild(c)}},[r]),(0,a.jsx)(c,{isPresent:r,childRef:i,sizeRef:d,children:s.cloneElement(t,{ref:i})})}let m=e=>{let{children:t,initial:r,isPresent:l,onExitComplete:n,custom:c,presenceAffectsLayout:m,mode:h,anchorX:x}=e,p=(0,o.M)(u),g=(0,s.useId)(),f=(0,s.useCallback)(e=>{for(let t of(p.set(e,!0),p.values()))if(!t)return;n&&n()},[p,n]),b=(0,s.useMemo)(()=>({id:g,initial:r,isPresent:l,custom:c,onExitComplete:f,register:e=>(p.set(e,!1),()=>p.delete(e))}),m?[Math.random(),f]:[l,f]);return(0,s.useMemo)(()=>{p.forEach((e,t)=>p.set(t,!1))},[l]),s.useEffect(()=>{l||p.size||!n||n()},[l]),"popLayout"===h&&(t=(0,a.jsx)(d,{isPresent:l,anchorX:x,children:t})),(0,a.jsx)(i.t.Provider,{value:b,children:t})};function u(){return new Map}var h=r(5087);let x=e=>e.key||"";function p(e){let t=[];return s.Children.forEach(e,e=>{(0,s.isValidElement)(e)&&t.push(e)}),t}var g=r(5403);let f=e=>{let{children:t,custom:r,initial:i=!0,onExitComplete:n,presenceAffectsLayout:c=!0,mode:d="sync",propagate:u=!1,anchorX:f="left"}=e,[b,v]=(0,h.xQ)(u),w=(0,s.useMemo)(()=>p(t),[t]),j=u&&!b?[]:w.map(x),y=(0,s.useRef)(!0),N=(0,s.useRef)(w),k=(0,o.M)(()=>new Map),[z,C]=(0,s.useState)(w),[R,M]=(0,s.useState)(w);(0,g.E)(()=>{y.current=!1,N.current=w;for(let e=0;e<R.length;e++){let t=x(R[e]);j.includes(t)?k.delete(t):!0!==k.get(t)&&k.set(t,!1)}},[R,j.length,j.join("-")]);let L=[];if(w!==z){let e=[...w];for(let t=0;t<R.length;t++){let r=R[t],a=x(r);j.includes(a)||(e.splice(t,0,r),L.push(r))}return"wait"===d&&L.length&&(e=L),M(p(e)),C(w),null}let{forceRender:B}=(0,s.useContext)(l.L);return(0,a.jsx)(a.Fragment,{children:R.map(e=>{let t=x(e),s=(!u||!!b)&&(w===R||j.includes(t));return(0,a.jsx)(m,{isPresent:s,initial:(!y.current||!!i)&&void 0,custom:r,presenceAffectsLayout:c,mode:d,onExitComplete:s?void 0:()=>{if(!k.has(t))return;k.set(t,!0);let e=!0;k.forEach(t=>{t||(e=!1)}),e&&(null==B||B(),M(N.current),u&&(null==v||v()),n&&n())},anchorX:f,children:e},t)})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[478,468,441,517,358],()=>t(2204)),_N_E=e.O()}]);