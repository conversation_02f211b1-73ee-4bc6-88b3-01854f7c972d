# Enable the rewrite engine
RewriteEngine On

# Rewrite for API requests
RewriteRule ^api/send-email$ api/send-email.php [L]

# Impostazioni PHP
<IfModule mod_php7.c>
  php_flag display_errors Off
  php_value max_execution_time 60
  php_value max_input_time 60
  php_value max_input_vars 1000
  php_value memory_limit 256M
  php_value post_max_size 32M
  php_value upload_max_filesize 24M
</IfModule>

# Sicurezza
<IfModule mod_headers.c>
  Header set X-Content-Type-Options "nosniff"
  Header set X-XSS-Protection "1; mode=block"
  Header set X-Frame-Options "SAMEORIGIN"
  Header set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# Rimuovi gli indici directory
Options -Indexes

# Abilita cache
<IfModule mod_expires.c>
  ExpiresActive On
  ExpiresByType image/jpg "access plus 1 year"
  ExpiresByType image/jpeg "access plus 1 year"
  ExpiresByType image/gif "access plus 1 year"
  ExpiresByType image/png "access plus 1 year"
  ExpiresByType image/svg+xml "access plus 1 year"
  ExpiresByType text/css "access plus 1 month"
  ExpiresByType application/javascript "access plus 1 month"
</IfModule>

# Compressione GZIP
<IfModule mod_deflate.c>
  AddOutputFilterByType DEFLATE text/html text/plain text/xml text/css application/javascript application/json image/svg+xml
</IfModule>
