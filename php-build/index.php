<?php
$request_uri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
$script_name = dirname($_SERVER['SCRIPT_NAME']);

// Rimuovi lo script_name dal request_uri se necessario (per installazioni in sottocartelle)
if ($script_name !== '/' && strpos($request_uri, $script_name) === 0) {
    $request_uri = substr($request_uri, strlen($script_name));
}

// Standardizza il percorso
$request_uri = rtrim($request_uri, '/');
if (empty($request_uri)) {
    $request_uri = '/';
}

// Controlla se è una richiesta API
if (strpos($request_uri, '/api/') === 0) {
    // Lascia che il server gestisca le richieste API
    return false;
}

// Controlla se il file esiste direttamente (per file statici)
$file_path = __DIR__ . $request_uri;
if (file_exists($file_path) && is_file($file_path)) {
    // Servi il file direttamente
    return false;
}

// Gestione delle pagine
if ($request_uri === '/') {
    // Homepage - trova il file index.html nella cartella principale
    $file_path = __DIR__ . '/index.html';
    readfile($file_path);
    exit;
}

// Gestisci le pagine principali
$lang_path = explode('/', ltrim($request_uri, '/'));
$lang = $lang_path[0];

// Se il percorso è solo /{lang}, servi la homepage di quella lingua
if (count($lang_path) === 1) {
    $file_path = __DIR__ . '/' . $lang . '/index.html';
    if (file_exists($file_path)) {
        readfile($file_path);
        exit;
    }
}

// Per le pagine specifiche, controlla se esiste un file index.html corrispondente
if (count($lang_path) > 1) {
    $page = implode('/', array_slice($lang_path, 1));
    $file_path = __DIR__ . '/' . $lang . '/' . $page . '/index.html';
    
    if (file_exists($file_path)) {
        readfile($file_path);
        exit;
    }
}

// Se non trova nulla, reindirizza alla homepage
header('Location: /');
exit;
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Error 404 - Page Not Found</title>
    <style>
        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
        h1 { color: #e53e3e; }
        a { color: #3182ce; text-decoration: none; }
        a:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <h1>404 - Pagina Non Trovata</h1>
    <p>La pagina richiesta non esiste.</p>
    <p><a href="/">Torna alla Homepage</a></p>
</body>
</html>