{"name": "temp-inparo", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "build-php": "ESLINT_NO_DEV_ERRORS=true NEXT_DISABLE_ESLINT=1 next build", "start": "next start", "lint": "next lint", "prepare-php": "node scripts/prepare-php-hosting.js"}, "dependencies": {"@headlessui/react": "^2.2.0", "@heroicons/react": "^2.2.0", "@types/nodemailer": "^6.4.17", "framer-motion": "^12.4.7", "next": "15.1.7", "next-intl": "^3.26.5", "nodemailer": "^6.10.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "sharp": "^0.33.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.7", "eslint-plugin-react-hooks": "^5.2.0", "postcss": "^8", "rimraf": "^6.0.1", "tailwindcss": "^3.4.1", "typescript": "^5"}}