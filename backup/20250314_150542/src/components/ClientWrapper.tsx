'use client';

import { usePathname } from 'next/navigation';
import { LanguageProvider } from "../app/providers";

interface ClientWrapperProps {
  children: React.ReactNode;
  initialLocale: string;
}

const locales = ['it', 'de', 'fr'];

export function ClientWrapper({ children, initialLocale }: ClientWrapperProps) {
  // Verifichiamo che la lingua sia valida
  const locale = locales.includes(initialLocale) ? initialLocale : 'it';

  return (
    <LanguageProvider initialLocale={locale}>
      {children}
    </LanguageProvider>
  );
}
