'use client';

import { <PERSON>aPhone, FaWhatsapp, FaClock, FaShieldAlt, FaTools, FaWater, FaWrench, FaBuilding } from "react-icons/fa";
import Link from "next/link";
import Image from "next/image";
import { Fade } from "@/components/animations/Fade";
import { EmergencyContactForm } from "@/components/forms/EmergencyContactForm";
import { Translations } from "@/types/translations";
import { useState, useEffect } from "react";

interface EmergencyClientProps {
  data: any;
  t: Translations;
  lang: string;
}

export function EmergencyClient({ data, t, lang }: EmergencyClientProps) {
  // Preveniamo errori di idratazione con un hook di mounting
  const [isMounted, setIsMounted] = useState(false);
  
  useEffect(() => {
    setIsMounted(true);
  }, []);
  
  // Se il componente non è ancora montato, rendiamo un contenitore vuoto
  // con la stessa struttura di base per evitare errori di idratazione
  if (!isMounted) {
    return <main className="min-h-screen bg-white">
      <section className="relative h-[90vh] min-h-[600px] overflow-hidden bg-gradient-to-br from-blue-700 via-blue-600 to-blue-500 pt-28 flex items-center"></section>
    </main>;
  }
  
  return (
    <main className="min-h-screen bg-white">
      {/* Hero Section - Design moderno con gradienti */}
      <section className="relative h-[90vh] min-h-[600px] overflow-hidden bg-gradient-to-br from-blue-700 via-blue-600 to-blue-500 pt-28 flex items-center">
        {/* Pattern di sfondo */}
        <div className="absolute inset-0 overflow-hidden opacity-10">
          <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI2MCIgaGVpZ2h0PSI2MCI+CjxyZWN0IHdpZHRoPSI2MCIgaGVpZ2h0PSI2MCIgZmlsbD0ibm9uZSI+PC9yZWN0Pgo8cGF0aCBkPSJNMzAgMEMxMy40IDAgMCAxMy40IDAgMzBzMTMuNCAzMCAzMCAzMCAzMC0xMy40IDMwLTMwUzQ2LjYgMCAzMCAwem0wIDUyLjVjLTEyLjQgMC0yMi41LTEwLjEtMjIuNS0yMi41UzE3LjYgNy41IDMwIDcuNXMyMi41IDEwLjEgMjIuNSAyMi41LTEwLjEgMjIuNS0yMi41IDIyLjV6IiBmaWxsPSIjZmZmIj48L3BhdGg+Cjwvc3ZnPg==')]"></div>
        </div>
        
        {/* Elementi decorativi animati */}
        <div className="absolute top-20 left-10 w-20 h-20 rounded-full bg-white opacity-5 animate-pulse"></div>
        <div className="absolute bottom-40 right-20 w-32 h-32 rounded-full bg-blue-300 opacity-10 animate-pulse animation-delay-1000"></div>
        <div className="absolute top-1/3 right-1/4 w-24 h-24 rounded-full bg-blue-400 opacity-5 animate-pulse animation-delay-2000"></div>
        
        {/* Elemento di design - forma geometrica */}
        <div className="absolute -bottom-20 -left-20 w-80 h-80 rounded-full border-8 border-white opacity-5 transform rotate-45"></div>
        <div className="absolute -top-10 -right-10 w-60 h-60 border-4 border-white opacity-5"></div>
        
        {/* Linea decorativa */}
        <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-white to-transparent opacity-20"></div>
        
        <div className="relative mx-auto max-w-7xl px-6 lg:px-8 h-full flex flex-col justify-center z-10">
          <div className="mx-auto max-w-3xl text-center">
            <Fade>
              <div className="mb-6 inline-flex items-center justify-center rounded-full bg-blue-900 bg-opacity-20 px-4 py-1 text-sm font-medium text-white ring-1 ring-inset ring-white/20">
                <span className="mr-2 h-2 w-2 rounded-full bg-blue-400 animate-pulse"></span>
                {t.common?.emergency || 'Notfall'} 24/7
              </div>
              <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-manrope font-bold tracking-tight text-white drop-shadow-md px-2 md:px-4 lg:px-6 break-words hyphens-auto max-w-full mb-2">
                {data.hero.title}
              </h1>
              <div className="h-1 w-24 mx-auto bg-white opacity-50 my-4 rounded-full"></div>
              <p className="mt-6 text-base md:text-lg font-sans leading-relaxed text-white text-opacity-90 px-4 sm:px-0">
                {data.hero.subtitle}
              </p>
            </Fade>
            <Fade>
              <div className="mt-8 flex flex-col sm:flex-row items-center justify-center gap-4 sm:gap-x-6">
                <Link
                  href={`tel:${data.cta.emergency_number}`}
                  className="w-full sm:w-auto rounded-xl bg-white px-8 py-4 text-xl font-semibold text-blue-700 shadow-lg hover:bg-blue-50 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600 flex items-center justify-center gap-2 transition-all duration-300 hover:scale-105 relative overflow-hidden group"
                >
                  <span className="absolute inset-0 bg-blue-100 opacity-0 group-hover:opacity-20 transition-opacity duration-300"></span>
                  <FaPhone className="h-5 w-5" />
                  <span>{data.cta.emergency_number}</span>
                </Link>
                <Link
                  href={`https://wa.me/${data.cta.whatsapp_number.replace(/\s+/g, '')}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-full sm:w-auto rounded-xl bg-white bg-opacity-10 border border-white border-opacity-20 backdrop-blur-sm px-8 py-4 text-xl font-semibold text-white shadow-lg hover:bg-opacity-20 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-white flex items-center justify-center gap-2 transition-all duration-300 hover:scale-105"
                >
                  <FaWhatsapp className="h-5 w-5" />
                  <span>{data.whatsapp || "WhatsApp"}</span>
                </Link>
              </div>
            </Fade>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-24 sm:py-32">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <Fade>
            <div className="mx-auto max-w-2xl text-center">
              <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                {data.features.title}
              </h2>
            </div>
          </Fade>
          <div className="mx-auto mt-16 max-w-7xl">
            <dl className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
              <Fade>
                <div className="rounded-xl bg-white p-8 shadow-lg ring-1 ring-gray-200 transition-all duration-300 hover:shadow-xl">
                  <dt className="flex items-center gap-x-3">
                    <FaClock className="h-6 w-6 text-blue-600" />
                    <span className="text-lg font-semibold leading-7 text-gray-900">
                      {data.features.feature1.title}
                    </span>
                  </dt>
                  <dd className="mt-4 text-base leading-7 text-gray-600">
                    {data.features.feature1.description}
                  </dd>
                </div>
              </Fade>
              <Fade>
                <div className="rounded-xl bg-white p-8 shadow-lg ring-1 ring-gray-200 transition-all duration-300 hover:shadow-xl">
                  <dt className="flex items-center gap-x-3">
                    <FaShieldAlt className="h-6 w-6 text-blue-600" />
                    <span className="text-lg font-semibold leading-7 text-gray-900">
                      {data.features.feature2.title}
                    </span>
                  </dt>
                  <dd className="mt-4 text-base leading-7 text-gray-600">
                    {data.features.feature2.description}
                  </dd>
                </div>
              </Fade>
              <Fade>
                <div className="rounded-xl bg-white p-8 shadow-lg ring-1 ring-gray-200 transition-all duration-300 hover:shadow-xl">
                  <dt className="flex items-center gap-x-3">
                    <FaTools className="h-6 w-6 text-blue-600" />
                    <span className="text-lg font-semibold leading-7 text-gray-900">
                      {data.features.feature3.title}
                    </span>
                  </dt>
                  <dd className="mt-4 text-base leading-7 text-gray-600">
                    {data.features.feature3.description}
                  </dd>
                </div>
              </Fade>
            </dl>
          </div>
        </div>
      </section>

      {/* Services Section - Design Moderno */}
      <section className="bg-gradient-to-b from-white to-gray-50 py-24 sm:py-32">
        <div className="mx-auto max-w-7xl px-6 lg:px-8 relative">
          {/* Elementi decorativi */}
          <div className="absolute -top-10 right-10 w-32 h-32 bg-blue-50 rounded-full opacity-70 blur-3xl"></div>
          <div className="absolute -bottom-10 left-10 w-40 h-40 bg-blue-50 rounded-full opacity-70 blur-3xl"></div>
          
          <Fade>
            <div className="mx-auto max-w-2xl text-center mb-16">

              <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                {lang === 'fr' ? (t.emergency?.services?.title || 'Nos Services d\'Urgence') : 
                   lang === 'de' ? (t.emergency?.services?.title || 'Unsere Notdienste') : 
                   (t.emergency?.services?.title || 'I Nostri Servizi di Emergenza')}
              </h2>
              <div className="h-1 w-24 mx-auto bg-blue-500 opacity-70 my-6 rounded-full"></div>
            </div>
          </Fade>

          <div className="mt-10 grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
            {data.services.map((service: any) => (
              <Fade key={service.id}>
                <div className="relative group overflow-hidden rounded-2xl bg-white ring-1 ring-gray-200 transition-all duration-300 hover:shadow-xl">
                  {/* Elementi decorativi */}
                  <div className="absolute -top-4 -right-4 w-16 h-16 bg-blue-100 rounded-full opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
                  
                  <div className="p-8">
                    <div className="flex items-center">
                      <div className="flex h-12 w-12 items-center justify-center rounded-full bg-blue-50 mr-4">
                        {service.id === 1 && <FaWater className="h-6 w-6 text-blue-600" />}
                        {service.id === 2 && <FaWrench className="h-6 w-6 text-blue-600" />}
                        {service.id === 3 && <FaBuilding className="h-6 w-6 text-blue-600" />}
                      </div>
                      <h3 className="text-xl font-semibold text-gray-900">{service.title}</h3>
                    </div>
                    
                    <p className="mt-4 text-sm text-gray-600">{service.description}</p>

                  </div>
                </div>
              </Fade>
            ))}
          </div>
        </div>
      </section>

      {/* Trotec Products Section */}
      <section className="py-24 sm:py-32 bg-gradient-to-br from-white via-gray-50 to-blue-50">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 relative">
          {/* Elementi decorativi */}
          <div className="absolute -top-20 right-0 w-72 h-72 bg-blue-100 rounded-full opacity-70 blur-3xl"></div>
          <div className="absolute -bottom-20 -left-20 w-96 h-96 bg-blue-100 rounded-full opacity-60 blur-3xl"></div>
          <div className="absolute top-1/2 left-1/3 w-48 h-48 bg-yellow-50 rounded-full opacity-40 blur-2xl"></div>
          
          <Fade>
            <div className="mx-auto max-w-2xl text-center mb-12 sm:mb-16">
              <div className="inline-block bg-blue-50 px-6 py-2 rounded-full mb-3">
                <span className="text-sm font-semibold text-blue-700">{data.trotec?.subtitle}</span>
              </div>
              <h2 className="text-3xl sm:text-4xl font-bold tracking-tight text-gray-900 lg:text-5xl relative inline-block">
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-700 to-blue-900">{data.trotec.title}</span>
                <div className="absolute -bottom-3 left-0 right-0 h-2 bg-gradient-to-r from-blue-600 to-blue-500 rounded-full"></div>
              </h2>
              <p className="mt-6 text-base sm:text-lg leading-7 sm:leading-8 text-gray-700 max-w-xl mx-auto">
                {data.trotec.description}
              </p>
            </div>
          </Fade>

          <div className="mt-12 sm:mt-16 space-y-12 sm:space-y-20">
            {data.trotec.products.map((product: any, index: number) => (
              <Fade key={product.id}>
                <div className="relative flex flex-col lg:flex-row items-center gap-4 sm:gap-6 bg-white rounded-2xl p-4 sm:p-6 shadow-lg hover:shadow-xl transition-all duration-300 border-b-3 border-blue-500" 
                     style={{flexDirection: index % 2 === 0 ? 'row' : 'row-reverse'}}>
                  
                  {/* Sfondo decorativo */}
                  <div className="absolute top-0 right-0 -mt-6 -mr-6 w-24 h-24 bg-gradient-to-br from-blue-200 to-blue-200 rounded-full opacity-70 blur-xl"></div>
                  <div className="absolute bottom-0 left-0 -mb-3 -ml-3 w-16 h-16 bg-gradient-to-br from-yellow-100 to-blue-100 rounded-full opacity-60 blur-xl"></div>
                  
                  <div className="w-full lg:w-1/2 z-10 mb-6 lg:mb-0">
                    <div className="flex items-center mb-3">
                      <span className="flex items-center justify-center w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-br from-blue-700 to-blue-500 text-white text-base sm:text-lg font-bold rounded-lg mr-3 shadow-md">{product.id}</span>
                      <h3 className="text-xl sm:text-2xl font-bold text-gray-900">{product.title}</h3>
                    </div>
                    <p className="text-gray-700 text-sm sm:text-base mb-4 sm:mb-5 leading-relaxed">{product.description}</p>
                    <div className="w-24 h-1 bg-gradient-to-r from-blue-500 to-blue-400 rounded-full"></div>
                  </div>
                  
                  <div className="w-full lg:w-1/2 bg-gradient-to-br from-gray-50 to-blue-50 rounded-xl p-4 sm:p-5 shadow-md border border-blue-100">
                    {product.id === 1 && (
                      <div className="flex flex-col items-center">
                        <div className="w-20 h-20 sm:w-28 sm:h-28 bg-gradient-to-br from-blue-100 to-blue-300 rounded-full flex items-center justify-center mb-4 shadow-md transform hover:scale-105 transition-transform duration-300">
                          <FaWater className="w-10 h-10 sm:w-14 sm:h-14 text-blue-700" />
                        </div>
                        <div className="text-center">
                          <div className="font-bold text-gray-900 text-lg sm:text-xl mb-3">TWP 11025 E</div>
                          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-3">
                            <div className="bg-white p-2 sm:p-3 rounded-lg shadow-sm border-l-3 border-blue-500 transform hover:-translate-y-1 transition-transform duration-300">
                              <div className="font-semibold text-blue-800 text-sm">Portata</div>
                              <div className="text-gray-900">15.000 l/h</div>
                            </div>
                            <div className="bg-white p-2 sm:p-3 rounded-lg shadow-sm border-l-3 border-blue-500 transform hover:-translate-y-1 transition-transform duration-300">
                              <div className="font-semibold text-blue-800 text-sm">Potenza</div>
                              <div className="text-gray-900">1.100 W</div>
                            </div>
                            <div className="bg-white p-2 sm:p-3 rounded-lg shadow-sm border-l-3 border-blue-500 transform hover:-translate-y-1 transition-transform duration-300">
                              <div className="font-semibold text-blue-800 text-sm">Profondità</div>
                              <div className="text-gray-900">7 m</div>
                            </div>
                            <div className="bg-white p-2 sm:p-3 rounded-lg shadow-sm border-l-3 border-blue-500 transform hover:-translate-y-1 transition-transform duration-300">
                              <div className="font-semibold text-blue-800 text-sm">Mandata</div>
                              <div className="text-gray-900">11 m</div>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                    
                    {product.id === 2 && (
                      <div className="flex flex-col items-center">
                        <div className="w-20 h-20 sm:w-28 sm:h-28 bg-gradient-to-br from-blue-100 to-blue-300 rounded-full flex items-center justify-center mb-4 shadow-md transform hover:scale-105 transition-transform duration-300">
                          <FaWrench className="w-10 h-10 sm:w-14 sm:h-14 text-blue-700" />
                        </div>
                        <div className="text-center w-full">
                          <div className="font-bold text-gray-900 text-lg sm:text-xl mb-3">MultiQube System</div>
                          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-3">
                            <div className="bg-white p-2 sm:p-3 rounded-lg shadow-sm border-l-3 border-blue-500 flex items-center transform hover:-translate-x-1 transition-transform duration-300">
                              <div className="w-2 h-2 rounded-full bg-blue-600 mr-2 flex-shrink-0"></div>
                              <div className="text-sm text-gray-800">{lang === 'it' ? 'Asciugatura rapida ed efficiente' : lang === 'fr' ? 'Séchage rapide et efficace' : 'Schnelle und effiziente Trocknung'}</div>
                            </div>
                            <div className="bg-white p-2 sm:p-3 rounded-lg shadow-sm border-l-3 border-blue-500 flex items-center transform hover:-translate-x-1 transition-transform duration-300">
                              <div className="w-2 h-2 rounded-full bg-blue-600 mr-2 flex-shrink-0"></div>
                              <div className="text-sm text-gray-800">{lang === 'it' ? 'Prevenzione muffa e danni strutturali' : lang === 'fr' ? 'Prévention des moisissures et des dommages structurels' : 'Schimmel- und Strukturschadenprävention'}</div>
                            </div>
                            <div className="bg-white p-2 sm:p-3 rounded-lg shadow-sm border-l-3 border-blue-500 flex items-center transform hover:-translate-x-1 transition-transform duration-300">
                              <div className="w-2 h-2 rounded-full bg-blue-600 mr-2 flex-shrink-0"></div>
                              <div className="text-sm text-gray-800">{lang === 'it' ? 'Tecnologia avanzata di deumidificazione' : lang === 'fr' ? 'Technologie avancée de déshumidification' : 'Fortschrittliche Entfeuchtungstechnologie'}</div>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                    
                    {product.id === 3 && (
                      <div className="flex flex-col items-center">
                        <div className="w-20 h-20 sm:w-28 sm:h-28 bg-gradient-to-br from-blue-100 to-blue-300 rounded-full flex items-center justify-center mb-4 shadow-md transform hover:scale-105 transition-transform duration-300">
                          <FaBuilding className="w-10 h-10 sm:w-14 sm:h-14 text-blue-700" />
                        </div>
                        <div className="text-center w-full">
                          <div className="font-bold text-gray-900 text-lg sm:text-xl mb-3">{lang === 'it' ? 'Coloranti di marcatura' : lang === 'fr' ? 'Colorants de marquage' : 'Markierungsfarbstoffe'}</div>
                          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-3">
                            <div className="bg-white p-2 sm:p-3 rounded-lg shadow-sm border-l-3 border-blue-500 flex items-center transform hover:-translate-x-1 transition-transform duration-300">
                              <div className="w-2 h-2 rounded-full bg-blue-600 mr-2 flex-shrink-0"></div>
                              <div className="text-sm text-gray-800">{lang === 'it' ? 'Localizzazione precisa delle perdite' : lang === 'fr' ? 'Localisation précise des fuites' : 'Präzise Leckortung'}</div>
                            </div>
                            <div className="bg-white p-2 sm:p-3 rounded-lg shadow-sm border-l-3 border-blue-500 flex items-center transform hover:-translate-x-1 transition-transform duration-300">
                              <div className="w-2 h-2 rounded-full bg-blue-600 mr-2 flex-shrink-0"></div>
                              <div className="text-sm text-gray-800">{lang === 'it' ? 'Identificazione rapida delle infiltrazioni' : lang === 'fr' ? 'Identification rapide des infiltrations' : 'Schnelle Identifizierung von Undichtigkeiten'}</div>
                            </div>
                            <div className="bg-white p-2 sm:p-3 rounded-lg shadow-sm border-l-3 border-blue-500 flex items-center transform hover:-translate-x-1 transition-transform duration-300">
                              <div className="w-2 h-2 rounded-full bg-blue-600 mr-2 flex-shrink-0"></div>
                              <div className="text-sm text-gray-800">{lang === 'it' ? 'Visibilità ottimale' : lang === 'fr' ? 'Visibilité optimale' : 'Optimale Sichtbarkeit'}</div>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </Fade>
            ))}
          </div>
          
          <Fade>
            <div className="mt-20 text-center">
              <p className="text-base sm:text-lg text-gray-700 mb-8 max-w-2xl mx-auto">
                {data.trotec.conclusion}
              </p>
              <div className="inline-flex">
                <Link href={`/${lang}/contact`} 
                   className="relative inline-flex items-center px-6 py-3 overflow-hidden text-white bg-gradient-to-r from-blue-600 to-blue-500 rounded-lg group gap-2 shadow-md hover:shadow-lg transition-all duration-300">
                  <span className="absolute right-0 flex items-center justify-start w-10 h-10 duration-300 transform translate-x-full group-hover:translate-x-0">
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                    </svg>
                  </span>
                  <span className="text-sm sm:text-base font-medium transition-all duration-300 group-hover:mr-4">
                    {lang === 'it' ? 'Contattaci per informazioni' : lang === 'fr' ? 'Contactez-nous pour plus d\'informations' : 'Kontaktieren Sie uns für Informationen'}
                  </span>
                </Link>
              </div>
            </div>
          </Fade>
        </div>
      </section>

      {/* Process Section */}
      <section className="py-24 sm:py-32 bg-white">
        <div className="mx-auto max-w-7xl px-6 lg:px-8 relative">
          
          <Fade>
            <div className="mx-auto max-w-2xl text-center mb-16 relative z-10">
              <h2 className="text-3xl sm:text-4xl font-bold tracking-tight text-gray-900">
                {data.process_title}
              </h2>
              <p className="mt-6 text-base leading-8 text-gray-600">
                {lang === 'it' ? 'Un processo chiaro e veloce per rispondere alle tue emergenze nel minor tempo possibile.' : 
                  lang === 'fr' ? 'Un processus clair et rapide pour répondre à vos urgences dans les plus brefs délais.' : 
                  'Ein klarer und schneller Prozess, um auf Ihre Notfälle in kürzester Zeit zu reagieren.'}
              </p>
            </div>
          </Fade>

          <div className="mx-auto mt-16 max-w-3xl">
            <ol className="relative border-l border-blue-200 space-y-16 mx-auto">
              {data.process.map((step: any) => (
                <Fade key={step.step}>
                  <li className="ml-8">
                    <div className="absolute -left-6 flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-blue-600 to-blue-500 shadow-md">
                      <span className="text-xl font-semibold text-white">{step.step}</span>
                    </div>
                    <h3 className="text-xl font-semibold text-gray-900">{step.title}</h3>
                    <p className="mt-2 text-sm leading-6 text-gray-600">{step.description}</p>
                  </li>
                </Fade>
              ))}
            </ol>
          </div>
        </div>
      </section>

      {/* Contact Form */}
      <section className="bg-gray-50 py-24 sm:py-32">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="grid grid-cols-1 gap-16 lg:grid-cols-2 items-center">
            <Fade>
              <div>
                <div className="mb-8">
                  <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                    {data.contact_form.title}
                  </h2>
                  <p className="mt-4 text-base leading-8 text-gray-600">
                    {data.contact_form.description}
                  </p>
                </div>
                
                <div className="rounded-xl bg-gradient-to-r from-blue-600 to-blue-700 text-white p-8 shadow-lg">
                  <h3 className="text-xl font-semibold mb-4">{lang === 'it' ? 'Chiama Subito' : lang === 'fr' ? 'Appelez Maintenant' : 'Rufen Sie Sofort An'}</h3>
                  <p className="mb-6">{lang === 'it' ? 'Per emergenze immediate, contattaci telefonicamente per ricevere assistenza immediata.' : 
                    lang === 'fr' ? 'Pour les urgences immédiates, contactez-nous par téléphone pour une assistance immédiate.' : 
                    'Für sofortige Notfälle, kontaktieren Sie uns telefonisch, um sofortige Hilfe zu erhalten.'}</p>
                  <Link 
                    href={`tel:${data.cta.emergency_number}`}
                    className="inline-flex items-center text-lg font-semibold hover:underline"
                  >
                    <FaPhone className="mr-2" />
                    {data.cta.emergency_number}
                  </Link>
                </div>
              </div>
            </Fade>
            
            <Fade>
              <div>
                <EmergencyContactForm t={t} lang={lang} />
              </div>
            </Fade>
          </div>
        </div>
      </section>
    </main>
  );
}
