'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import { FaBars, FaTimes } from 'react-icons/fa';
import LanguageSelector from '../shared/LanguageSelector';
import { Translations } from '@/types/translations';

interface NavigationProps {
  lang: string;
  translations: Translations;
}

export default function Navigation({ lang, translations }: NavigationProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMounted, setIsMounted] = useState(false);
  const pathname = usePathname();

  useEffect(() => {
    setIsMounted(true);
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 0);
    };
    window.addEventListener('scroll', handleScroll);

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  if (!isMounted) {
    return null;
  }

  // Determina la pagina attiva dal pathname
  const isHomePage = pathname === `/${lang}` || pathname === '/';
  const isServicesPage = pathname.includes('/services');
  const isContactPage = pathname.includes('/contact');
  const isEmergencyPage = pathname.includes('/emergency');

  return (
    <nav className={`fixed w-full z-50 transition-all duration-300 ${isScrolled ? 'bg-white/95 backdrop-blur-sm shadow-lg' : 'bg-white/80'}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex">
            <Link href={`/${lang}`} className="flex-shrink-0 flex items-center">
              <span className="text-xl font-bold text-gray-900">Inparo</span>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex md:items-center md:space-x-8">
            <Link
              href={`/${lang}`}
              className={`text-gray-900 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium ${isHomePage ? 'border-b-2 border-blue-600 font-medium' : ''}`}
            >
              {translations.nav.home}
            </Link>
            <Link
              href={`/${lang}/services`}
              className={`text-gray-900 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium ${isServicesPage ? 'border-b-2 border-blue-600 font-medium' : ''}`}
            >
              {translations.nav.services}
            </Link>
            <Link
              href={`/${lang}/contact`}
              className={`text-gray-900 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium ${isContactPage ? 'border-b-2 border-blue-600 font-medium' : ''}`}
            >
              {translations.nav.contact}
            </Link>
            <Link
              href={`/${lang}/emergency`}
              className={`${isEmergencyPage ? 'bg-red-700' : 'bg-red-600'} text-white hover:bg-red-700 px-3 py-2 rounded-md text-sm font-medium`}
            >
              {translations.nav.emergency}
            </Link>
            <LanguageSelector currentLang={lang} />
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden flex items-center">
            <button
              onClick={() => setIsOpen(!isOpen)}
              className="inline-flex items-center justify-center p-2 rounded-md text-gray-900 hover:text-gray-700 focus:outline-none"
            >
              {isOpen ? <FaTimes className="h-6 w-6" /> : <FaBars className="h-6 w-6" />}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="md:hidden bg-white"
          >
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3">
              <Link
                href={`/${lang}`}
                onClick={() => setIsOpen(false)}
                className={`block px-3 py-2 rounded-md text-base font-medium ${isHomePage ? 'border-b-2 border-blue-600 text-gray-900' : 'text-gray-900 hover:bg-gray-50'}`}
              >
                {translations.nav.home}
              </Link>
              <Link
                href={`/${lang}/services`}
                onClick={() => setIsOpen(false)}
                className={`block px-3 py-2 rounded-md text-base font-medium ${isServicesPage ? 'border-b-2 border-blue-600 text-gray-900' : 'text-gray-900 hover:bg-gray-50'}`}
              >
                {translations.nav.services}
              </Link>
              <Link
                href={`/${lang}/contact`}
                onClick={() => setIsOpen(false)}
                className={`block px-3 py-2 rounded-md text-base font-medium ${isContactPage ? 'border-b-2 border-blue-600 text-gray-900' : 'text-gray-900 hover:bg-gray-50'}`}
              >
                {translations.nav.contact}
              </Link>
              <Link
                href={`/${lang}/emergency`}
                onClick={() => setIsOpen(false)}
                className={`${isEmergencyPage ? 'bg-red-700' : 'bg-red-600'} text-white hover:bg-red-700 block px-3 py-2 rounded-md text-base font-medium`}
              >
                {translations.nav.emergency}
              </Link>
              <div className="px-3 py-2">
                <LanguageSelector currentLang={lang} />
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </nav>
  );
}
