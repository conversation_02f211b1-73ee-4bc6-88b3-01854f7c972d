'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaCheck, FaTimes } from 'react-icons/fa';

import { Translations } from "@/types/translations";

interface EmergencyContactFormProps {
  t: Translations;
  lang: string;
}

export function EmergencyContactForm({ t, lang }: EmergencyContactFormProps) {
  const [formState, setFormState] = useState({
    name: '',
    phone: '',
    address: '',
    emergency_type: '',
    message: '',
    privacy: false
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!formState.name.trim()) newErrors.name = 'Nome richiesto';
    if (!formState.phone.trim()) {
      newErrors.phone = 'Telefono richiesto';
    } else if (!/^[+]?[(]?[0-9]{3}[)]?[-\s.]?[0-9]{3}[-\s.]?[0-9]{4,6}$/.test(formState.phone)) {
      newErrors.phone = 'Numero di telefono non valido';
    }
    if (!formState.address.trim()) newErrors.address = 'Indirizzo richiesto';
    if (!formState.emergency_type) newErrors.emergency_type = 'Tipo di emergenza richiesto';
    if (!formState.privacy) newErrors.privacy = 'Accetta la privacy policy';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForm()) return;

    setIsSubmitting(true);
    setSubmitStatus('idle');

    try {
      // Costruzione del contenuto dell'email
      const emergencyType = formState.emergency_type || 'Non specificato';
      const emailSubject = `EMERGENZA: ${emergencyType} - ${formState.name}`;
      
      // Chiamata API per inviare l'email
      const response = await fetch('/api/send-email.php', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: formState.name,
          email: '<EMAIL>', // Email di supporto per le emergenze
          subject: emailSubject,
          message: `RICHIESTA DI EMERGENZA

Nome: ${formState.name}
Telefono: ${formState.phone}
Indirizzo: ${formState.address}
Tipo di emergenza: ${emergencyType}
Messaggio: ${formState.message}

Inviato dal form di emergenza del sito web.`,
          isEmergency: true
        }),
      });

      if (response.ok) {
        setSubmitStatus('success');
        setFormState({
          name: '',
          phone: '',
          address: '',
          emergency_type: '',
          message: '',
          privacy: false
        });
      } else {
        throw new Error('Errore nell\'invio dell\'email');
      }
    } catch (error) {
      console.error('Errore durante l\'invio dell\'email:', error);
      setSubmitStatus('error');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="relative overflow-hidden bg-gradient-to-br from-red-50 via-white to-orange-50 p-8 rounded-2xl shadow-xl border border-red-100"
    >
      {/* Elementi decorativi */}
      <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-red-500/10 to-orange-500/10 rounded-full -mr-16 -mt-16"></div>
      <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-red-500/10 to-orange-500/10 rounded-full -ml-12 -mb-12"></div>
      
      {/* Icona di emergenza */}
      <div className="flex items-center mb-6">
        <div className="bg-gradient-to-r from-red-600 to-red-500 p-3 rounded-full shadow-md mr-4">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
          </svg>
        </div>
        <h3 className="text-2xl font-bold text-gray-900">{t.contact.emergency}</h3>
      </div>
      
      <p className="text-gray-700 mb-8 ml-16">{t.contact.description}</p>

      <form onSubmit={handleSubmit} className="space-y-6 relative z-10 mx-auto max-w-3xl">
        <div className="relative group">
          <div className="absolute -inset-0.5 bg-gradient-to-r from-red-200 to-orange-200 rounded-lg blur opacity-30 group-hover:opacity-50 transition duration-300"></div>
          <div className="relative bg-white p-5 rounded-lg shadow-sm hover:shadow transition">
            <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1 flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
              {t.contact.form.name}
            </label>
            <input
              type="text"
              id="name"
              name="name"
              value={formState.name}
              onChange={(e) => setFormState(prev => ({ ...prev, name: e.target.value }))}
              className={`block w-full px-4 py-3 rounded-lg bg-gray-50 border-transparent text-gray-900 focus:border-red-500 focus:bg-white focus:ring-2 focus:ring-red-200 transition duration-200 ${
                errors.name ? 'border-red-500 ring-2 ring-red-200' : ''
              }`}
              placeholder={t.contact.form.placeholders?.name || "Es. Mario Rossi"}
              aria-invalid={errors.name ? 'true' : 'false'}
              aria-describedby={errors.name ? 'name-error' : undefined}
            />
            {errors.name && (
              <p id="name-error" className="mt-1 text-sm text-red-600 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
                {errors.name}
              </p>
            )}
          </div>
        </div>

        <div className="relative group">
          <div className="absolute -inset-0.5 bg-gradient-to-r from-red-200 to-orange-200 rounded-lg blur opacity-30 group-hover:opacity-50 transition duration-300"></div>
          <div className="relative bg-white p-5 rounded-lg shadow-sm hover:shadow transition">
            <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1 flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
              </svg>
              {t.contact.form.phone}
            </label>
            <input
              type="tel"
              id="phone"
              name="phone"
              value={formState.phone}
              onChange={(e) => setFormState(prev => ({ ...prev, phone: e.target.value }))}
              className={`block w-full px-4 py-3 rounded-lg bg-gray-50 border-transparent text-gray-900 focus:border-red-500 focus:bg-white focus:ring-2 focus:ring-red-200 transition duration-200 ${
                errors.phone ? 'border-red-500 ring-2 ring-red-200' : ''
              }`}
              placeholder={t.contact.form.placeholders?.phone || "+41 XX XXX XX XX"}
              aria-invalid={errors.phone ? 'true' : 'false'}
              aria-describedby={errors.phone ? 'phone-error' : undefined}
            />
            {errors.phone && (
              <p id="phone-error" className="mt-1 text-sm text-red-600 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
                {errors.phone}
              </p>
            )}
          </div>
        </div>

        <div className="relative group">
          <div className="absolute -inset-0.5 bg-gradient-to-r from-red-200 to-orange-200 rounded-lg blur opacity-30 group-hover:opacity-50 transition duration-300"></div>
          <div className="relative bg-white p-5 rounded-lg shadow-sm hover:shadow transition">
            <label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-1 flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
              {t.contact.form.address || 'Indirizzo'}
            </label>
            <input
              type="text"
              id="address"
              name="address"
              value={formState.address}
              onChange={(e) => setFormState(prev => ({ ...prev, address: e.target.value }))}
              className={`block w-full px-4 py-3 rounded-lg bg-gray-50 border-transparent text-gray-900 focus:border-red-500 focus:bg-white focus:ring-2 focus:ring-red-200 transition duration-200 ${
                errors.address ? 'border-red-500 ring-2 ring-red-200' : ''
              }`}
              placeholder={t.contact.form.placeholders?.address || "Via, numero civico, cap, città"}
              aria-invalid={errors.address ? 'true' : 'false'}
              aria-describedby={errors.address ? 'address-error' : undefined}
            />
            {errors.address && (
              <p id="address-error" className="mt-1 text-sm text-red-600 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
                {errors.address}
              </p>
            )}
          </div>
        </div>

        <div className="relative group">
          <div className="absolute -inset-0.5 bg-gradient-to-r from-red-200 to-orange-200 rounded-lg blur opacity-30 group-hover:opacity-50 transition duration-300"></div>
          <div className="relative bg-white p-5 rounded-lg shadow-sm hover:shadow transition">
            <label htmlFor="emergency_type" className="block text-sm font-medium text-gray-700 mb-1 flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              {t.contact.form.emergency_type || 'Tipo di emergenza'}
            </label>
            <select
              id="emergency_type"
              name="emergency_type"
              value={formState.emergency_type}
              onChange={(e) => setFormState(prev => ({ ...prev, emergency_type: e.target.value }))}
              className={`block w-full px-4 py-3 rounded-lg bg-gray-50 border-transparent text-gray-900 focus:border-red-500 focus:bg-white focus:ring-2 focus:ring-red-200 transition duration-200 ${
                errors.emergency_type ? 'border-red-500 ring-2 ring-red-200' : ''
              }`}
              aria-invalid={errors.emergency_type ? 'true' : 'false'}
              aria-describedby={errors.emergency_type ? 'emergency-type-error' : undefined}
            >
              <option value="">{lang === 'it' ? 'Seleziona il tipo di emergenza' : lang === 'fr' ? 'Sélectionnez le type d\'urgence' : 'Wählen Sie den Notfalltyp'}</option>
              {lang === 'it' ? (
                // Opzioni in italiano
                ['Allagamento', 'Perdita d\'acqua', 'Tubature rotte', 'Altro'].map((type) => (
                  <option key={type} value={type}>{type}</option>
                ))
              ) : lang === 'fr' ? (
                // Opzioni in francese
                ['Inondation', 'Fuite d\'eau', 'Tuyaux cassés', 'Autre'].map((type) => (
                  <option key={type} value={type}>{type}</option>
                ))
              ) : (
                // Opzioni in tedesco
                ['Überschwemmung', 'Wasserleck', 'Gebrochene Rohre', 'Andere'].map((type) => (
                  <option key={type} value={type}>{type}</option>
                ))
              )}
            </select>
            {errors.emergency_type && (
              <p id="emergency-type-error" className="mt-1 text-sm text-red-600 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
                {errors.emergency_type}
              </p>
            )}
          </div>
        </div>

        <div className="relative group">
          <div className="absolute -inset-0.5 bg-gradient-to-r from-red-200 to-orange-200 rounded-lg blur opacity-30 group-hover:opacity-50 transition duration-300"></div>
          <div className="relative bg-white p-5 rounded-lg shadow-sm hover:shadow transition">
            <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-1 flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
              </svg>
              {t.contact.form.message}
            </label>
            <textarea
              id="message"
              name="message"
              rows={4}
              value={formState.message}
              onChange={(e) => setFormState(prev => ({ ...prev, message: e.target.value }))}
              className="block w-full px-4 py-3 rounded-lg bg-gray-50 border-transparent text-gray-900 focus:border-red-500 focus:bg-white focus:ring-2 focus:ring-red-200 transition duration-200"
              placeholder={t.contact.form.placeholders?.message || "Scrivi qui il tuo messaggio..."}
            />
          </div>
        </div>

        <div className="relative group">
          <div className="absolute -inset-0.5 bg-gradient-to-r from-red-200 to-orange-200 rounded-lg blur opacity-30 group-hover:opacity-50 transition duration-300"></div>
          <div className="relative bg-white p-5 rounded-lg shadow-sm hover:shadow transition">
            <div className="flex items-start">
              <div className="flex items-center h-5">
                <input
                  id="privacy"
                  name="privacy"
                  type="checkbox"
                  checked={formState.privacy}
                  onChange={(e) => setFormState(prev => ({ ...prev, privacy: e.target.checked }))}
                  className={`h-5 w-5 rounded border-transparent bg-gray-100 text-red-600 focus:ring-red-400 transition-colors ${
                    errors.privacy ? 'border-red-500 ring-2 ring-red-200' : ''
                  }`}
                  aria-invalid={errors.privacy ? 'true' : 'false'}
                  aria-describedby={errors.privacy ? 'privacy-error' : undefined}
                />
              </div>
              <div className="ml-3">
                <label htmlFor="privacy" className="text-sm font-medium text-gray-700 flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                  </svg>
                  {lang === 'it' ? 'Accetto la privacy policy' : 
                   lang === 'fr' ? 'J\'accepte la politique de confidentialité' : 
                   'Ich akzeptiere die Datenschutzrichtlinie'}
                </label>
                {errors.privacy && (
                  <p id="privacy-error" className="mt-1 text-sm text-red-600 flex items-center ml-6">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                    </svg>
                    {errors.privacy}
                  </p>
                )}
              </div>
            </div>
          </div>
        </div>

        <div className="relative group mt-8">
          <div className="absolute -inset-1 bg-gradient-to-r from-red-600 to-orange-500 rounded-lg blur opacity-40 group-hover:opacity-75 transition duration-300 animate-pulse" style={{ zIndex: 0 }}></div>
          <motion.button
            type="submit"
            disabled={isSubmitting}
            className={`relative w-full flex justify-center items-center py-4 px-6 border border-transparent rounded-lg text-base font-bold text-white bg-gradient-to-r from-red-600 to-red-500 shadow-lg hover:from-red-500 hover:to-red-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:from-gray-400 disabled:to-gray-300 disabled:cursor-not-allowed transition-all duration-300 hover:shadow-xl z-10`}
            whileHover={{ translateY: -2 }}
            whileTap={{ scale: 0.98 }}
          >
            {isSubmitting ? (
              <div className="flex items-center">
                <motion.div
                  className="h-5 w-5 mr-3 border-2 border-white border-t-transparent rounded-full"
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                />
                <span>Invio in corso...</span>
              </div>
            ) : (
              <div className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 14l5-5-5-5" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9H5" />
                </svg>
                {t.contact.form.submit || 'Invia richiesta'}
              </div>
            )}
          </motion.button>
        </div>
      </form>

      <AnimatePresence>
        {submitStatus !== 'idle' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ type: 'spring', damping: 20 }}
            className={`mt-8 p-6 rounded-xl shadow-lg border ${
              submitStatus === 'success' ? 'bg-gradient-to-r from-green-50 to-green-100 border-green-200' : 'bg-gradient-to-r from-red-50 to-red-100 border-red-200'
            }`}
          >
            <div className="flex items-center">
              <div className={`flex-shrink-0 p-3 rounded-full ${
                submitStatus === 'success' ? 'bg-green-100' : 'bg-red-100'
              }`}>
                {submitStatus === 'success' ? (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                ) : (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                )}
              </div>
              <div className="ml-4">
                <h3
                  className={`text-lg font-bold ${
                    submitStatus === 'success' ? 'text-green-800' : 'text-red-800'
                  }`}
                >
                  {submitStatus === 'success' ? 'Richiesta inviata con successo!' : 'Errore durante l\'invio'}
                </h3>
                <p
                  className={`mt-1 text-sm ${
                    submitStatus === 'success' ? 'text-green-700' : 'text-red-700'
                  }`}
                >
                  {submitStatus === 'success' ? 'Ti contatteremo al più presto.' : 'Si è verificato un errore. Riprova più tardi.'}
                </p>
              </div>
            </div>
            {submitStatus === 'success' && (
              <div className="mt-4 flex justify-end">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => setSubmitStatus('idle')}
                  className="px-4 py-2 text-sm font-medium text-green-700 bg-green-100 rounded-md hover:bg-green-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                >
                  Chiudi messaggio
                </motion.button>
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
}
