// Lingue supportate dall'applicazione
export const locales = ['it', 'de', 'fr'] as const;
export type Locale = typeof locales[number];

// Lingua di default
export const defaultLocale: Locale = 'de';

// Nomi delle lingue per il selettore
export const localeNames: Record<Locale, string> = {
  it: 'Italiano',
  de: 'Deutsch',
  fr: 'Français',
} as const;

// Funzione per verificare se una stringa è una lingua valida
export function isValidLocale(locale: string): locale is Locale {
  return locales.includes(locale as Locale);
}
