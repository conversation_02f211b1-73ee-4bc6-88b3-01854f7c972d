import { Metadata } from 'next';
import { getTranslation } from '@/i18n/server';
import PrivacyPolicyClient from './PrivacyPolicyClient';

interface PrivacyPolicyPageProps {
  params: { lang: string };
}

export async function generateMetadata({ params }: PrivacyPolicyPageProps): Promise<Metadata> {
  return {
    title: `Privacy Policy | Inparo`,
    description: 'Informativa sulla privacy di Inparo GmbH conforme alla legislazione svizzera sulla protezione dei dati.'
  };
}

export default async function PrivacyPolicyPage({ params }: PrivacyPolicyPageProps) {
  const t = await getTranslation(params.lang);
  return <PrivacyPolicyClient t={t} lang={params.lang} />;
}
