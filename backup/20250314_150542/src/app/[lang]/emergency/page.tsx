import { getTranslation } from "@/i18n/server";
import { emergencyServices } from "@/mocks/emergency";
import { EmergencyClient } from "@/components/emergency/EmergencyClient";

interface EmergencyPageProps {
  params: { lang: string };
}

// Questa è ora una pagina server
export default async function EmergencyPage({ params }: EmergencyPageProps) {
  // Otteniamo il lang dal params
  const { lang } = params;
  
  // Otteniamo i dati di traduzione lato server
  const translation = await getTranslation(lang);
  
  // Otteniamo i dati dei servizi di emergenza
  const emergencyData = emergencyServices[lang as keyof typeof emergencyServices] || emergencyServices.it;

  // Renderizziamo il componente client con i dati ottenuti dal server
  return <EmergencyClient data={emergencyData} t={translation} lang={lang} />;
}
