import Image from "next/image";
import Link from "next/link";
import { getTranslation } from "@/i18n/server";
import { FaHammer, FaWater, FaBroom, FaComments, FaTools } from 'react-icons/fa';

interface HomeProps {
  params: Promise<{ lang: string }>;
}

export default async function Home({ params }: HomeProps) {
  // Attendiamo che i parametri siano disponibili
  const resolvedParams = await params;
  const t = await getTranslation(resolvedParams.lang);

  // Verifichiamo che le traduzioni siano caricate correttamente
  if (!t || !t.home || !t.home.services) {
    console.error('Missing translations:', t);
    return <div>Loading...</div>;
  }

  const sections = {
    hero: (
      <div className="relative isolate overflow-hidden bg-gradient-to-br from-gray-900 via-purple-900 to-red-900">
        {/* Elementi decorativi di sfondo */}
        <div className="absolute inset-0 -z-10 opacity-30">
          <div className="absolute top-20 right-0 w-72 h-72 bg-red-500 rounded-full filter blur-3xl opacity-20"></div>
          <div className="absolute bottom-0 left-1/4 w-72 h-72 bg-purple-500 rounded-full filter blur-3xl opacity-20"></div>
          <div className="absolute w-full h-full opacity-10" style={{ backgroundImage: 'url("/images/noise.png")', backgroundRepeat: 'repeat' }}></div>
        </div>
        
        <div className="mx-auto max-w-7xl px-6 py-24 sm:pb-32 lg:flex lg:px-8 lg:py-40 items-center justify-between">
          {/* Contenuto testuale */}
          <div className="mx-auto max-w-2xl lg:mx-0 lg:max-w-xl lg:flex-shrink-0 lg:pt-8 z-10">
            <h1 className="mt-10 text-4xl font-bold tracking-tight text-white sm:text-6xl">
              {t.home.hero.title}{" "}
              <span className="bg-gradient-to-r from-red-500 to-purple-600 bg-clip-text text-transparent">{t.home.hero.titleHighlight}</span>
            </h1>
            <p className="mt-6 text-lg leading-8 text-gray-200">
              {t.home.hero.subtitle}
            </p>
            <div className="mt-10 flex items-center gap-x-6">
              <Link
                href={`/${resolvedParams.lang}/contact`}
                className="rounded-md bg-gradient-to-r from-red-600 to-purple-700 px-5 py-3 text-sm font-semibold text-white shadow-lg hover:shadow-xl transition-all duration-300 hover:translate-y-[-2px]"
              >
                {t.common.getQuote}
              </Link>
              <Link
                href={`/${resolvedParams.lang}/emergency`}
                className="rounded-md border border-white/30 bg-white/10 backdrop-blur-sm px-5 py-3 text-sm font-semibold text-white shadow-sm hover:bg-white/20 transition-all duration-300"
              >
                {t.common.emergency}
              </Link>
            </div>
          </div>
          
          {/* Immagine hero */}
          <div className="mt-16 lg:mt-0 lg:flex-1 flex justify-center items-center relative z-10">
            <div className="relative mx-auto w-full max-w-lg lg:max-w-xl">
              {/* Sfondo luminoso decorativo */}
              <div className="absolute top-0 -left-4 w-72 h-72 bg-purple-600 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-blob"></div>
              <div className="absolute -bottom-8 right-0 w-72 h-72 bg-red-600 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-blob animation-delay-4000"></div>
              
              {/* Contenitore immagine con cornice e link */}
              <Link 
                href={`/${resolvedParams.lang}/services`} 
                className="block relative mx-auto border-8 border-white/10 rounded-2xl overflow-hidden shadow-2xl transform rotate-2 hover:rotate-0 transition-all duration-500 group"
              >
                <Image
                  src="/images/services/renovation.jpg"
                  alt="I nostri servizi di ristrutturazione"
                  width={600}
                  height={400}
                  className="w-full h-auto object-cover rounded-xl group-hover:scale-105 transition-transform duration-500"
                  priority
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent opacity-60"></div>
                <div className="absolute bottom-0 left-0 right-0 p-6">
                  <div className="text-white text-lg font-medium">
                    {resolvedParams.lang === 'it' ? 'Scopri tutti i nostri servizi →' : 
                     resolvedParams.lang === 'de' ? 'Alle Dienstleistungen entdecken →' : 
                     'Découvrir tous nos services →'}
                  </div>
                </div>
              </Link>
            </div>
          </div>
        </div>
        
        {/* Divisore ondulato in basso */}
        <div className="absolute bottom-0 left-0 right-0 z-10">
          <div className="relative h-16">
            <div className="absolute bottom-0 inset-x-0 h-8 bg-white" style={{ clipPath: 'polygon(0 100%, 100% 100%, 100% 0)' }}></div>
            <div className="absolute bottom-0 inset-x-0 h-8 bg-white" style={{ clipPath: 'polygon(0 100%, 0 0, 100% 100%)' }}></div>
          </div>
        </div>
      </div>
    ),
    services: (
      <div className="relative py-24 sm:py-32 bg-white">
        {/* Elementi decorativi */}
        <div className="absolute inset-0 -z-10 overflow-hidden">
          <div className="absolute -top-40 right-0 w-96 h-96 bg-purple-600 rounded-full mix-blend-multiply filter blur-3xl opacity-10"></div>
          <div className="absolute bottom-0 left-1/4 w-96 h-96 bg-red-600 rounded-full mix-blend-multiply filter blur-3xl opacity-10"></div>
        </div>
        
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl lg:text-center">
            <h2 className="text-base font-semibold leading-7 bg-gradient-to-r from-red-500 to-purple-600 bg-clip-text text-transparent">
              {t.home.services.subtitle}
            </h2>
            <p className="mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
              {t.home.services.title}
            </p>
          </div>
          <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
            <dl className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-2">
              {/* Renovation */}
              <div className="group flex flex-col p-6 rounded-2xl bg-white border border-gray-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:border-purple-200">
                <dt className="flex items-center gap-x-3 text-base font-bold leading-7 text-gray-900">
                  <div className="rounded-full bg-gradient-to-r from-red-500 to-purple-600 p-2 shadow-md">
                    <FaHammer className="h-5 w-5 flex-none text-white" aria-hidden="true" />
                  </div>
                  {t.home.services.renovation.title}
                </dt>
                <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                  <p className="flex-auto">{t.home.services.renovation.description}</p>
                  <p className="mt-6">
                    <Link
                      href={`/${resolvedParams.lang}/services`}
                      className="text-sm font-semibold leading-6 inline-flex items-center bg-gradient-to-r from-red-500 to-purple-600 bg-clip-text text-transparent group-hover:translate-x-1 transition-transform duration-300"
                    >
                      {t.common.learnMore} <span aria-hidden="true" className="ml-1">→</span>
                    </Link>
                  </p>
                </dd>
              </div>

              {/* Emergency */}
              <div className="group flex flex-col p-6 rounded-2xl bg-white border border-gray-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:border-purple-200">
                <dt className="flex items-center gap-x-3 text-base font-bold leading-7 text-gray-900">
                  <div className="rounded-full bg-gradient-to-r from-red-500 to-purple-600 p-2 shadow-md">
                    <FaWater className="h-5 w-5 flex-none text-white" aria-hidden="true" />
                  </div>
                  {t.home.services.emergency.title}
                </dt>
                <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                  <p className="flex-auto">{t.home.services.emergency.description}</p>
                  <p className="mt-6">
                    <Link
                      href={`/${resolvedParams.lang}/emergency`}
                      className="text-sm font-semibold leading-6 inline-flex items-center bg-gradient-to-r from-red-500 to-purple-600 bg-clip-text text-transparent group-hover:translate-x-1 transition-transform duration-300"
                    >
                      {t.common.learnMore} <span aria-hidden="true" className="ml-1">→</span>
                    </Link>
                  </p>
                </dd>
              </div>

              {/* Servizi Tecnici */}
              <div className="group flex flex-col p-6 rounded-2xl bg-white border border-gray-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:border-purple-200">
                <dt className="flex items-center gap-x-3 text-base font-bold leading-7 text-gray-900">
                  <div className="rounded-full bg-gradient-to-r from-red-500 to-purple-600 p-2 shadow-md">
                    <FaTools className="h-5 w-5 flex-none text-white" aria-hidden="true" />
                  </div>
                  {t.home.services.technical.title}
                </dt>
                <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                  <p className="flex-auto">{t.home.services.technical.description}</p>
                  <p className="mt-6">
                    <Link
                      href={`/${resolvedParams.lang}/services`}
                      className="text-sm font-semibold leading-6 inline-flex items-center bg-gradient-to-r from-red-500 to-purple-600 bg-clip-text text-transparent group-hover:translate-x-1 transition-transform duration-300"
                    >
                      {t.common.learnMore} <span aria-hidden="true" className="ml-1">→</span>
                    </Link>
                  </p>
                </dd>
              </div>

              {/* Consulting */}
              <div className="group flex flex-col p-6 rounded-2xl bg-white border border-gray-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:border-purple-200">
                <dt className="flex items-center gap-x-3 text-base font-bold leading-7 text-gray-900">
                  <div className="rounded-full bg-gradient-to-r from-red-500 to-purple-600 p-2 shadow-md">
                    <FaComments className="h-5 w-5 flex-none text-white" aria-hidden="true" />
                  </div>
                  {t.home.services.consulting.title}
                </dt>
                <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                  <p className="flex-auto">{t.home.services.consulting.description}</p>
                  <p className="mt-6">
                    <Link
                      href={`/${resolvedParams.lang}/contact`}
                      className="text-sm font-semibold leading-6 inline-flex items-center bg-gradient-to-r from-red-500 to-purple-600 bg-clip-text text-transparent group-hover:translate-x-1 transition-transform duration-300"
                    >
                      {t.common.learnMore} <span aria-hidden="true" className="ml-1">→</span>
                    </Link>
                  </p>
                </dd>
              </div>
            </dl>
          </div>
        </div>
      </div>
    )
  };

  return (
    <main>
      {Object.entries(sections).map(([key, section]) => (
        <div key={key}>
          {section}
        </div>
      ))}
    </main>
  );
}
