{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "LGlzzJQpJu6yKIt4hRYkA", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "K3GMH8hzvSFrgSKa1k1yrS9xUQsjzxe5FROUgIWbZnQ=", "__NEXT_PREVIEW_MODE_ID": "c30a5792a2439dc2b6b4a6b710815eb6", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "914ed85743be9e13f75cd6628827c4c6ae6a9bbf34c38badcead1afb89778e60", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "5ee213dcbc4cbe5445a56a17d09d7d82bdff27ff852a999589c763794c1b5d7e"}}}, "functions": {}, "sortedMiddleware": ["/"]}