(()=>{var e={};e.id=911,e.ids=[911],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},9551:e=>{"use strict";e.exports=require("url")},9066:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>l.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d});var s=r(260),i=r(8203),a=r(5155),l=r.n(a),o=r(7292),n={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>o[e]);r.d(t,n);let d=["",{children:["[lang]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,7214)),"/home/<USER>/CascadeProjects/inparo-web/src/app/[lang]/page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,1746)),"/home/<USER>/CascadeProjects/inparo-web/src/app/[lang]/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,1354)),"/home/<USER>/CascadeProjects/inparo-web/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,9937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,1485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["/home/<USER>/CascadeProjects/inparo-web/src/app/[lang]/page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/[lang]/page",pathname:"/[lang]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},9465:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,9607,23)),Promise.resolve().then(r.t.bind(r,1066,23))},9737:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,8531,23)),Promise.resolve().then(r.t.bind(r,1902,23))},1066:(e,t,r)=>{let{createProxy:s}=r(3439);e.exports=s("/home/<USER>/CascadeProjects/inparo-web/node_modules/next/dist/client/image-component.js")},2326:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return o}}),r(5843);let s=r(6749),i=r(2833);function a(e){return void 0!==e.default}function l(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function o(e,t){var r,o;let n,d,c,{src:m,sizes:u,unoptimized:x=!1,priority:h=!1,loading:p,className:g,quality:f,width:b,height:v,fill:w=!1,style:j,overrideSrc:y,onLoad:N,onLoadingComplete:P,placeholder:_="empty",blurDataURL:z,fetchPriority:S,decoding:C="async",layout:M,objectFit:k,objectPosition:E,lazyBoundary:O,lazyRoot:I,...q}=e,{imgConf:R,showAltText:A,blurComplete:G,defaultLoader:D}=t,$=R||i.imageConfigDefault;if("allSizes"in $)n=$;else{let e=[...$.deviceSizes,...$.imageSizes].sort((e,t)=>e-t),t=$.deviceSizes.sort((e,t)=>e-t),s=null==(r=$.qualities)?void 0:r.sort((e,t)=>e-t);n={...$,allSizes:e,deviceSizes:t,qualities:s}}if(void 0===D)throw Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config");let F=q.loader||D;delete q.loader,delete q.srcSet;let L="__next_img_default"in F;if(L){if("custom"===n.loader)throw Error('Image with src "'+m+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader')}else{let e=F;F=t=>{let{config:r,...s}=t;return e(s)}}if(M){"fill"===M&&(w=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[M];e&&(j={...j,...e});let t={responsive:"100vw",fill:"100vw"}[M];t&&!u&&(u=t)}let B="",W=l(b),T=l(v);if((o=m)&&"object"==typeof o&&(a(o)||void 0!==o.src)){let e=a(m)?m.default:m;if(!e.src)throw Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e));if(!e.height||!e.width)throw Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e));if(d=e.blurWidth,c=e.blurHeight,z=z||e.blurDataURL,B=e.src,!w){if(W||T){if(W&&!T){let t=W/e.width;T=Math.round(e.height*t)}else if(!W&&T){let t=T/e.height;W=Math.round(e.width*t)}}else W=e.width,T=e.height}}let U=!h&&("lazy"===p||void 0===p);(!(m="string"==typeof m?m:B)||m.startsWith("data:")||m.startsWith("blob:"))&&(x=!0,U=!1),n.unoptimized&&(x=!0),L&&!n.dangerouslyAllowSVG&&m.split("?",1)[0].endsWith(".svg")&&(x=!0);let V=l(f),Y=Object.assign(w?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:k,objectPosition:E}:{},A?{}:{color:"transparent"},j),H=G||"empty"===_?null:"blur"===_?'url("data:image/svg+xml;charset=utf-8,'+(0,s.getImageBlurSvg)({widthInt:W,heightInt:T,blurWidth:d,blurHeight:c,blurDataURL:z||"",objectFit:Y.objectFit})+'")':'url("'+_+'")',J=H?{backgroundSize:Y.objectFit||"cover",backgroundPosition:Y.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:H}:{},K=function(e){let{config:t,src:r,unoptimized:s,width:i,quality:a,sizes:l,loader:o}=e;if(s)return{src:r,srcSet:void 0,sizes:void 0};let{widths:n,kind:d}=function(e,t,r){let{deviceSizes:s,allSizes:i}=e;if(r){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let s;s=e.exec(r);s)t.push(parseInt(s[2]));if(t.length){let e=.01*Math.min(...t);return{widths:i.filter(t=>t>=s[0]*e),kind:"w"}}return{widths:i,kind:"w"}}return"number"!=typeof t?{widths:s,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>i.find(t=>t>=e)||i[i.length-1]))],kind:"x"}}(t,i,l),c=n.length-1;return{sizes:l||"w"!==d?l:"100vw",srcSet:n.map((e,s)=>o({config:t,src:r,quality:a,width:e})+" "+("w"===d?e:s+1)+d).join(", "),src:o({config:t,src:r,quality:a,width:n[c]})}}({config:n,src:m,unoptimized:x,width:W,quality:V,sizes:u,loader:F});return{props:{...q,loading:U?"lazy":p,fetchPriority:S,width:W,height:T,decoding:C,className:g,style:{...Y,...J},sizes:K.sizes,srcSet:K.srcSet,src:y||K.src},meta:{unoptimized:x,priority:h,placeholder:_,fill:w}}}},6749:(e,t)=>{"use strict";function r(e){let{widthInt:t,heightInt:r,blurWidth:s,blurHeight:i,blurDataURL:a,objectFit:l}=e,o=s?40*s:t,n=i?40*i:r,d=o&&n?"viewBox='0 0 "+o+" "+n+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+d+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(d?"none":"contain"===l?"xMidYMid":"cover"===l?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+a+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return r}})},2833:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{VALID_LOADERS:function(){return r},imageConfigDefault:function(){return s}});let r=["default","imgix","cloudinary","akamai","custom"],s={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},8516:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return n},getImageProps:function(){return o}});let s=r(3264),i=r(2326),a=r(1066),l=s._(r(6352));function o(e){let{props:t}=(0,i.getImgProps)(e,{defaultLoader:l.default,imgConf:{deviceSizes:[640,750,828,1e3],imageSizes:[16,32,48,64,96,128,256,384,512,768,1e3],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let n=a.Image},6352:(e,t)=>{"use strict";function r(e){var t;let{config:r,src:s,width:i,quality:a}=e,l=a||(null==(t=r.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return r.path+"?url="+encodeURIComponent(s)+"&w="+i+"&q="+l+(s.startsWith("/_next/static/media/"),"")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return s}}),r.__next_img_default=!0;let s=r},5843:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"warnOnce",{enumerable:!0,get:function(){return r}});let r=e=>{}},7214:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var s=r(2740),i=r(8516),a=r.n(i),l=r(9607),o=r.n(l),n=r(1075),d=r(82);async function c({params:e}){let t=await e,r=await (0,n.s)(t.lang);if(!r||!r.home||!r.home.services)return console.error("Missing translations:",r),(0,s.jsx)("div",{children:"Loading..."});let i={hero:(0,s.jsxs)("div",{className:"relative isolate overflow-hidden bg-gradient-to-br from-gray-900 via-purple-900 to-red-900",children:[(0,s.jsxs)("div",{className:"absolute inset-0 -z-10 opacity-30",children:[(0,s.jsx)("div",{className:"absolute top-20 right-0 w-72 h-72 bg-red-500 rounded-full filter blur-3xl opacity-20"}),(0,s.jsx)("div",{className:"absolute bottom-0 left-1/4 w-72 h-72 bg-purple-500 rounded-full filter blur-3xl opacity-20"}),(0,s.jsx)("div",{className:"absolute w-full h-full opacity-10",style:{backgroundImage:'url("/images/noise.png")',backgroundRepeat:"repeat"}})]}),(0,s.jsxs)("div",{className:"mx-auto max-w-7xl px-6 py-24 sm:pb-32 lg:flex lg:px-8 lg:py-40 items-center justify-between",children:[(0,s.jsxs)("div",{className:"mx-auto max-w-2xl lg:mx-0 lg:max-w-xl lg:flex-shrink-0 lg:pt-8 z-10",children:[(0,s.jsxs)("h1",{className:"mt-10 text-4xl font-bold tracking-tight text-white sm:text-6xl",children:[r.home.hero.title," ",(0,s.jsx)("span",{className:"bg-gradient-to-r from-red-500 to-purple-600 bg-clip-text text-transparent",children:r.home.hero.titleHighlight})]}),(0,s.jsx)("p",{className:"mt-6 text-lg leading-8 text-gray-200",children:r.home.hero.subtitle}),(0,s.jsxs)("div",{className:"mt-10 flex items-center gap-x-6",children:[(0,s.jsx)(o(),{href:`/${t.lang}/contact`,className:"rounded-md bg-gradient-to-r from-red-600 to-purple-700 px-5 py-3 text-sm font-semibold text-white shadow-lg hover:shadow-xl transition-all duration-300 hover:translate-y-[-2px]",children:r.common.getQuote}),(0,s.jsx)(o(),{href:`/${t.lang}/emergency`,className:"rounded-md border border-white/30 bg-white/10 backdrop-blur-sm px-5 py-3 text-sm font-semibold text-white shadow-sm hover:bg-white/20 transition-all duration-300",children:r.common.emergency})]})]}),(0,s.jsx)("div",{className:"mt-16 lg:mt-0 lg:flex-1 flex justify-center items-center relative z-10",children:(0,s.jsxs)("div",{className:"relative mx-auto w-full max-w-lg lg:max-w-xl",children:[(0,s.jsx)("div",{className:"absolute top-0 -left-4 w-72 h-72 bg-purple-600 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-blob"}),(0,s.jsx)("div",{className:"absolute -bottom-8 right-0 w-72 h-72 bg-red-600 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-blob animation-delay-4000"}),(0,s.jsxs)(o(),{href:`/${t.lang}/services`,className:"block relative mx-auto border-8 border-white/10 rounded-2xl overflow-hidden shadow-2xl transform rotate-2 hover:rotate-0 transition-all duration-500 group",children:[(0,s.jsx)(a(),{src:"/images/services/renovation.jpg",alt:"I nostri servizi di ristrutturazione",width:600,height:400,className:"w-full h-auto object-cover rounded-xl group-hover:scale-105 transition-transform duration-500",priority:!0}),(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/70 to-transparent opacity-60"}),(0,s.jsx)("div",{className:"absolute bottom-0 left-0 right-0 p-6",children:(0,s.jsx)("div",{className:"text-white text-lg font-medium",children:"it"===t.lang?"Scopri tutti i nostri servizi →":"de"===t.lang?"Alle Dienstleistungen entdecken →":"D\xe9couvrir tous nos services →"})})]})]})})]}),(0,s.jsx)("div",{className:"absolute bottom-0 left-0 right-0 z-10",children:(0,s.jsxs)("div",{className:"relative h-16",children:[(0,s.jsx)("div",{className:"absolute bottom-0 inset-x-0 h-8 bg-white",style:{clipPath:"polygon(0 100%, 100% 100%, 100% 0)"}}),(0,s.jsx)("div",{className:"absolute bottom-0 inset-x-0 h-8 bg-white",style:{clipPath:"polygon(0 100%, 0 0, 100% 100%)"}})]})})]}),services:(0,s.jsxs)("div",{className:"relative py-24 sm:py-32 bg-white",children:[(0,s.jsxs)("div",{className:"absolute inset-0 -z-10 overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute -top-40 right-0 w-96 h-96 bg-purple-600 rounded-full mix-blend-multiply filter blur-3xl opacity-10"}),(0,s.jsx)("div",{className:"absolute bottom-0 left-1/4 w-96 h-96 bg-red-600 rounded-full mix-blend-multiply filter blur-3xl opacity-10"})]}),(0,s.jsxs)("div",{className:"mx-auto max-w-7xl px-6 lg:px-8",children:[(0,s.jsxs)("div",{className:"mx-auto max-w-2xl lg:text-center",children:[(0,s.jsx)("h2",{className:"text-base font-semibold leading-7 bg-gradient-to-r from-red-500 to-purple-600 bg-clip-text text-transparent",children:r.home.services.subtitle}),(0,s.jsx)("p",{className:"mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl",children:r.home.services.title})]}),(0,s.jsx)("div",{className:"mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none",children:(0,s.jsxs)("dl",{className:"grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-2",children:[(0,s.jsxs)("div",{className:"group flex flex-col p-6 rounded-2xl bg-white border border-gray-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:border-purple-200",children:[(0,s.jsxs)("dt",{className:"flex items-center gap-x-3 text-base font-bold leading-7 text-gray-900",children:[(0,s.jsx)("div",{className:"rounded-full bg-gradient-to-r from-red-500 to-purple-600 p-2 shadow-md",children:(0,s.jsx)(d.Kni,{className:"h-5 w-5 flex-none text-white","aria-hidden":"true"})}),r.home.services.renovation.title]}),(0,s.jsxs)("dd",{className:"mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600",children:[(0,s.jsx)("p",{className:"flex-auto",children:r.home.services.renovation.description}),(0,s.jsx)("p",{className:"mt-6",children:(0,s.jsxs)(o(),{href:`/${t.lang}/services`,className:"text-sm font-semibold leading-6 inline-flex items-center bg-gradient-to-r from-red-500 to-purple-600 bg-clip-text text-transparent group-hover:translate-x-1 transition-transform duration-300",children:[r.common.learnMore," ",(0,s.jsx)("span",{"aria-hidden":"true",className:"ml-1",children:"→"})]})})]})]}),(0,s.jsxs)("div",{className:"group flex flex-col p-6 rounded-2xl bg-white border border-gray-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:border-purple-200",children:[(0,s.jsxs)("dt",{className:"flex items-center gap-x-3 text-base font-bold leading-7 text-gray-900",children:[(0,s.jsx)("div",{className:"rounded-full bg-gradient-to-r from-red-500 to-purple-600 p-2 shadow-md",children:(0,s.jsx)(d.PYU,{className:"h-5 w-5 flex-none text-white","aria-hidden":"true"})}),r.home.services.emergency.title]}),(0,s.jsxs)("dd",{className:"mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600",children:[(0,s.jsx)("p",{className:"flex-auto",children:r.home.services.emergency.description}),(0,s.jsx)("p",{className:"mt-6",children:(0,s.jsxs)(o(),{href:`/${t.lang}/emergency`,className:"text-sm font-semibold leading-6 inline-flex items-center bg-gradient-to-r from-red-500 to-purple-600 bg-clip-text text-transparent group-hover:translate-x-1 transition-transform duration-300",children:[r.common.learnMore," ",(0,s.jsx)("span",{"aria-hidden":"true",className:"ml-1",children:"→"})]})})]})]}),(0,s.jsxs)("div",{className:"group flex flex-col p-6 rounded-2xl bg-white border border-gray-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:border-purple-200",children:[(0,s.jsxs)("dt",{className:"flex items-center gap-x-3 text-base font-bold leading-7 text-gray-900",children:[(0,s.jsx)("div",{className:"rounded-full bg-gradient-to-r from-red-500 to-purple-600 p-2 shadow-md",children:(0,s.jsx)(d.xdT,{className:"h-5 w-5 flex-none text-white","aria-hidden":"true"})}),r.home.services.technical.title]}),(0,s.jsxs)("dd",{className:"mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600",children:[(0,s.jsx)("p",{className:"flex-auto",children:r.home.services.technical.description}),(0,s.jsx)("p",{className:"mt-6",children:(0,s.jsxs)(o(),{href:`/${t.lang}/services`,className:"text-sm font-semibold leading-6 inline-flex items-center bg-gradient-to-r from-red-500 to-purple-600 bg-clip-text text-transparent group-hover:translate-x-1 transition-transform duration-300",children:[r.common.learnMore," ",(0,s.jsx)("span",{"aria-hidden":"true",className:"ml-1",children:"→"})]})})]})]}),(0,s.jsxs)("div",{className:"group flex flex-col p-6 rounded-2xl bg-white border border-gray-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:border-purple-200",children:[(0,s.jsxs)("dt",{className:"flex items-center gap-x-3 text-base font-bold leading-7 text-gray-900",children:[(0,s.jsx)("div",{className:"rounded-full bg-gradient-to-r from-red-500 to-purple-600 p-2 shadow-md",children:(0,s.jsx)(d.uN,{className:"h-5 w-5 flex-none text-white","aria-hidden":"true"})}),r.home.services.consulting.title]}),(0,s.jsxs)("dd",{className:"mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600",children:[(0,s.jsx)("p",{className:"flex-auto",children:r.home.services.consulting.description}),(0,s.jsx)("p",{className:"mt-6",children:(0,s.jsxs)(o(),{href:`/${t.lang}/contact`,className:"text-sm font-semibold leading-6 inline-flex items-center bg-gradient-to-r from-red-500 to-purple-600 bg-clip-text text-transparent group-hover:translate-x-1 transition-transform duration-300",children:[r.common.learnMore," ",(0,s.jsx)("span",{"aria-hidden":"true",className:"ml-1",children:"→"})]})})]})]})]})})]})]})};return(0,s.jsx)("main",{children:Object.entries(i).map(([e,t])=>(0,s.jsx)("div",{children:t},e))})}}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[638,403,77,321,902,840],()=>r(9066));module.exports=s})();