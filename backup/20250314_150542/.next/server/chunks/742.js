"use strict";exports.id=742,exports.ids=[742],exports.modules={1742:(e,r,s)=>{s.d(r,{I:()=>i});var t=s(5512),o=s(8009),a=s(6978),n=s(6648);function i({t:e,lang:r}){let[s,i]=(0,o.useState)({name:"",phone:"",address:"",emergency_type:"",message:"",privacy:!1}),[d,l]=(0,o.useState)({}),[c,m]=(0,o.useState)(!1),[h,g]=(0,o.useState)("idle"),u=()=>{let e={};return s.name.trim()||(e.name="Nome richiesto"),s.phone.trim()?/^[+]?[(]?[0-9]{3}[)]?[-\s.]?[0-9]{3}[-\s.]?[0-9]{4,6}$/.test(s.phone)||(e.phone="Numero di telefono non valido"):e.phone="Telefono richiesto",s.address.trim()||(e.address="Indirizzo richiesto"),s.emergency_type||(e.emergency_type="Tipo di emergenza richiesto"),s.privacy||(e.privacy="Accetta la privacy policy"),l(e),0===Object.keys(e).length},p=async e=>{if(e.preventDefault(),u()){m(!0),g("idle");try{let e=s.emergency_type||"Non specificato",r=`EMERGENZA: ${e} - ${s.name}`;if((await fetch("/api/send-email.php",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:s.name,email:"<EMAIL>",subject:r,message:`RICHIESTA DI EMERGENZA

Nome: ${s.name}
Telefono: ${s.phone}
Indirizzo: ${s.address}
Tipo di emergenza: ${e}
Messaggio: ${s.message}

Inviato dal form di emergenza del sito web.`,isEmergency:!0})})).ok)g("success"),i({name:"",phone:"",address:"",emergency_type:"",message:"",privacy:!1});else throw Error("Errore nell'invio dell'email")}catch(e){console.error("Errore durante l'invio dell'email:",e),g("error")}finally{m(!1)}}};return(0,t.jsxs)(a.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"relative overflow-hidden bg-gradient-to-br from-red-50 via-white to-orange-50 p-8 rounded-2xl shadow-xl border border-red-100",children:[(0,t.jsx)("div",{className:"absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-red-500/10 to-orange-500/10 rounded-full -mr-16 -mt-16"}),(0,t.jsx)("div",{className:"absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-red-500/10 to-orange-500/10 rounded-full -ml-12 -mb-12"}),(0,t.jsxs)("div",{className:"flex items-center mb-6",children:[(0,t.jsx)("div",{className:"bg-gradient-to-r from-red-600 to-red-500 p-3 rounded-full shadow-md mr-4",children:(0,t.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 text-white",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 10V3L4 14h7v7l9-11h-7z"})})}),(0,t.jsx)("h3",{className:"text-2xl font-bold text-gray-900",children:e.contact.emergency})]}),(0,t.jsx)("p",{className:"text-gray-700 mb-8 ml-16",children:e.contact.description}),(0,t.jsxs)("form",{onSubmit:p,className:"space-y-6 relative z-10 mx-auto max-w-3xl",children:[(0,t.jsxs)("div",{className:"relative group",children:[(0,t.jsx)("div",{className:"absolute -inset-0.5 bg-gradient-to-r from-red-200 to-orange-200 rounded-lg blur opacity-30 group-hover:opacity-50 transition duration-300"}),(0,t.jsxs)("div",{className:"relative bg-white p-5 rounded-lg shadow-sm hover:shadow transition",children:[(0,t.jsxs)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-1 flex items-center",children:[(0,t.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-2 text-red-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})}),e.contact.form.name]}),(0,t.jsx)("input",{type:"text",id:"name",name:"name",value:s.name,onChange:e=>i(r=>({...r,name:e.target.value})),className:`block w-full px-4 py-3 rounded-lg bg-gray-50 border-transparent text-gray-900 focus:border-red-500 focus:bg-white focus:ring-2 focus:ring-red-200 transition duration-200 ${d.name?"border-red-500 ring-2 ring-red-200":""}`,placeholder:e.contact.form.placeholders?.name||"Es. Mario Rossi","aria-invalid":d.name?"true":"false","aria-describedby":d.name?"name-error":void 0}),d.name&&(0,t.jsxs)("p",{id:"name-error",className:"mt-1 text-sm text-red-600 flex items-center",children:[(0,t.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"})}),d.name]})]})]}),(0,t.jsxs)("div",{className:"relative group",children:[(0,t.jsx)("div",{className:"absolute -inset-0.5 bg-gradient-to-r from-red-200 to-orange-200 rounded-lg blur opacity-30 group-hover:opacity-50 transition duration-300"}),(0,t.jsxs)("div",{className:"relative bg-white p-5 rounded-lg shadow-sm hover:shadow transition",children:[(0,t.jsxs)("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700 mb-1 flex items-center",children:[(0,t.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-2 text-red-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"})}),e.contact.form.phone]}),(0,t.jsx)("input",{type:"tel",id:"phone",name:"phone",value:s.phone,onChange:e=>i(r=>({...r,phone:e.target.value})),className:`block w-full px-4 py-3 rounded-lg bg-gray-50 border-transparent text-gray-900 focus:border-red-500 focus:bg-white focus:ring-2 focus:ring-red-200 transition duration-200 ${d.phone?"border-red-500 ring-2 ring-red-200":""}`,placeholder:e.contact.form.placeholders?.phone||"+41 XX XXX XX XX","aria-invalid":d.phone?"true":"false","aria-describedby":d.phone?"phone-error":void 0}),d.phone&&(0,t.jsxs)("p",{id:"phone-error",className:"mt-1 text-sm text-red-600 flex items-center",children:[(0,t.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"})}),d.phone]})]})]}),(0,t.jsxs)("div",{className:"relative group",children:[(0,t.jsx)("div",{className:"absolute -inset-0.5 bg-gradient-to-r from-red-200 to-orange-200 rounded-lg blur opacity-30 group-hover:opacity-50 transition duration-300"}),(0,t.jsxs)("div",{className:"relative bg-white p-5 rounded-lg shadow-sm hover:shadow transition",children:[(0,t.jsxs)("label",{htmlFor:"address",className:"block text-sm font-medium text-gray-700 mb-1 flex items-center",children:[(0,t.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-2 text-red-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:[(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})]}),e.contact.form.address||"Indirizzo"]}),(0,t.jsx)("input",{type:"text",id:"address",name:"address",value:s.address,onChange:e=>i(r=>({...r,address:e.target.value})),className:`block w-full px-4 py-3 rounded-lg bg-gray-50 border-transparent text-gray-900 focus:border-red-500 focus:bg-white focus:ring-2 focus:ring-red-200 transition duration-200 ${d.address?"border-red-500 ring-2 ring-red-200":""}`,placeholder:e.contact.form.placeholders?.address||"Via, numero civico, cap, citt\xe0","aria-invalid":d.address?"true":"false","aria-describedby":d.address?"address-error":void 0}),d.address&&(0,t.jsxs)("p",{id:"address-error",className:"mt-1 text-sm text-red-600 flex items-center",children:[(0,t.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"})}),d.address]})]})]}),(0,t.jsxs)("div",{className:"relative group",children:[(0,t.jsx)("div",{className:"absolute -inset-0.5 bg-gradient-to-r from-red-200 to-orange-200 rounded-lg blur opacity-30 group-hover:opacity-50 transition duration-300"}),(0,t.jsxs)("div",{className:"relative bg-white p-5 rounded-lg shadow-sm hover:shadow transition",children:[(0,t.jsxs)("label",{htmlFor:"emergency_type",className:"block text-sm font-medium text-gray-700 mb-1 flex items-center",children:[(0,t.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-2 text-red-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 10V3L4 14h7v7l9-11h-7z"})}),e.contact.form.emergency_type||"Tipo di emergenza"]}),(0,t.jsxs)("select",{id:"emergency_type",name:"emergency_type",value:s.emergency_type,onChange:e=>i(r=>({...r,emergency_type:e.target.value})),className:`block w-full px-4 py-3 rounded-lg bg-gray-50 border-transparent text-gray-900 focus:border-red-500 focus:bg-white focus:ring-2 focus:ring-red-200 transition duration-200 ${d.emergency_type?"border-red-500 ring-2 ring-red-200":""}`,"aria-invalid":d.emergency_type?"true":"false","aria-describedby":d.emergency_type?"emergency-type-error":void 0,children:[(0,t.jsx)("option",{value:"",children:"it"===r?"Seleziona il tipo di emergenza":"fr"===r?"S\xe9lectionnez le type d'urgence":"W\xe4hlen Sie den Notfalltyp"}),"it"===r?["Allagamento","Perdita d'acqua","Tubature rotte","Altro"].map(e=>(0,t.jsx)("option",{value:e,children:e},e)):"fr"===r?["Inondation","Fuite d'eau","Tuyaux cass\xe9s","Autre"].map(e=>(0,t.jsx)("option",{value:e,children:e},e)):["\xdcberschwemmung","Wasserleck","Gebrochene Rohre","Andere"].map(e=>(0,t.jsx)("option",{value:e,children:e},e))]}),d.emergency_type&&(0,t.jsxs)("p",{id:"emergency-type-error",className:"mt-1 text-sm text-red-600 flex items-center",children:[(0,t.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"})}),d.emergency_type]})]})]}),(0,t.jsxs)("div",{className:"relative group",children:[(0,t.jsx)("div",{className:"absolute -inset-0.5 bg-gradient-to-r from-red-200 to-orange-200 rounded-lg blur opacity-30 group-hover:opacity-50 transition duration-300"}),(0,t.jsxs)("div",{className:"relative bg-white p-5 rounded-lg shadow-sm hover:shadow transition",children:[(0,t.jsxs)("label",{htmlFor:"message",className:"block text-sm font-medium text-gray-700 mb-1 flex items-center",children:[(0,t.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-2 text-red-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"})}),e.contact.form.message]}),(0,t.jsx)("textarea",{id:"message",name:"message",rows:4,value:s.message,onChange:e=>i(r=>({...r,message:e.target.value})),className:"block w-full px-4 py-3 rounded-lg bg-gray-50 border-transparent text-gray-900 focus:border-red-500 focus:bg-white focus:ring-2 focus:ring-red-200 transition duration-200",placeholder:e.contact.form.placeholders?.message||"Scrivi qui il tuo messaggio..."})]})]}),(0,t.jsxs)("div",{className:"relative group",children:[(0,t.jsx)("div",{className:"absolute -inset-0.5 bg-gradient-to-r from-red-200 to-orange-200 rounded-lg blur opacity-30 group-hover:opacity-50 transition duration-300"}),(0,t.jsx)("div",{className:"relative bg-white p-5 rounded-lg shadow-sm hover:shadow transition",children:(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)("div",{className:"flex items-center h-5",children:(0,t.jsx)("input",{id:"privacy",name:"privacy",type:"checkbox",checked:s.privacy,onChange:e=>i(r=>({...r,privacy:e.target.checked})),className:`h-5 w-5 rounded border-transparent bg-gray-100 text-red-600 focus:ring-red-400 transition-colors ${d.privacy?"border-red-500 ring-2 ring-red-200":""}`,"aria-invalid":d.privacy?"true":"false","aria-describedby":d.privacy?"privacy-error":void 0})}),(0,t.jsxs)("div",{className:"ml-3",children:[(0,t.jsxs)("label",{htmlFor:"privacy",className:"text-sm font-medium text-gray-700 flex items-center",children:[(0,t.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-2 text-red-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"})}),"it"===r?"Accetto la privacy policy":"fr"===r?"J'accepte la politique de confidentialit\xe9":"Ich akzeptiere die Datenschutzrichtlinie"]}),d.privacy&&(0,t.jsxs)("p",{id:"privacy-error",className:"mt-1 text-sm text-red-600 flex items-center ml-6",children:[(0,t.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"})}),d.privacy]})]})]})})]}),(0,t.jsxs)("div",{className:"relative group mt-8",children:[(0,t.jsx)("div",{className:"absolute -inset-1 bg-gradient-to-r from-red-600 to-orange-500 rounded-lg blur opacity-40 group-hover:opacity-75 transition duration-300 animate-pulse",style:{zIndex:0}}),(0,t.jsx)(a.P.button,{type:"submit",disabled:c,className:"relative w-full flex justify-center items-center py-4 px-6 border border-transparent rounded-lg text-base font-bold text-white bg-gradient-to-r from-red-600 to-red-500 shadow-lg hover:from-red-500 hover:to-red-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:from-gray-400 disabled:to-gray-300 disabled:cursor-not-allowed transition-all duration-300 hover:shadow-xl z-10",whileHover:{translateY:-2},whileTap:{scale:.98},children:c?(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(a.P.div,{className:"h-5 w-5 mr-3 border-2 border-white border-t-transparent rounded-full",animate:{rotate:360},transition:{duration:1,repeat:1/0,ease:"linear"}}),(0,t.jsx)("span",{children:"Invio in corso..."})]}):(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:[(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 14l5-5-5-5"}),(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9H5"})]}),e.contact.form.submit||"Invia richiesta"]})})]})]}),(0,t.jsx)(n.N,{children:"idle"!==h&&(0,t.jsxs)(a.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{type:"spring",damping:20},className:`mt-8 p-6 rounded-xl shadow-lg border ${"success"===h?"bg-gradient-to-r from-green-50 to-green-100 border-green-200":"bg-gradient-to-r from-red-50 to-red-100 border-red-200"}`,children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:`flex-shrink-0 p-3 rounded-full ${"success"===h?"bg-green-100":"bg-red-100"}`,children:"success"===h?(0,t.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 text-green-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}):(0,t.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 text-red-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("h3",{className:`text-lg font-bold ${"success"===h?"text-green-800":"text-red-800"}`,children:"success"===h?"Richiesta inviata con successo!":"Errore durante l'invio"}),(0,t.jsx)("p",{className:`mt-1 text-sm ${"success"===h?"text-green-700":"text-red-700"}`,children:"success"===h?"Ti contatteremo al pi\xf9 presto.":"Si \xe8 verificato un errore. Riprova pi\xf9 tardi."})]})]}),"success"===h&&(0,t.jsx)("div",{className:"mt-4 flex justify-end",children:(0,t.jsx)(a.P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>g("idle"),className:"px-4 py-2 text-sm font-medium text-green-700 bg-green-100 rounded-md hover:bg-green-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500",children:"Chiudi messaggio"})})]})})]})}}};