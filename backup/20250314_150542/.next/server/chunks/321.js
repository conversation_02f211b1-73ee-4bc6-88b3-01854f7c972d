exports.id=321,exports.ids=[321],exports.modules={5179:t=>{t.exports={style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"},className:"__className_d65c78"}},9334:(t,e,i)=>{"use strict";var r=i(8686);i.o(r,"usePathname")&&i.d(e,{usePathname:function(){return r.usePathname}}),i.o(r,"useRouter")&&i.d(e,{useRouter:function(){return r.useRouter}})},4380:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"addBasePath",{enumerable:!0,get:function(){return s}});let r=i(4147),n=i(4887);function s(t,e){return(0,n.normalizePathTrailingSlash)((0,r.addPathPrefix)(t,""))}("function"==typeof e.default||"object"==typeof e.default&&null!==e.default)&&void 0===e.default.__esModule&&(Object.defineProperty(e.default,"__esModule",{value:!0}),Object.assign(e.default,e),t.exports=e.default)},8531:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"default",{enumerable:!0,get:function(){return f}});let r=i(5488),n=i(5512),s=r._(i(8009)),o=i(5024),a=i(7829),l=i(9118),u=i(5267),h=i(3727),c=i(3438),d=i(4380);function p(t){return"string"==typeof t?t:(0,o.formatUrl)(t)}i(6831);let f=s.default.forwardRef(function(t,e){let i,r;let{href:o,as:f,children:m,prefetch:v=null,passHref:g,replace:y,shallow:x,scroll:b,onClick:P,onMouseEnter:w,onTouchStart:T,legacyBehavior:S=!1,...A}=t;i=m,S&&("string"==typeof i||"number"==typeof i)&&(i=(0,n.jsx)("a",{children:i}));let M=s.default.useContext(a.AppRouterContext),E=null===v?u.PrefetchKind.AUTO:u.PrefetchKind.FULL,{href:C,as:j}=s.default.useMemo(()=>{let t=p(o);return{href:t,as:f?p(f):t}},[o,f]),V=s.default.useRef(C),D=s.default.useRef(j);S&&(r=s.default.Children.only(i));let O=S?r&&"object"==typeof r&&r.ref:e,[R,k,L]=(0,l.useIntersection)({rootMargin:"200px"}),B=s.default.useCallback(t=>{(D.current!==j||V.current!==C)&&(L(),D.current=j,V.current=C),R(t)},[j,C,L,R]),F=(0,h.useMergedRef)(B,O);s.default.useEffect(()=>{},[j,C,k,!1!==v,M,E]);let I={ref:F,onClick(t){S||"function"!=typeof P||P(t),S&&r.props&&"function"==typeof r.props.onClick&&r.props.onClick(t),M&&!t.defaultPrevented&&function(t,e,i,r,n,o,a){let{nodeName:l}=t.currentTarget;"A"===l.toUpperCase()&&function(t){let e=t.currentTarget.getAttribute("target");return e&&"_self"!==e||t.metaKey||t.ctrlKey||t.shiftKey||t.altKey||t.nativeEvent&&2===t.nativeEvent.which}(t)||(t.preventDefault(),s.default.startTransition(()=>{let t=null==a||a;"beforePopState"in e?e[n?"replace":"push"](i,r,{shallow:o,scroll:t}):e[n?"replace":"push"](r||i,{scroll:t})}))}(t,M,C,j,y,x,b)},onMouseEnter(t){S||"function"!=typeof w||w(t),S&&r.props&&"function"==typeof r.props.onMouseEnter&&r.props.onMouseEnter(t)},onTouchStart:function(t){S||"function"!=typeof T||T(t),S&&r.props&&"function"==typeof r.props.onTouchStart&&r.props.onTouchStart(t)}};return(0,c.isAbsoluteUrl)(j)?I.href=j:S&&!g&&("a"!==r.type||"href"in r.props)||(I.href=(0,d.addBasePath)(j)),S?s.default.cloneElement(r,I):(0,n.jsx)("a",{...A,...I,children:i})});("function"==typeof e.default||"object"==typeof e.default&&null!==e.default)&&void 0===e.default.__esModule&&(Object.defineProperty(e.default,"__esModule",{value:!0}),Object.assign(e.default,e),t.exports=e.default)},4887:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"normalizePathTrailingSlash",{enumerable:!0,get:function(){return s}});let r=i(5612),n=i(1546),s=t=>{if(!t.startsWith("/"))return t;let{pathname:e,query:i,hash:s}=(0,n.parsePath)(t);return/\.[^/]+\/?$/.test(e)?""+(0,r.removeTrailingSlash)(e)+i+s:e.endsWith("/")?""+e+i+s:e+"/"+i+s};("function"==typeof e.default||"object"==typeof e.default&&null!==e.default)&&void 0===e.default.__esModule&&(Object.defineProperty(e.default,"__esModule",{value:!0}),Object.assign(e.default,e),t.exports=e.default)},1284:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{cancelIdleCallback:function(){return r},requestIdleCallback:function(){return i}});let i="undefined"!=typeof self&&self.requestIdleCallback&&self.requestIdleCallback.bind(window)||function(t){let e=Date.now();return self.setTimeout(function(){t({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-e))}})},1)},r="undefined"!=typeof self&&self.cancelIdleCallback&&self.cancelIdleCallback.bind(window)||function(t){return clearTimeout(t)};("function"==typeof e.default||"object"==typeof e.default&&null!==e.default)&&void 0===e.default.__esModule&&(Object.defineProperty(e.default,"__esModule",{value:!0}),Object.assign(e.default,e),t.exports=e.default)},9118:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"useIntersection",{enumerable:!0,get:function(){return l}});let r=i(8009),n=i(1284),s="function"==typeof IntersectionObserver,o=new Map,a=[];function l(t){let{rootRef:e,rootMargin:i,disabled:l}=t,u=l||!s,[h,c]=(0,r.useState)(!1),d=(0,r.useRef)(null),p=(0,r.useCallback)(t=>{d.current=t},[]);return(0,r.useEffect)(()=>{if(s){if(u||h)return;let t=d.current;if(t&&t.tagName)return function(t,e,i){let{id:r,observer:n,elements:s}=function(t){let e;let i={root:t.root||null,margin:t.rootMargin||""},r=a.find(t=>t.root===i.root&&t.margin===i.margin);if(r&&(e=o.get(r)))return e;let n=new Map;return e={id:i,observer:new IntersectionObserver(t=>{t.forEach(t=>{let e=n.get(t.target),i=t.isIntersecting||t.intersectionRatio>0;e&&i&&e(i)})},t),elements:n},a.push(i),o.set(i,e),e}(i);return s.set(t,e),n.observe(t),function(){if(s.delete(t),n.unobserve(t),0===s.size){n.disconnect(),o.delete(r);let t=a.findIndex(t=>t.root===r.root&&t.margin===r.margin);t>-1&&a.splice(t,1)}}}(t,t=>t&&c(t),{root:null==e?void 0:e.current,rootMargin:i})}else if(!h){let t=(0,n.requestIdleCallback)(()=>c(!0));return()=>(0,n.cancelIdleCallback)(t)}},[u,i,e,h,d.current]),[p,h,(0,r.useCallback)(()=>{c(!1)},[])]}("function"==typeof e.default||"object"==typeof e.default&&null!==e.default)&&void 0===e.default.__esModule&&(Object.defineProperty(e.default,"__esModule",{value:!0}),Object.assign(e.default,e),t.exports=e.default)},3727:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"useMergedRef",{enumerable:!0,get:function(){return n}});let r=i(8009);function n(t,e){let i=(0,r.useRef)(()=>{}),n=(0,r.useRef)(()=>{});return(0,r.useMemo)(()=>t&&e?r=>{null===r?(i.current(),n.current()):(i.current=s(t,r),n.current=s(e,r))}:t||e,[t,e])}function s(t,e){if("function"!=typeof t)return t.current=e,()=>{t.current=null};{let i=t(e);return"function"==typeof i?i:()=>t(null)}}("function"==typeof e.default||"object"==typeof e.default&&null!==e.default)&&void 0===e.default.__esModule&&(Object.defineProperty(e.default,"__esModule",{value:!0}),Object.assign(e.default,e),t.exports=e.default)},4147:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"addPathPrefix",{enumerable:!0,get:function(){return n}});let r=i(1546);function n(t,e){if(!t.startsWith("/")||!e)return t;let{pathname:i,query:n,hash:s}=(0,r.parsePath)(t);return""+e+i+n+s}},5024:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{formatUrl:function(){return s},formatWithValidation:function(){return a},urlObjectKeys:function(){return o}});let r=i(1063)._(i(3866)),n=/https?|ftp|gopher|file/;function s(t){let{auth:e,hostname:i}=t,s=t.protocol||"",o=t.pathname||"",a=t.hash||"",l=t.query||"",u=!1;e=e?encodeURIComponent(e).replace(/%3A/i,":")+"@":"",t.host?u=e+t.host:i&&(u=e+(~i.indexOf(":")?"["+i+"]":i),t.port&&(u+=":"+t.port)),l&&"object"==typeof l&&(l=String(r.urlQueryToSearchParams(l)));let h=t.search||l&&"?"+l||"";return s&&!s.endsWith(":")&&(s+=":"),t.slashes||(!s||n.test(s))&&!1!==u?(u="//"+(u||""),o&&"/"!==o[0]&&(o="/"+o)):u||(u=""),a&&"#"!==a[0]&&(a="#"+a),h&&"?"!==h[0]&&(h="?"+h),""+s+u+(o=o.replace(/[?#]/g,encodeURIComponent))+(h=h.replace("#","%23"))+a}let o=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function a(t){return s(t)}},1546:(t,e)=>{"use strict";function i(t){let e=t.indexOf("#"),i=t.indexOf("?"),r=i>-1&&(e<0||i<e);return r||e>-1?{pathname:t.substring(0,r?i:e),query:r?t.substring(i,e>-1?e:void 0):"",hash:e>-1?t.slice(e):""}:{pathname:t,query:"",hash:""}}Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"parsePath",{enumerable:!0,get:function(){return i}})},3866:(t,e)=>{"use strict";function i(t){let e={};return t.forEach((t,i)=>{void 0===e[i]?e[i]=t:Array.isArray(e[i])?e[i].push(t):e[i]=[e[i],t]}),e}function r(t){return"string"!=typeof t&&("number"!=typeof t||isNaN(t))&&"boolean"!=typeof t?"":String(t)}function n(t){let e=new URLSearchParams;return Object.entries(t).forEach(t=>{let[i,n]=t;Array.isArray(n)?n.forEach(t=>e.append(i,r(t))):e.set(i,r(n))}),e}function s(t){for(var e=arguments.length,i=Array(e>1?e-1:0),r=1;r<e;r++)i[r-1]=arguments[r];return i.forEach(e=>{Array.from(e.keys()).forEach(e=>t.delete(e)),e.forEach((e,i)=>t.append(i,e))}),t}Object.defineProperty(e,"__esModule",{value:!0}),function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{assign:function(){return s},searchParamsToUrlQuery:function(){return i},urlQueryToSearchParams:function(){return n}})},5612:(t,e)=>{"use strict";function i(t){return t.replace(/\/$/,"")||"/"}Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"removeTrailingSlash",{enumerable:!0,get:function(){return i}})},3438:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{DecodeError:function(){return f},MiddlewareNotFoundError:function(){return y},MissingStaticPage:function(){return g},NormalizeError:function(){return m},PageNotFoundError:function(){return v},SP:function(){return d},ST:function(){return p},WEB_VITALS:function(){return i},execOnce:function(){return r},getDisplayName:function(){return l},getLocationOrigin:function(){return o},getURL:function(){return a},isAbsoluteUrl:function(){return s},isResSent:function(){return u},loadGetInitialProps:function(){return c},normalizeRepeatedSlashes:function(){return h},stringifyError:function(){return x}});let i=["CLS","FCP","FID","INP","LCP","TTFB"];function r(t){let e,i=!1;return function(){for(var r=arguments.length,n=Array(r),s=0;s<r;s++)n[s]=arguments[s];return i||(i=!0,e=t(...n)),e}}let n=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,s=t=>n.test(t);function o(){let{protocol:t,hostname:e,port:i}=window.location;return t+"//"+e+(i?":"+i:"")}function a(){let{href:t}=window.location,e=o();return t.substring(e.length)}function l(t){return"string"==typeof t?t:t.displayName||t.name||"Unknown"}function u(t){return t.finished||t.headersSent}function h(t){let e=t.split("?");return e[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(e[1]?"?"+e.slice(1).join("?"):"")}async function c(t,e){let i=e.res||e.ctx&&e.ctx.res;if(!t.getInitialProps)return e.ctx&&e.Component?{pageProps:await c(e.Component,e.ctx)}:{};let r=await t.getInitialProps(e);if(i&&u(i))return r;if(!r)throw Error('"'+l(t)+'.getInitialProps()" should resolve to an object. But found "'+r+'" instead.');return r}let d="undefined"!=typeof performance,p=d&&["mark","measure","getEntriesByName"].every(t=>"function"==typeof performance[t]);class f extends Error{}class m extends Error{}class v extends Error{constructor(t){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+t}}class g extends Error{constructor(t,e){super(),this.message="Failed to load static file for page: "+t+" "+e}}class y extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function x(t){return JSON.stringify({message:t.message,stack:t.stack})}},9607:(t,e,i)=>{let{createProxy:r}=i(3439);t.exports=r("/home/<USER>/CascadeProjects/inparo-web/node_modules/next/dist/client/app-dir/link.js")},6648:(t,e,i)=>{"use strict";i.d(e,{N:()=>g});var r=i(5512),n=i(8009),s=i(9872),o=i(5248),a=i(9e3),l=i(5785);class u extends n.Component{getSnapshotBeforeUpdate(t){let e=this.props.childRef.current;if(e&&t.isPresent&&!this.props.isPresent){let t=e.offsetParent,i=t instanceof HTMLElement&&t.offsetWidth||0,r=this.props.sizeRef.current;r.height=e.offsetHeight||0,r.width=e.offsetWidth||0,r.top=e.offsetTop,r.left=e.offsetLeft,r.right=i-r.width-r.left}return null}componentDidUpdate(){}render(){return this.props.children}}function h({children:t,isPresent:e,anchorX:i}){let s=(0,n.useId)(),o=(0,n.useRef)(null),a=(0,n.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:h}=(0,n.useContext)(l.Q);return(0,n.useInsertionEffect)(()=>{let{width:t,height:r,top:n,left:l,right:u}=a.current;if(e||!o.current||!t||!r)return;let c="left"===i?`left: ${l}`:`right: ${u}`;o.current.dataset.motionPopId=s;let d=document.createElement("style");return h&&(d.nonce=h),document.head.appendChild(d),d.sheet&&d.sheet.insertRule(`
          [data-motion-pop-id="${s}"] {
            position: absolute !important;
            width: ${t}px !important;
            height: ${r}px !important;
            ${c}px !important;
            top: ${n}px !important;
          }
        `),()=>{document.head.removeChild(d)}},[e]),(0,r.jsx)(u,{isPresent:e,childRef:o,sizeRef:a,children:n.cloneElement(t,{ref:o})})}let c=({children:t,initial:e,isPresent:i,onExitComplete:s,custom:l,presenceAffectsLayout:u,mode:c,anchorX:p})=>{let f=(0,o.M)(d),m=(0,n.useId)(),v=(0,n.useCallback)(t=>{for(let e of(f.set(t,!0),f.values()))if(!e)return;s&&s()},[f,s]),g=(0,n.useMemo)(()=>({id:m,initial:e,isPresent:i,custom:l,onExitComplete:v,register:t=>(f.set(t,!1),()=>f.delete(t))}),u?[Math.random(),v]:[i,v]);return(0,n.useMemo)(()=>{f.forEach((t,e)=>f.set(e,!1))},[i]),n.useEffect(()=>{i||f.size||!s||s()},[i]),"popLayout"===c&&(t=(0,r.jsx)(h,{isPresent:i,anchorX:p,children:t})),(0,r.jsx)(a.t.Provider,{value:g,children:t})};function d(){return new Map}var p=i(5883);let f=t=>t.key||"";function m(t){let e=[];return n.Children.forEach(t,t=>{(0,n.isValidElement)(t)&&e.push(t)}),e}var v=i(3607);let g=({children:t,custom:e,initial:i=!0,onExitComplete:a,presenceAffectsLayout:l=!0,mode:u="sync",propagate:h=!1,anchorX:d="left"})=>{let[g,y]=(0,p.xQ)(h),x=(0,n.useMemo)(()=>m(t),[t]),b=h&&!g?[]:x.map(f),P=(0,n.useRef)(!0),w=(0,n.useRef)(x),T=(0,o.M)(()=>new Map),[S,A]=(0,n.useState)(x),[M,E]=(0,n.useState)(x);(0,v.E)(()=>{P.current=!1,w.current=x;for(let t=0;t<M.length;t++){let e=f(M[t]);b.includes(e)?T.delete(e):!0!==T.get(e)&&T.set(e,!1)}},[M,b.length,b.join("-")]);let C=[];if(x!==S){let t=[...x];for(let e=0;e<M.length;e++){let i=M[e],r=f(i);b.includes(r)||(t.splice(e,0,i),C.push(i))}return"wait"===u&&C.length&&(t=C),E(m(t)),A(x),null}let{forceRender:j}=(0,n.useContext)(s.L);return(0,r.jsx)(r.Fragment,{children:M.map(t=>{let n=f(t),s=(!h||!!g)&&(x===M||b.includes(n));return(0,r.jsx)(c,{isPresent:s,initial:(!P.current||!!i)&&void 0,custom:e,presenceAffectsLayout:l,mode:u,onExitComplete:s?void 0:()=>{if(!T.has(n))return;T.set(n,!0);let t=!0;T.forEach(e=>{e||(t=!1)}),t&&(null==j||j(),E(w.current),h&&(null==y||y()),a&&a())},anchorX:d,children:t},n)})})}},5883:(t,e,i)=>{"use strict";i.d(e,{xQ:()=>s});var r=i(8009),n=i(9e3);function s(t=!0){let e=(0,r.useContext)(n.t);if(null===e)return[!0,null];let{isPresent:i,onExitComplete:o,register:a}=e,l=(0,r.useId)();(0,r.useEffect)(()=>{if(t)return a(l)},[t]);let u=(0,r.useCallback)(()=>t&&o&&o(l),[l,o,t]);return!i&&o?[!1,u]:[!0]}},9872:(t,e,i)=>{"use strict";i.d(e,{L:()=>r});let r=(0,i(8009).createContext)({})},5785:(t,e,i)=>{"use strict";i.d(e,{Q:()=>r});let r=(0,i(8009).createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"})},9e3:(t,e,i)=>{"use strict";i.d(e,{t:()=>r});let r=(0,i(8009).createContext)(null)},6978:(t,e,i)=>{"use strict";let r;function n(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function s(t,e,i,r){if("function"==typeof e||("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e)){let[n,s]=function(t){let e=[{},{}];return null==t||t.values.forEach((t,i)=>{e[0][i]=t.get(),e[1][i]=t.getVelocity()}),e}(r);e=e(void 0!==i?i:t.custom,n,s)}return e}function o(t,e,i){let r=t.getProps();return s(r,e,void 0!==i?i:r.custom,t)}function a(t){let e;return()=>(void 0===e&&(e=t()),e)}i.d(e,{P:()=>sv});let l=a(()=>void 0!==window.ScrollTimeline);class u{constructor(t){this.stop=()=>this.runAll("stop"),this.animations=t.filter(Boolean)}get finished(){return Promise.all(this.animations.map(t=>"finished"in t?t.finished:t))}getAll(t){return this.animations[0][t]}setAll(t,e){for(let i=0;i<this.animations.length;i++)this.animations[i][t]=e}attachTimeline(t,e){let i=this.animations.map(i=>l()&&i.attachTimeline?i.attachTimeline(t):"function"==typeof e?e(i):void 0);return()=>{i.forEach((t,e)=>{t&&t(),this.animations[e].stop()})}}get time(){return this.getAll("time")}set time(t){this.setAll("time",t)}get speed(){return this.getAll("speed")}set speed(t){this.setAll("speed",t)}get startTime(){return this.getAll("startTime")}get duration(){let t=0;for(let e=0;e<this.animations.length;e++)t=Math.max(t,this.animations[e].duration);return t}runAll(t){this.animations.forEach(e=>e[t]())}flatten(){this.runAll("flatten")}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class h extends u{then(t,e){return Promise.all(this.animations).then(t).catch(e)}}function c(t,e){return t?t[e]||t.default||t:void 0}function d(t){let e=0,i=t.next(e);for(;!i.done&&e<2e4;)e+=50,i=t.next(e);return e>=2e4?1/0:e}function p(t){return"function"==typeof t}function f(t,e){t.timeline=e,t.onfinish=null}let m=t=>Array.isArray(t)&&"number"==typeof t[0],v={linearEasing:void 0},g=function(t,e){let i=a(t);return()=>{var t;return null!==(t=v[e])&&void 0!==t?t:i()}}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),y=(t,e,i)=>{let r=e-t;return 0===r?1:(i-t)/r},x=(t,e,i=10)=>{let r="",n=Math.max(Math.round(e/i),2);for(let e=0;e<n;e++)r+=t(y(0,n-1,e))+", ";return`linear(${r.substring(0,r.length-2)})`},b=([t,e,i,r])=>`cubic-bezier(${t}, ${e}, ${i}, ${r})`,P={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:b([0,.65,.55,1]),circOut:b([.55,0,1,.45]),backIn:b([.31,.01,.66,-.59]),backOut:b([.33,1.53,.69,.99])},w={x:!1,y:!1};function T(t,e){let i=function(t,e,i){if(t instanceof EventTarget)return[t];if("string"==typeof t){let e=document.querySelectorAll(t);return e?Array.from(e):[]}return Array.from(t)}(t),r=new AbortController;return[i,{passive:!0,...e,signal:r.signal},()=>r.abort()]}function S(t){return!("touch"===t.pointerType||w.x||w.y)}function A(t,e){let i=`${e}PointerCapture`;if(t.target instanceof Element&&i in t.target&&void 0!==t.pointerId)try{t.target[i](t.pointerId)}catch(t){}}let M=(t,e)=>!!e&&(t===e||M(t,e.parentElement)),E=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary,C=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),j=new WeakSet;function V(t){return e=>{"Enter"===e.key&&t(e)}}function D(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}let O=(t,e)=>{let i=t.currentTarget;if(!i)return;let r=V(()=>{if(j.has(i))return;D(i,"down");let t=V(()=>{D(i,"up")});i.addEventListener("keyup",t,e),i.addEventListener("blur",()=>D(i,"cancel"),e)});i.addEventListener("keydown",r,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",r),e)};function R(t){return E(t)&&!(w.x||w.y)}let k=t=>1e3*t,L=t=>t/1e3,B=t=>t,F=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],I=new Set(F),U=new Set(["width","height","top","left","right","bottom",...F]),z=t=>Array.isArray(t),N=t=>!!(t&&"object"==typeof t&&t.mix&&t.toValue),$=t=>z(t)?t[t.length-1]||0:t,_={skipAnimations:!1,useManualTiming:!1},W=["read","resolveKeyframes","update","preRender","render","postRender"],H={value:null,addProjectionMetrics:null};function Y(t,e){let i=!1,r=!0,n={delta:0,timestamp:0,isProcessing:!1},s=()=>i=!0,o=W.reduce((t,i)=>(t[i]=function(t,e){let i=new Set,r=new Set,n=!1,s=!1,o=new WeakSet,a={delta:0,timestamp:0,isProcessing:!1},l=0;function u(e){o.has(e)&&(h.schedule(e),t()),l++,e(a)}let h={schedule:(t,e=!1,s=!1)=>{let a=s&&n?i:r;return e&&o.add(t),a.has(t)||a.add(t),t},cancel:t=>{r.delete(t),o.delete(t)},process:t=>{if(a=t,n){s=!0;return}n=!0,[i,r]=[r,i],i.forEach(u),e&&H.value&&H.value.frameloop[e].push(l),l=0,i.clear(),n=!1,s&&(s=!1,h.process(t))}};return h}(s,e?i:void 0),t),{}),{read:a,resolveKeyframes:l,update:u,preRender:h,render:c,postRender:d}=o,p=()=>{let s=_.useManualTiming?n.timestamp:performance.now();i=!1,_.useManualTiming||(n.delta=r?1e3/60:Math.max(Math.min(s-n.timestamp,40),1)),n.timestamp=s,n.isProcessing=!0,a.process(n),l.process(n),u.process(n),h.process(n),c.process(n),d.process(n),n.isProcessing=!1,i&&e&&(r=!1,t(p))},f=()=>{i=!0,r=!0,n.isProcessing||t(p)};return{schedule:W.reduce((t,e)=>{let r=o[e];return t[e]=(t,e=!1,n=!1)=>(i||f(),r.schedule(t,e,n)),t},{}),cancel:t=>{for(let e=0;e<W.length;e++)o[W[e]].cancel(t)},state:n,steps:o}}let{schedule:K,cancel:X,state:q,steps:G}=Y("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:B,!0);function Z(){r=void 0}let Q={now:()=>(void 0===r&&Q.set(q.isProcessing||_.useManualTiming?q.timestamp:performance.now()),r),set:t=>{r=t,queueMicrotask(Z)}};function J(t,e){-1===t.indexOf(e)&&t.push(e)}function tt(t,e){let i=t.indexOf(e);i>-1&&t.splice(i,1)}class te{constructor(){this.subscriptions=[]}add(t){return J(this.subscriptions,t),()=>tt(this.subscriptions,t)}notify(t,e,i){let r=this.subscriptions.length;if(r){if(1===r)this.subscriptions[0](t,e,i);else for(let n=0;n<r;n++){let r=this.subscriptions[n];r&&r(t,e,i)}}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}let ti=t=>!isNaN(parseFloat(t)),tr={current:void 0};class tn{constructor(t,e={}){this.version="12.4.7",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{let i=Q.now();this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),e&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=Q.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=ti(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new te);let i=this.events[t].add(e);return"change"===t?()=>{i(),K.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,i){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-i}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return tr.current&&tr.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var t;let e=Q.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||e-this.updatedAt>30)return 0;let i=Math.min(this.updatedAt-this.prevUpdatedAt,30);return t=parseFloat(this.current)-parseFloat(this.prevFrameValue),i?1e3/i*t:0}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function ts(t,e){return new tn(t,e)}let to=t=>!!(t&&t.getVelocity);function ta(t,e){let i=t.getValue("willChange");if(to(i)&&i.add)return i.add(e)}let tl=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),tu="data-"+tl("framerAppearId"),th={current:!1},tc=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function td(t,e,i,r){if(t===e&&i===r)return B;let n=e=>(function(t,e,i,r,n){let s,o;let a=0;do(s=tc(o=e+(i-e)/2,r,n)-t)>0?i=o:e=o;while(Math.abs(s)>1e-7&&++a<12);return o})(e,0,1,t,i);return t=>0===t||1===t?t:tc(n(t),e,r)}let tp=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,tf=t=>e=>1-t(1-e),tm=td(.33,1.53,.69,.99),tv=tf(tm),tg=tp(tv),ty=t=>(t*=2)<1?.5*tv(t):.5*(2-Math.pow(2,-10*(t-1))),tx=t=>1-Math.sin(Math.acos(t)),tb=tf(tx),tP=tp(tx),tw=t=>/^0[^.\s]+$/u.test(t),tT=(t,e,i)=>i>e?e:i<t?t:i,tS={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},tA={...tS,transform:t=>tT(0,1,t)},tM={...tS,default:1},tE=t=>Math.round(1e5*t)/1e5,tC=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,tj=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,tV=(t,e)=>i=>!!("string"==typeof i&&tj.test(i)&&i.startsWith(t)||e&&null!=i&&Object.prototype.hasOwnProperty.call(i,e)),tD=(t,e,i)=>r=>{if("string"!=typeof r)return r;let[n,s,o,a]=r.match(tC);return{[t]:parseFloat(n),[e]:parseFloat(s),[i]:parseFloat(o),alpha:void 0!==a?parseFloat(a):1}},tO=t=>tT(0,255,t),tR={...tS,transform:t=>Math.round(tO(t))},tk={test:tV("rgb","red"),parse:tD("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:r=1})=>"rgba("+tR.transform(t)+", "+tR.transform(e)+", "+tR.transform(i)+", "+tE(tA.transform(r))+")"},tL={test:tV("#"),parse:function(t){let e="",i="",r="",n="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),r=t.substring(5,7),n=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),r=t.substring(3,4),n=t.substring(4,5),e+=e,i+=i,r+=r,n+=n),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(r,16),alpha:n?parseInt(n,16)/255:1}},transform:tk.transform},tB=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),tF=tB("deg"),tI=tB("%"),tU=tB("px"),tz=tB("vh"),tN=tB("vw"),t$={...tI,parse:t=>tI.parse(t)/100,transform:t=>tI.transform(100*t)},t_={test:tV("hsl","hue"),parse:tD("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:r=1})=>"hsla("+Math.round(t)+", "+tI.transform(tE(e))+", "+tI.transform(tE(i))+", "+tE(tA.transform(r))+")"},tW={test:t=>tk.test(t)||tL.test(t)||t_.test(t),parse:t=>tk.test(t)?tk.parse(t):t_.test(t)?t_.parse(t):tL.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?tk.transform(t):t_.transform(t)},tH=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,tY="number",tK="color",tX=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function tq(t){let e=t.toString(),i=[],r={color:[],number:[],var:[]},n=[],s=0,o=e.replace(tX,t=>(tW.test(t)?(r.color.push(s),n.push(tK),i.push(tW.parse(t))):t.startsWith("var(")?(r.var.push(s),n.push("var"),i.push(t)):(r.number.push(s),n.push(tY),i.push(parseFloat(t))),++s,"${}")).split("${}");return{values:i,split:o,indexes:r,types:n}}function tG(t){return tq(t).values}function tZ(t){let{split:e,types:i}=tq(t),r=e.length;return t=>{let n="";for(let s=0;s<r;s++)if(n+=e[s],void 0!==t[s]){let e=i[s];e===tY?n+=tE(t[s]):e===tK?n+=tW.transform(t[s]):n+=t[s]}return n}}let tQ=t=>"number"==typeof t?0:t,tJ={test:function(t){var e,i;return isNaN(t)&&"string"==typeof t&&((null===(e=t.match(tC))||void 0===e?void 0:e.length)||0)+((null===(i=t.match(tH))||void 0===i?void 0:i.length)||0)>0},parse:tG,createTransformer:tZ,getAnimatableNone:function(t){let e=tG(t);return tZ(t)(e.map(tQ))}},t1=new Set(["brightness","contrast","saturate","opacity"]);function t0(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[r]=i.match(tC)||[];if(!r)return t;let n=i.replace(r,""),s=t1.has(e)?1:0;return r!==i&&(s*=100),e+"("+s+n+")"}let t2=/\b([a-z-]*)\(.*?\)/gu,t3={...tJ,getAnimatableNone:t=>{let e=t.match(t2);return e?e.map(t0).join(" "):t}},t6={...tS,transform:Math.round},t4={borderWidth:tU,borderTopWidth:tU,borderRightWidth:tU,borderBottomWidth:tU,borderLeftWidth:tU,borderRadius:tU,radius:tU,borderTopLeftRadius:tU,borderTopRightRadius:tU,borderBottomRightRadius:tU,borderBottomLeftRadius:tU,width:tU,maxWidth:tU,height:tU,maxHeight:tU,top:tU,right:tU,bottom:tU,left:tU,padding:tU,paddingTop:tU,paddingRight:tU,paddingBottom:tU,paddingLeft:tU,margin:tU,marginTop:tU,marginRight:tU,marginBottom:tU,marginLeft:tU,backgroundPositionX:tU,backgroundPositionY:tU,rotate:tF,rotateX:tF,rotateY:tF,rotateZ:tF,scale:tM,scaleX:tM,scaleY:tM,scaleZ:tM,skew:tF,skewX:tF,skewY:tF,distance:tU,translateX:tU,translateY:tU,translateZ:tU,x:tU,y:tU,z:tU,perspective:tU,transformPerspective:tU,opacity:tA,originX:t$,originY:t$,originZ:tU,zIndex:t6,size:tU,fillOpacity:tA,strokeOpacity:tA,numOctaves:t6},t5={...t4,color:tW,backgroundColor:tW,outlineColor:tW,fill:tW,stroke:tW,borderColor:tW,borderTopColor:tW,borderRightColor:tW,borderBottomColor:tW,borderLeftColor:tW,filter:t3,WebkitFilter:t3},t9=t=>t5[t];function t7(t,e){let i=t9(t);return i!==t3&&(i=tJ),i.getAnimatableNone?i.getAnimatableNone(e):void 0}let t8=new Set(["auto","none","0"]),et=t=>t===tS||t===tU,ee=(t,e)=>parseFloat(t.split(", ")[e]),ei=(t,e)=>(i,{transform:r})=>{if("none"===r||!r)return 0;let n=r.match(/^matrix3d\((.+)\)$/u);if(n)return ee(n[1],e);{let e=r.match(/^matrix\((.+)\)$/u);return e?ee(e[1],t):0}},er=new Set(["x","y","z"]),en=F.filter(t=>!er.has(t)),es={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:ei(4,13),y:ei(5,14)};es.translateX=es.x,es.translateY=es.y;let eo=new Set,ea=!1,el=!1;function eu(){if(el){let t=Array.from(eo).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{let e=function(t){let e=[];return en.forEach(i=>{let r=t.getValue(i);void 0!==r&&(e.push([i,r.get()]),r.set(i.startsWith("scale")?1:0))}),e}(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=i.get(t);e&&e.forEach(([e,i])=>{var r;null===(r=t.getValue(e))||void 0===r||r.set(i)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}el=!1,ea=!1,eo.forEach(t=>t.complete()),eo.clear()}function eh(){eo.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(el=!0)})}class ec{constructor(t,e,i,r,n,s=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=r,this.element=n,this.isAsync=s}scheduleResolve(){this.isScheduled=!0,this.isAsync?(eo.add(this),ea||(ea=!0,K.read(eh),K.resolveKeyframes(eu))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:i,motionValue:r}=this;for(let n=0;n<t.length;n++)if(null===t[n]){if(0===n){let n=null==r?void 0:r.get(),s=t[t.length-1];if(void 0!==n)t[0]=n;else if(i&&e){let r=i.readValue(e,s);null!=r&&(t[0]=r)}void 0===t[0]&&(t[0]=s),r&&void 0===n&&r.set(t[0])}else t[n]=t[n-1]}}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),eo.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,eo.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}let ed=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),ep=t=>e=>"string"==typeof e&&e.startsWith(t),ef=ep("--"),em=ep("var(--"),ev=t=>!!em(t)&&eg.test(t.split("/*")[0].trim()),eg=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,ey=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,ex=t=>e=>e.test(t),eb=[tS,tU,tI,tF,tN,tz,{test:t=>"auto"===t,parse:t=>t}],eP=t=>eb.find(ex(t));class ew extends ec{constructor(t,e,i,r,n){super(t,e,i,r,n,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let r=t[i];if("string"==typeof r&&ev(r=r.trim())){let n=function t(e,i,r=1){B(r<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[n,s]=function(t){let e=ey.exec(t);if(!e)return[,];let[,i,r,n]=e;return[`--${null!=i?i:r}`,n]}(e);if(!n)return;let o=window.getComputedStyle(i).getPropertyValue(n);if(o){let t=o.trim();return ed(t)?parseFloat(t):t}return ev(s)?t(s,i,r+1):s}(r,e.current);void 0!==n&&(t[i]=n),i===t.length-1&&(this.finalKeyframe=r)}}if(this.resolveNoneKeyframes(),!U.has(i)||2!==t.length)return;let[r,n]=t,s=eP(r),o=eP(n);if(s!==o){if(et(s)&&et(o))for(let e=0;e<t.length;e++){let i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else this.needsMeasurement=!0}}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++){var r;("number"==typeof(r=t[e])?0===r:null===r||"none"===r||"0"===r||tw(r))&&i.push(e)}i.length&&function(t,e,i){let r,n=0;for(;n<t.length&&!r;){let e=t[n];"string"==typeof e&&!t8.has(e)&&tq(e).values.length&&(r=t[n]),n++}if(r&&i)for(let n of e)t[n]=t7(i,r)}(t,i,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=es[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let r=e[e.length-1];void 0!==r&&t.getValue(i,r).jump(r,!1)}measureEndState(){var t;let{element:e,name:i,unresolvedKeyframes:r}=this;if(!e||!e.current)return;let n=e.getValue(i);n&&n.jump(this.measuredOrigin,!1);let s=r.length-1,o=r[s];r[s]=es[i](e.measureViewportBox(),window.getComputedStyle(e.current)),null!==o&&void 0===this.finalKeyframe&&(this.finalKeyframe=o),(null===(t=this.removedTransforms)||void 0===t?void 0:t.length)&&this.removedTransforms.forEach(([t,i])=>{e.getValue(t).set(i)}),this.resolveNoneKeyframes()}}let eT=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(tJ.test(t)||"0"===t)&&!t.startsWith("url(")),eS=t=>null!==t;function eA(t,{repeat:e,repeatType:i="loop"},r){let n=t.filter(eS),s=e&&"loop"!==i&&e%2==1?0:n.length-1;return s&&void 0!==r?r:n[s]}class eM{constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:r=0,repeatDelay:n=0,repeatType:s="loop",...o}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.createdAt=Q.now(),this.options={autoplay:t,delay:e,type:i,repeat:r,repeatDelay:n,repeatType:s,...o},this.updateFinishedPromise()}calcStartTime(){return this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt}get resolved(){return this._resolved||this.hasAttemptedResolve||(eh(),eu()),this._resolved}onKeyframesResolved(t,e){this.resolvedAt=Q.now(),this.hasAttemptedResolve=!0;let{name:i,type:r,velocity:n,delay:s,onComplete:o,onUpdate:a,isGenerator:l}=this.options;if(!l&&!function(t,e,i,r){let n=t[0];if(null===n)return!1;if("display"===e||"visibility"===e)return!0;let s=t[t.length-1],o=eT(n,e),a=eT(s,e);return B(o===a,`You are trying to animate ${e} from "${n}" to "${s}". ${n} is not an animatable value - to enable this animation set ${n} to a value animatable to ${s} via the \`style\` property.`),!!o&&!!a&&(function(t){let e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||p(i))&&r)}(t,i,r,n)){if(th.current||!s){a&&a(eA(t,this.options,e)),o&&o(),this.resolveFinishedPromise();return}this.options.duration=0}let u=this.initPlayback(t,e);!1!==u&&(this._resolved={keyframes:t,finalKeyframe:e,...u},this.onPostResolved())}onPostResolved(){}then(t,e){return this.currentFinishedPromise.then(t,e)}flatten(){this.options.type="keyframes",this.options.ease="linear"}updateFinishedPromise(){this.currentFinishedPromise=new Promise(t=>{this.resolveFinishedPromise=t})}}let eE={layout:0,mainThread:0,waapi:0},eC=(t,e,i)=>t+(e-t)*i;function ej(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}function eV(t,e){return i=>i>0?e:t}let eD=(t,e,i)=>{let r=t*t,n=i*(e*e-r)+r;return n<0?0:Math.sqrt(n)},eO=[tL,tk,t_],eR=t=>eO.find(e=>e.test(t));function ek(t){let e=eR(t);if(B(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`),!e)return!1;let i=e.parse(t);return e===t_&&(i=function({hue:t,saturation:e,lightness:i,alpha:r}){t/=360,i/=100;let n=0,s=0,o=0;if(e/=100){let r=i<.5?i*(1+e):i+e-i*e,a=2*i-r;n=ej(a,r,t+1/3),s=ej(a,r,t),o=ej(a,r,t-1/3)}else n=s=o=i;return{red:Math.round(255*n),green:Math.round(255*s),blue:Math.round(255*o),alpha:r}}(i)),i}let eL=(t,e)=>{let i=ek(t),r=ek(e);if(!i||!r)return eV(t,e);let n={...i};return t=>(n.red=eD(i.red,r.red,t),n.green=eD(i.green,r.green,t),n.blue=eD(i.blue,r.blue,t),n.alpha=eC(i.alpha,r.alpha,t),tk.transform(n))},eB=(t,e)=>i=>e(t(i)),eF=(...t)=>t.reduce(eB),eI=new Set(["none","hidden"]);function eU(t,e){return i=>eC(t,e,i)}function ez(t){return"number"==typeof t?eU:"string"==typeof t?ev(t)?eV:tW.test(t)?eL:e_:Array.isArray(t)?eN:"object"==typeof t?tW.test(t)?eL:e$:eV}function eN(t,e){let i=[...t],r=i.length,n=t.map((t,i)=>ez(t)(t,e[i]));return t=>{for(let e=0;e<r;e++)i[e]=n[e](t);return i}}function e$(t,e){let i={...t,...e},r={};for(let n in i)void 0!==t[n]&&void 0!==e[n]&&(r[n]=ez(t[n])(t[n],e[n]));return t=>{for(let e in r)i[e]=r[e](t);return i}}let e_=(t,e)=>{let i=tJ.createTransformer(e),r=tq(t),n=tq(e);return r.indexes.var.length===n.indexes.var.length&&r.indexes.color.length===n.indexes.color.length&&r.indexes.number.length>=n.indexes.number.length?eI.has(t)&&!n.values.length||eI.has(e)&&!r.values.length?function(t,e){return eI.has(t)?i=>i<=0?t:e:i=>i>=1?e:t}(t,e):eF(eN(function(t,e){var i;let r=[],n={color:0,var:0,number:0};for(let s=0;s<e.values.length;s++){let o=e.types[s],a=t.indexes[o][n[o]],l=null!==(i=t.values[a])&&void 0!==i?i:0;r[s]=l,n[o]++}return r}(r,n),n.values),i):(B(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),eV(t,e))};function eW(t,e,i){return"number"==typeof t&&"number"==typeof e&&"number"==typeof i?eC(t,e,i):ez(t)(t,e)}function eH(t,e,i){var r,n;let s=Math.max(e-5,0);return r=i-t(s),(n=e-s)?1e3/n*r:0}let eY={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function eK(t,e){return t*Math.sqrt(1-e*e)}let eX=["duration","bounce"],eq=["stiffness","damping","mass"];function eG(t,e){return e.some(e=>void 0!==t[e])}function eZ(t=eY.visualDuration,e=eY.bounce){let i;let r="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:n,restDelta:s}=r,o=r.keyframes[0],a=r.keyframes[r.keyframes.length-1],l={done:!1,value:o},{stiffness:u,damping:h,mass:c,duration:p,velocity:f,isResolvedFromDuration:m}=function(t){let e={velocity:eY.velocity,stiffness:eY.stiffness,damping:eY.damping,mass:eY.mass,isResolvedFromDuration:!1,...t};if(!eG(t,eq)&&eG(t,eX)){if(t.visualDuration){let i=2*Math.PI/(1.2*t.visualDuration),r=i*i,n=2*tT(.05,1,1-(t.bounce||0))*Math.sqrt(r);e={...e,mass:eY.mass,stiffness:r,damping:n}}else{let i=function({duration:t=eY.duration,bounce:e=eY.bounce,velocity:i=eY.velocity,mass:r=eY.mass}){let n,s;B(t<=k(eY.maxDuration),"Spring duration must be 10 seconds or less");let o=1-e;o=tT(eY.minDamping,eY.maxDamping,o),t=tT(eY.minDuration,eY.maxDuration,L(t)),o<1?(n=e=>{let r=e*o,n=r*t;return .001-(r-i)/eK(e,o)*Math.exp(-n)},s=e=>{let r=e*o*t,s=Math.pow(o,2)*Math.pow(e,2)*t,a=Math.exp(-r),l=eK(Math.pow(e,2),o);return(r*i+i-s)*a*(-n(e)+.001>0?-1:1)/l}):(n=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),s=e=>t*t*(i-e)*Math.exp(-e*t));let a=function(t,e,i){let r=i;for(let i=1;i<12;i++)r-=t(r)/e(r);return r}(n,s,5/t);if(t=k(t),isNaN(a))return{stiffness:eY.stiffness,damping:eY.damping,duration:t};{let e=Math.pow(a,2)*r;return{stiffness:e,damping:2*o*Math.sqrt(r*e),duration:t}}}(t);(e={...e,...i,mass:eY.mass}).isResolvedFromDuration=!0}}return e}({...r,velocity:-L(r.velocity||0)}),v=f||0,g=h/(2*Math.sqrt(u*c)),y=a-o,b=L(Math.sqrt(u/c)),P=5>Math.abs(y);if(n||(n=P?eY.restSpeed.granular:eY.restSpeed.default),s||(s=P?eY.restDelta.granular:eY.restDelta.default),g<1){let t=eK(b,g);i=e=>a-Math.exp(-g*b*e)*((v+g*b*y)/t*Math.sin(t*e)+y*Math.cos(t*e))}else if(1===g)i=t=>a-Math.exp(-b*t)*(y+(v+b*y)*t);else{let t=b*Math.sqrt(g*g-1);i=e=>{let i=Math.exp(-g*b*e),r=Math.min(t*e,300);return a-i*((v+g*b*y)*Math.sinh(r)+t*y*Math.cosh(r))/t}}let w={calculatedDuration:m&&p||null,next:t=>{let e=i(t);if(m)l.done=t>=p;else{let r=0;g<1&&(r=0===t?k(v):eH(i,t,e));let o=Math.abs(r)<=n,u=Math.abs(a-e)<=s;l.done=o&&u}return l.value=l.done?a:e,l},toString:()=>{let t=Math.min(d(w),2e4),e=x(e=>w.next(t*e).value,t,30);return t+"ms "+e}};return w}function eQ({keyframes:t,velocity:e=0,power:i=.8,timeConstant:r=325,bounceDamping:n=10,bounceStiffness:s=500,modifyTarget:o,min:a,max:l,restDelta:u=.5,restSpeed:h}){let c,d;let p=t[0],f={done:!1,value:p},m=t=>void 0!==a&&t<a||void 0!==l&&t>l,v=t=>void 0===a?l:void 0===l?a:Math.abs(a-t)<Math.abs(l-t)?a:l,g=i*e,y=p+g,x=void 0===o?y:o(y);x!==y&&(g=x-p);let b=t=>-g*Math.exp(-t/r),P=t=>x+b(t),w=t=>{let e=b(t),i=P(t);f.done=Math.abs(e)<=u,f.value=f.done?x:i},T=t=>{m(f.value)&&(c=t,d=eZ({keyframes:[f.value,v(f.value)],velocity:eH(P,t,f.value),damping:n,stiffness:s,restDelta:u,restSpeed:h}))};return T(0),{calculatedDuration:null,next:t=>{let e=!1;return(d||void 0!==c||(e=!0,w(t),T(t)),void 0!==c&&t>=c)?d.next(t-c):(e||w(t),f)}}}let eJ=td(.42,0,1,1),e1=td(0,0,.58,1),e0=td(.42,0,.58,1),e2=t=>Array.isArray(t)&&"number"!=typeof t[0],e3={linear:B,easeIn:eJ,easeInOut:e0,easeOut:e1,circIn:tx,circInOut:tP,circOut:tb,backIn:tv,backInOut:tg,backOut:tm,anticipate:ty},e6=t=>{if(m(t)){B(4===t.length,"Cubic bezier arrays must contain four numerical values.");let[e,i,r,n]=t;return td(e,i,r,n)}return"string"==typeof t?(B(void 0!==e3[t],`Invalid easing type '${t}'`),e3[t]):t};function e4({duration:t=300,keyframes:e,times:i,ease:r="easeInOut"}){let n=e2(r)?r.map(e6):e6(r),s={done:!1,value:e[0]},o=function(t,e,{clamp:i=!0,ease:r,mixer:n}={}){let s=t.length;if(B(s===e.length,"Both input and output ranges must be the same length"),1===s)return()=>e[0];if(2===s&&e[0]===e[1])return()=>e[1];let o=t[0]===t[1];t[0]>t[s-1]&&(t=[...t].reverse(),e=[...e].reverse());let a=function(t,e,i){let r=[],n=i||eW,s=t.length-1;for(let i=0;i<s;i++){let s=n(t[i],t[i+1]);e&&(s=eF(Array.isArray(e)?e[i]||B:e,s)),r.push(s)}return r}(e,r,n),l=a.length,u=i=>{if(o&&i<t[0])return e[0];let r=0;if(l>1)for(;r<t.length-2&&!(i<t[r+1]);r++);let n=y(t[r],t[r+1],i);return a[r](n)};return i?e=>u(tT(t[0],t[s-1],e)):u}((i&&i.length===e.length?i:function(t){let e=[0];return function(t,e){let i=t[t.length-1];for(let r=1;r<=e;r++){let n=y(0,e,r);t.push(eC(i,1,n))}}(e,t.length-1),e}(e)).map(e=>e*t),e,{ease:Array.isArray(n)?n:e.map(()=>n||e0).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(s.value=o(e),s.done=e>=t,s)}}let e5=t=>{let e=({timestamp:e})=>t(e);return{start:()=>K.update(e,!0),stop:()=>X(e),now:()=>q.isProcessing?q.timestamp:Q.now()}},e9={decay:eQ,inertia:eQ,tween:e4,keyframes:e4,spring:eZ},e7=t=>t/100;class e8 extends eM{constructor(t){super(t),this.holdTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.startTime=null,this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.teardown();let{onStop:t}=this.options;t&&t()};let{name:e,motionValue:i,element:r,keyframes:n}=this.options,s=(null==r?void 0:r.KeyframeResolver)||ec;this.resolver=new s(n,(t,e)=>this.onKeyframesResolved(t,e),e,i,r),this.resolver.scheduleResolve()}flatten(){super.flatten(),this._resolved&&Object.assign(this._resolved,this.initPlayback(this._resolved.keyframes))}initPlayback(t){let e,i;let{type:r="keyframes",repeat:n=0,repeatDelay:s=0,repeatType:o,velocity:a=0}=this.options,l=p(r)?r:e9[r]||e4;l!==e4&&"number"!=typeof t[0]&&(e=eF(e7,eW(t[0],t[1])),t=[0,100]);let u=l({...this.options,keyframes:t});"mirror"===o&&(i=l({...this.options,keyframes:[...t].reverse(),velocity:-a})),null===u.calculatedDuration&&(u.calculatedDuration=d(u));let{calculatedDuration:h}=u,c=h+s;return{generator:u,mirroredGenerator:i,mapPercentToKeyframes:e,calculatedDuration:h,resolvedDuration:c,totalDuration:c*(n+1)-s}}onPostResolved(){let{autoplay:t=!0}=this.options;eE.mainThread++,this.play(),"paused"!==this.pendingPlayState&&t?this.state=this.pendingPlayState:this.pause()}tick(t,e=!1){let{resolved:i}=this;if(!i){let{keyframes:t}=this.options;return{done:!0,value:t[t.length-1]}}let{finalKeyframe:r,generator:n,mirroredGenerator:s,mapPercentToKeyframes:o,keyframes:a,calculatedDuration:l,totalDuration:u,resolvedDuration:h}=i;if(null===this.startTime)return n.next(0);let{delay:c,repeat:d,repeatType:p,repeatDelay:f,onUpdate:m}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-u/this.speed,this.startTime)),e?this.currentTime=t:null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=Math.round(t-this.startTime)*this.speed;let v=this.currentTime-c*(this.speed>=0?1:-1),g=this.speed>=0?v<0:v>u;this.currentTime=Math.max(v,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=u);let y=this.currentTime,x=n;if(d){let t=Math.min(this.currentTime,u)/h,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,d+1))%2&&("reverse"===p?(i=1-i,f&&(i-=f/h)):"mirror"===p&&(x=s)),y=tT(0,1,i)*h}let b=g?{done:!1,value:a[0]}:x.next(y);o&&(b.value=o(b.value));let{done:P}=b;g||null===l||(P=this.speed>=0?this.currentTime>=u:this.currentTime<=0);let w=null===this.holdTime&&("finished"===this.state||"running"===this.state&&P);return w&&void 0!==r&&(b.value=eA(a,this.options,r)),m&&m(b.value),w&&this.finish(),b}get duration(){let{resolved:t}=this;return t?L(t.calculatedDuration):0}get time(){return L(this.currentTime)}set time(t){t=k(t),this.currentTime=t,null!==this.holdTime||0===this.speed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.speed)}get speed(){return this.playbackSpeed}set speed(t){let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=L(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved){this.pendingPlayState="running";return}if(this.isStopped)return;let{driver:t=e5,onPlay:e,startTime:i}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),e&&e();let r=this.driver.now();null!==this.holdTime?this.startTime=r-this.holdTime:this.startTime?"finished"===this.state&&(this.startTime=r):this.startTime=null!=i?i:this.calcStartTime(),"finished"===this.state&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){var t;if(!this._resolved){this.pendingPlayState="paused";return}this.state="paused",this.holdTime=null!==(t=this.currentTime)&&void 0!==t?t:0}complete(){"running"!==this.state&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";let{onComplete:t}=this.options;t&&t()}cancel(){null!==this.cancelTime&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel(),eE.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}}let it=new Set(["opacity","clipPath","filter","transform"]),ie=a(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),ii={anticipate:ty,backInOut:tg,circInOut:tP};class ir extends eM{constructor(t){super(t);let{name:e,motionValue:i,element:r,keyframes:n}=this.options;this.resolver=new ew(n,(t,e)=>this.onKeyframesResolved(t,e),e,i,r),this.resolver.scheduleResolve()}initPlayback(t,e){var i;let{duration:r=300,times:n,ease:s,type:o,motionValue:a,name:l,startTime:u}=this.options;if(!a.owner||!a.owner.current)return!1;if("string"==typeof s&&g()&&s in ii&&(s=ii[s]),p((i=this.options).type)||"spring"===i.type||!function t(e){return!!("function"==typeof e&&g()||!e||"string"==typeof e&&(e in P||g())||m(e)||Array.isArray(e)&&e.every(t))}(i.ease)){let{onComplete:e,onUpdate:i,motionValue:a,element:l,...u}=this.options,h=function(t,e){let i=new e8({...e,keyframes:t,repeat:0,delay:0,isGenerator:!0}),r={done:!1,value:t[0]},n=[],s=0;for(;!r.done&&s<2e4;)n.push((r=i.sample(s)).value),s+=10;return{times:void 0,keyframes:n,duration:s-10,ease:"linear"}}(t,u);1===(t=h.keyframes).length&&(t[1]=t[0]),r=h.duration,n=h.times,s=h.ease,o="keyframes"}let h=function(t,e,i,{delay:r=0,duration:n=300,repeat:s=0,repeatType:o="loop",ease:a="easeInOut",times:l}={}){let u={[e]:i};l&&(u.offset=l);let h=function t(e,i){if(e)return"function"==typeof e&&g()?x(e,i):m(e)?b(e):Array.isArray(e)?e.map(e=>t(e,i)||P.easeOut):P[e]}(a,n);Array.isArray(h)&&(u.easing=h),H.value&&eE.waapi++;let c=t.animate(u,{delay:r,duration:n,easing:Array.isArray(h)?"linear":h,fill:"both",iterations:s+1,direction:"reverse"===o?"alternate":"normal"});return H.value&&c.finished.finally(()=>{eE.waapi--}),c}(a.owner.current,l,t,{...this.options,duration:r,times:n,ease:s});return h.startTime=null!=u?u:this.calcStartTime(),this.pendingTimeline?(f(h,this.pendingTimeline),this.pendingTimeline=void 0):h.onfinish=()=>{let{onComplete:i}=this.options;a.set(eA(t,this.options,e)),i&&i(),this.cancel(),this.resolveFinishedPromise()},{animation:h,duration:r,times:n,type:o,ease:s,keyframes:t}}get duration(){let{resolved:t}=this;if(!t)return 0;let{duration:e}=t;return L(e)}get time(){let{resolved:t}=this;if(!t)return 0;let{animation:e}=t;return L(e.currentTime||0)}set time(t){let{resolved:e}=this;if(!e)return;let{animation:i}=e;i.currentTime=k(t)}get speed(){let{resolved:t}=this;if(!t)return 1;let{animation:e}=t;return e.playbackRate}set speed(t){let{resolved:e}=this;if(!e)return;let{animation:i}=e;i.playbackRate=t}get state(){let{resolved:t}=this;if(!t)return"idle";let{animation:e}=t;return e.playState}get startTime(){let{resolved:t}=this;if(!t)return null;let{animation:e}=t;return e.startTime}attachTimeline(t){if(this._resolved){let{resolved:e}=this;if(!e)return B;let{animation:i}=e;f(i,t)}else this.pendingTimeline=t;return B}play(){if(this.isStopped)return;let{resolved:t}=this;if(!t)return;let{animation:e}=t;"finished"===e.playState&&this.updateFinishedPromise(),e.play()}pause(){let{resolved:t}=this;if(!t)return;let{animation:e}=t;e.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.resolveFinishedPromise(),this.updateFinishedPromise();let{resolved:t}=this;if(!t)return;let{animation:e,keyframes:i,duration:r,type:n,ease:s,times:o}=t;if("idle"===e.playState||"finished"===e.playState)return;if(this.time){let{motionValue:t,onUpdate:e,onComplete:a,element:l,...u}=this.options,h=new e8({...u,keyframes:i,duration:r,type:n,ease:s,times:o,isGenerator:!0}),c=k(this.time);t.setWithVelocity(h.sample(c-10).value,h.sample(c).value,10)}let{onStop:a}=this.options;a&&a(),this.cancel()}complete(){let{resolved:t}=this;t&&t.animation.finish()}cancel(){let{resolved:t}=this;t&&t.animation.cancel()}static supports(t){let{motionValue:e,name:i,repeatDelay:r,repeatType:n,damping:s,type:o}=t;if(!e||!e.owner||!(e.owner.current instanceof HTMLElement))return!1;let{onUpdate:a,transformTemplate:l}=e.owner.getProps();return ie()&&i&&it.has(i)&&!a&&!l&&!r&&"mirror"!==n&&0!==s&&"inertia"!==o}}let is={type:"spring",stiffness:500,damping:25,restSpeed:10},io=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),ia={type:"keyframes",duration:.8},il={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},iu=(t,{keyframes:e})=>e.length>2?ia:I.has(t)?t.startsWith("scale")?io(e[1]):is:il,ih=(t,e,i,r={},n,s)=>o=>{let a=c(r,t)||{},l=a.delay||r.delay||0,{elapsed:u=0}=r;u-=k(l);let d={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-u,onUpdate:t=>{e.set(t),a.onUpdate&&a.onUpdate(t)},onComplete:()=>{o(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:s?void 0:n};!function({when:t,delay:e,delayChildren:i,staggerChildren:r,staggerDirection:n,repeat:s,repeatType:o,repeatDelay:a,from:l,elapsed:u,...h}){return!!Object.keys(h).length}(a)&&(d={...d,...iu(t,d)}),d.duration&&(d.duration=k(d.duration)),d.repeatDelay&&(d.repeatDelay=k(d.repeatDelay)),void 0!==d.from&&(d.keyframes[0]=d.from);let p=!1;if(!1!==d.type&&(0!==d.duration||d.repeatDelay)||(d.duration=0,0!==d.delay||(p=!0)),(th.current||_.skipAnimations)&&(p=!0,d.duration=0,d.delay=0),p&&!s&&void 0!==e.get()){let t=eA(d.keyframes,a);if(void 0!==t)return K.update(()=>{d.onUpdate(t),d.onComplete()}),new h([])}return!s&&ir.supports(d)?new ir(d):new e8(d)};function ic(t,e,{delay:i=0,transitionOverride:r,type:n}={}){var s;let{transition:a=t.getDefaultTransition(),transitionEnd:l,...u}=e;r&&(a=r);let h=[],d=n&&t.animationState&&t.animationState.getState()[n];for(let e in u){let r=t.getValue(e,null!==(s=t.latestValues[e])&&void 0!==s?s:null),n=u[e];if(void 0===n||d&&function({protectedKeys:t,needsAnimating:e},i){let r=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,r}(d,e))continue;let o={delay:i,...c(a||{},e)},l=!1;if(window.MotionHandoffAnimation){let i=t.props[tu];if(i){let t=window.MotionHandoffAnimation(i,e,K);null!==t&&(o.startTime=t,l=!0)}}ta(t,e),r.start(ih(e,r,n,t.shouldReduceMotion&&U.has(e)?{type:!1}:o,t,l));let p=r.animation;p&&h.push(p)}return l&&Promise.all(h).then(()=>{K.update(()=>{l&&function(t,e){let{transitionEnd:i={},transition:r={},...n}=o(t,e)||{};for(let e in n={...n,...i}){let i=$(n[e]);t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,ts(i))}}(t,l)})}),h}function id(t,e,i={}){var r;let n=o(t,e,"exit"===i.type?null===(r=t.presenceContext)||void 0===r?void 0:r.custom:void 0),{transition:s=t.getDefaultTransition()||{}}=n||{};i.transitionOverride&&(s=i.transitionOverride);let a=n?()=>Promise.all(ic(t,n,i)):()=>Promise.resolve(),l=t.variantChildren&&t.variantChildren.size?(r=0)=>{let{delayChildren:n=0,staggerChildren:o,staggerDirection:a}=s;return function(t,e,i=0,r=0,n=1,s){let o=[],a=(t.variantChildren.size-1)*r,l=1===n?(t=0)=>t*r:(t=0)=>a-t*r;return Array.from(t.variantChildren).sort(ip).forEach((t,r)=>{t.notify("AnimationStart",e),o.push(id(t,e,{...s,delay:i+l(r)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(o)}(t,e,n+r,o,a,i)}:()=>Promise.resolve(),{when:u}=s;if(!u)return Promise.all([a(),l(i.delay)]);{let[t,e]="beforeChildren"===u?[a,l]:[l,a];return t().then(()=>e())}}function ip(t,e){return t.sortNodePosition(e)}function im(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let r=0;r<i;r++)if(e[r]!==t[r])return!1;return!0}function iv(t){return"string"==typeof t||Array.isArray(t)}let ig=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],iy=["initial",...ig],ix=iy.length,ib=[...ig].reverse(),iP=ig.length;function iw(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function iT(){return{animate:iw(!0),whileInView:iw(),whileHover:iw(),whileTap:iw(),whileDrag:iw(),whileFocus:iw(),exit:iw()}}class iS{constructor(t){this.isMounted=!1,this.node=t}update(){}}class iA extends iS{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(function(t,e,i={}){let r;if(t.notify("AnimationStart",e),Array.isArray(e))r=Promise.all(e.map(e=>id(t,e,i)));else if("string"==typeof e)r=id(t,e,i);else{let n="function"==typeof e?o(t,e,i.custom):e;r=Promise.all(ic(t,n,i))}return r.then(()=>{t.notify("AnimationComplete",e)})})(t,e,i))),i=iT(),r=!0,s=e=>(i,r)=>{var n;let s=o(t,r,"exit"===e?null===(n=t.presenceContext)||void 0===n?void 0:n.custom:void 0);if(s){let{transition:t,transitionEnd:e,...r}=s;i={...i,...r,...e}}return i};function a(a){let{props:l}=t,u=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<ix;t++){let r=iy[t],n=e.props[r];(iv(n)||!1===n)&&(i[r]=n)}return i}(t.parent)||{},h=[],c=new Set,d={},p=1/0;for(let e=0;e<iP;e++){var f;let o=ib[e],m=i[o],v=void 0!==l[o]?l[o]:u[o],g=iv(v),y=o===a?m.isActive:null;!1===y&&(p=e);let x=v===u[o]&&v!==l[o]&&g;if(x&&r&&t.manuallyAnimateOnMount&&(x=!1),m.protectedKeys={...d},!m.isActive&&null===y||!v&&!m.prevProp||n(v)||"boolean"==typeof v)continue;let b=(f=m.prevProp,"string"==typeof v?v!==f:!!Array.isArray(v)&&!im(v,f)),P=b||o===a&&m.isActive&&!x&&g||e>p&&g,w=!1,T=Array.isArray(v)?v:[v],S=T.reduce(s(o),{});!1===y&&(S={});let{prevResolvedValues:A={}}=m,M={...A,...S},E=e=>{P=!0,c.has(e)&&(w=!0,c.delete(e)),m.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in M){let e=S[t],i=A[t];if(!d.hasOwnProperty(t))(z(e)&&z(i)?im(e,i):e===i)?void 0!==e&&c.has(t)?E(t):m.protectedKeys[t]=!0:null!=e?E(t):c.add(t)}m.prevProp=v,m.prevResolvedValues=S,m.isActive&&(d={...d,...S}),r&&t.blockInitialAnimation&&(P=!1);let C=!(x&&b)||w;P&&C&&h.push(...T.map(t=>({animation:t,options:{type:o}})))}if(c.size){let e={};if("boolean"!=typeof l.initial){let i=o(t,Array.isArray(l.initial)?l.initial[0]:l.initial);i&&i.transition&&(e.transition=i.transition)}c.forEach(i=>{let r=t.getBaseTarget(i),n=t.getValue(i);n&&(n.liveStyle=!0),e[i]=null!=r?r:null}),h.push({animation:e})}let m=!!h.length;return r&&(!1===l.initial||l.initial===l.animate)&&!t.manuallyAnimateOnMount&&(m=!1),r=!1,m?e(h):Promise.resolve()}return{animateChanges:a,setActive:function(e,r){var n;if(i[e].isActive===r)return Promise.resolve();null===(n=t.variantChildren)||void 0===n||n.forEach(t=>{var i;return null===(i=t.animationState)||void 0===i?void 0:i.setActive(e,r)}),i[e].isActive=r;let s=a(e);for(let t in i)i[t].protectedKeys={};return s},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=iT(),r=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();n(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){var t;this.node.animationState.reset(),null===(t=this.unmountControls)||void 0===t||t.call(this)}}let iM=0;class iE extends iS{constructor(){super(...arguments),this.id=iM++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let r=this.node.animationState.setActive("exit",!t);e&&!t&&r.then(()=>{e(this.id)})}mount(){let{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}function iC(t,e,i,r={passive:!0}){return t.addEventListener(e,i,r),()=>t.removeEventListener(e,i)}function ij(t){return{point:{x:t.pageX,y:t.pageY}}}let iV=t=>e=>E(e)&&t(e,ij(e));function iD(t,e,i,r){return iC(t,e,iV(i),r)}function iO({top:t,left:e,right:i,bottom:r}){return{x:{min:e,max:i},y:{min:t,max:r}}}function iR(t){return t.max-t.min}function ik(t,e,i,r=.5){t.origin=r,t.originPoint=eC(e.min,e.max,t.origin),t.scale=iR(i)/iR(e),t.translate=eC(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function iL(t,e,i,r){ik(t.x,e.x,i.x,r?r.originX:void 0),ik(t.y,e.y,i.y,r?r.originY:void 0)}function iB(t,e,i){t.min=i.min+e.min,t.max=t.min+iR(e)}function iF(t,e,i){t.min=e.min-i.min,t.max=t.min+iR(e)}function iI(t,e,i){iF(t.x,e.x,i.x),iF(t.y,e.y,i.y)}let iU=()=>({translate:0,scale:1,origin:0,originPoint:0}),iz=()=>({x:iU(),y:iU()}),iN=()=>({min:0,max:0}),i$=()=>({x:iN(),y:iN()});function i_(t){return[t("x"),t("y")]}function iW(t){return void 0===t||1===t}function iH({scale:t,scaleX:e,scaleY:i}){return!iW(t)||!iW(e)||!iW(i)}function iY(t){return iH(t)||iK(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function iK(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}function iX(t,e,i,r,n){return void 0!==n&&(t=r+n*(t-r)),r+i*(t-r)+e}function iq(t,e=0,i=1,r,n){t.min=iX(t.min,e,i,r,n),t.max=iX(t.max,e,i,r,n)}function iG(t,{x:e,y:i}){iq(t.x,e.translate,e.scale,e.originPoint),iq(t.y,i.translate,i.scale,i.originPoint)}function iZ(t,e){t.min=t.min+e,t.max=t.max+e}function iQ(t,e,i,r,n=.5){let s=eC(t.min,t.max,n);iq(t,e,i,s,r)}function iJ(t,e){iQ(t.x,e.x,e.scaleX,e.scale,e.originX),iQ(t.y,e.y,e.scaleY,e.scale,e.originY)}function i1(t,e){return iO(function(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),r=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:r.y,right:r.x}}(t.getBoundingClientRect(),e))}function i0(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}let i2=(t,e)=>Math.abs(t-e);class i3{constructor(t,e,{transformPagePoint:i,dragSnapToOrigin:r=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=i5(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,i=function(t,e){return Math.sqrt(i2(t.x,e.x)**2+i2(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=3;if(!e&&!i)return;let{point:r}=t,{timestamp:n}=q;this.history.push({...r,timestamp:n});let{onStart:s,onMove:o}=this.handlers;e||(s&&s(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),o&&o(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{if(t.target instanceof Element&&t.target.hasPointerCapture&&void 0!==t.pointerId)try{if(!t.target.hasPointerCapture(t.pointerId))return}catch(t){}this.lastMoveEvent=t,this.lastMoveEventInfo=i6(e,this.transformPagePoint),K.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{A(t,"release"),this.end();let{onEnd:i,onSessionEnd:r,resumeAnimation:n}=this.handlers;if(this.dragSnapToOrigin&&n&&n(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let s=i5("pointercancel"===t.type||"lostpointercapture"===t.type?this.lastMoveEventInfo:i6(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,s),r&&r(t,s)},!E(t))return;this.dragSnapToOrigin=r,this.handlers=e,this.transformPagePoint=i;let n=i6(ij(t),this.transformPagePoint),{point:s}=n,{timestamp:o}=q;this.history=[{...s,timestamp:o}];let{onSessionStart:a}=e;a&&a(t,i5(n,this.history)),A(t,"set"),this.removeListeners=eF(iD(t.currentTarget,"pointermove",this.handlePointerMove),iD(t.currentTarget,"pointerup",this.handlePointerUp),iD(t.currentTarget,"pointercancel",this.handlePointerUp),iD(t.currentTarget,"lostpointercapture",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),X(this.updatePoint)}}function i6(t,e){return e?{point:e(t.point)}:t}function i4(t,e){return{x:t.x-e.x,y:t.y-e.y}}function i5({point:t},e){return{point:t,delta:i4(t,i9(e)),offset:i4(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,r=null,n=i9(t);for(;i>=0&&(r=t[i],!(n.timestamp-r.timestamp>k(.1)));)i--;if(!r)return{x:0,y:0};let s=L(n.timestamp-r.timestamp);if(0===s)return{x:0,y:0};let o={x:(n.x-r.x)/s,y:(n.y-r.y)/s};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}(e,0)}}function i9(t){return t[t.length-1]}function i7(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function i8(t,e){let i=e.min-t.min,r=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,r]=[r,i]),{min:i,max:r}}function rt(t,e,i){return{min:re(t,e),max:re(t,i)}}function re(t,e){return"number"==typeof t?t:t[e]||0}let ri=new WeakMap;class rr{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=i$(),this.visualElement=t}start(t,{snapToCursor:e=!1}={}){let{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;let{dragSnapToOrigin:r}=this.getProps();this.panSession=new i3(t,{onSessionStart:t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(ij(t).point)},onStart:(t,e)=>{let{drag:i,dragPropagation:r,onDragStart:n}=this.getProps();if(i&&!r&&(this.openDragLock&&this.openDragLock(),this.openDragLock="x"===i||"y"===i?w[i]?null:(w[i]=!0,()=>{w[i]=!1}):w.x||w.y?null:(w.x=w.y=!0,()=>{w.x=w.y=!1}),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),i_(t=>{let e=this.getAxisMotionValue(t).get()||0;if(tI.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let r=i.layout.layoutBox[t];r&&(e=iR(r)*(parseFloat(e)/100))}}this.originPoint[t]=e}),n&&K.postRender(()=>n(t,e)),ta(this.visualElement,"transform");let{animationState:s}=this.visualElement;s&&s.setActive("whileDrag",!0)},onMove:(t,e)=>{let{dragPropagation:i,dragDirectionLock:r,onDirectionLock:n,onDrag:s}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:o}=e;if(r&&null===this.currentDirection){this.currentDirection=function(t,e=10){let i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(o),null!==this.currentDirection&&n&&n(this.currentDirection);return}this.updateAxis("x",e.point,o),this.updateAxis("y",e.point,o),this.visualElement.render(),s&&s(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>i_(t=>{var e;return"paused"===this.getAnimationState(t)&&(null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.play())})},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:r})}stop(t,e){let i=this.isDragging;if(this.cancel(),!i)return;let{velocity:r}=e;this.startAnimation(r);let{onDragEnd:n}=this.getProps();n&&K.postRender(()=>n(t,e))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:r}=this.getProps();if(!i||!rn(t,r,this.currentDirection))return;let n=this.getAxisMotionValue(t),s=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(s=function(t,{min:e,max:i},r){return void 0!==e&&t<e?t=r?eC(e,t,r.min):Math.max(t,e):void 0!==i&&t>i&&(t=r?eC(i,t,r.max):Math.min(t,i)),t}(s,this.constraints[t],this.elastic[t])),n.set(s)}resolveConstraints(){var t;let{dragConstraints:e,dragElastic:i}=this.getProps(),r=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null===(t=this.visualElement.projection)||void 0===t?void 0:t.layout,n=this.constraints;e&&i0(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&r?this.constraints=function(t,{top:e,left:i,bottom:r,right:n}){return{x:i7(t.x,i,n),y:i7(t.y,e,r)}}(r.layoutBox,e):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:rt(t,"left","right"),y:rt(t,"top","bottom")}}(i),n!==this.constraints&&r&&this.constraints&&!this.hasMutatedConstraints&&i_(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(r.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!i0(e))return!1;let r=e.current;B(null!==r,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:n}=this.visualElement;if(!n||!n.layout)return!1;let s=function(t,e,i){let r=i1(t,i),{scroll:n}=e;return n&&(iZ(r.x,n.offset.x),iZ(r.y,n.offset.y)),r}(r,n.root,this.visualElement.getTransformPagePoint()),o={x:i8((t=n.layout.layoutBox).x,s.x),y:i8(t.y,s.y)};if(i){let t=i(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=iO(t))}return o}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:r,dragTransition:n,dragSnapToOrigin:s,onDragTransitionEnd:o}=this.getProps(),a=this.constraints||{};return Promise.all(i_(o=>{if(!rn(o,e,this.currentDirection))return;let l=a&&a[o]||{};s&&(l={min:0,max:0});let u={type:"inertia",velocity:i?t[o]:0,bounceStiffness:r?200:1e6,bounceDamping:r?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...n,...l};return this.startAxisValueAnimation(o,u)})).then(o)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return ta(this.visualElement,t),i.start(ih(t,i,0,e,this.visualElement,!1))}stopAnimation(){i_(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){i_(t=>{var e;return null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.pause()})}getAnimationState(t){var e;return null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){i_(e=>{let{drag:i}=this.getProps();if(!rn(e,i,this.currentDirection))return;let{projection:r}=this.visualElement,n=this.getAxisMotionValue(e);if(r&&r.layout){let{min:i,max:s}=r.layout.layoutBox[e];n.set(t[e]-eC(i,s,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!i0(e)||!i||!this.constraints)return;this.stopAnimation();let r={x:0,y:0};i_(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let i=e.get();r[t]=function(t,e){let i=.5,r=iR(t),n=iR(e);return n>r?i=y(e.min,e.max-r,t.min):r>n&&(i=y(t.min,t.max-n,e.min)),tT(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:n}=this.visualElement.getProps();this.visualElement.current.style.transform=n?n({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),i_(e=>{if(!rn(e,t,null))return;let i=this.getAxisMotionValue(e),{min:n,max:s}=this.constraints[e];i.set(eC(n,s,r[e]))})}addListeners(){if(!this.visualElement.current)return;ri.set(this.visualElement,this);let t=iD(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();i0(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,r=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),K.read(e);let n=iC(window,"resize",()=>this.scalePositionWithinConstraints()),s=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(i_(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{n(),t(),r(),s&&s()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:r=!1,dragConstraints:n=!1,dragElastic:s=.35,dragMomentum:o=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:r,dragConstraints:n,dragElastic:s,dragMomentum:o}}}function rn(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class rs extends iS{constructor(t){super(t),this.removeGroupControls=B,this.removeListeners=B,this.controls=new rr(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||B}unmount(){this.removeGroupControls(),this.removeListeners()}}let ro=t=>(e,i)=>{t&&K.postRender(()=>t(e,i))};class ra extends iS{constructor(){super(...arguments),this.removePointerDownListener=B}onPointerDown(t){this.session=new i3(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint()})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:r}=this.node.getProps();return{onSessionStart:ro(t),onStart:ro(e),onMove:i,onEnd:(t,e)=>{delete this.session,r&&K.postRender(()=>r(t,e))}}}mount(){this.removePointerDownListener=iD(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var rl,ru,rh=i(5512),rc=i(8009),rd=i(5883),rp=i(9872);let rf=(0,rc.createContext)({}),rm={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function rv(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let rg={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t){if(!tU.test(t))return t;t=parseFloat(t)}let i=rv(t,e.target.x),r=rv(t,e.target.y);return`${i}% ${r}%`}},ry={},{schedule:rx,cancel:rb}=Y(queueMicrotask,!1);class rP extends rc.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:r}=this.props,{projection:n}=t;(function(t){for(let e in t)ry[e]=t[e],ef(e)&&(ry[e].isCSSVariable=!0)})(rT),n&&(e.group&&e.group.add(n),i&&i.register&&r&&i.register(n),n.root.didUpdate(),n.addEventListener("animationComplete",()=>{this.safeToRemove()}),n.setOptions({...n.options,onExitComplete:()=>this.safeToRemove()})),rm.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:r,isPresent:n}=this.props,s=i.projection;return s&&(s.isPresent=n,r||t.layoutDependency!==e||void 0===e?s.willUpdate():this.safeToRemove(),t.isPresent===n||(n?s.promote():s.relegate()||K.postRender(()=>{let t=s.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),rx.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:r}=t;r&&(r.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(r),i&&i.deregister&&i.deregister(r))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function rw(t){let[e,i]=(0,rd.xQ)(),r=(0,rc.useContext)(rp.L);return(0,rh.jsx)(rP,{...t,layoutGroup:r,switchLayoutGroup:(0,rc.useContext)(rf),isPresent:e,safeToRemove:i})}let rT={borderRadius:{...rg,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:rg,borderTopRightRadius:rg,borderBottomLeftRadius:rg,borderBottomRightRadius:rg,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let r=tJ.parse(t);if(r.length>5)return t;let n=tJ.createTransformer(t),s="number"!=typeof r[0]?1:0,o=i.x.scale*e.x,a=i.y.scale*e.y;r[0+s]/=o,r[1+s]/=a;let l=eC(o,a,.5);return"number"==typeof r[2+s]&&(r[2+s]/=l),"number"==typeof r[3+s]&&(r[3+s]/=l),n(r)}}},rS=(t,e)=>t.depth-e.depth;class rA{constructor(){this.children=[],this.isDirty=!1}add(t){J(this.children,t),this.isDirty=!0}remove(t){tt(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(rS),this.isDirty=!1,this.children.forEach(t)}}function rM(t){let e=to(t)?t.get():t;return N(e)?e.toValue():e}let rE=["TopLeft","TopRight","BottomLeft","BottomRight"],rC=rE.length,rj=t=>"string"==typeof t?parseFloat(t):t,rV=t=>"number"==typeof t||tU.test(t);function rD(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let rO=rk(0,.5,tb),rR=rk(.5,.95,B);function rk(t,e,i){return r=>r<t?0:r>e?1:i(y(t,e,r))}function rL(t,e){t.min=e.min,t.max=e.max}function rB(t,e){rL(t.x,e.x),rL(t.y,e.y)}function rF(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function rI(t,e,i,r,n){return t-=e,t=r+1/i*(t-r),void 0!==n&&(t=r+1/n*(t-r)),t}function rU(t,e,[i,r,n],s,o){!function(t,e=0,i=1,r=.5,n,s=t,o=t){if(tI.test(e)&&(e=parseFloat(e),e=eC(o.min,o.max,e/100)-o.min),"number"!=typeof e)return;let a=eC(s.min,s.max,r);t===s&&(a-=e),t.min=rI(t.min,e,i,a,n),t.max=rI(t.max,e,i,a,n)}(t,e[i],e[r],e[n],e.scale,s,o)}let rz=["x","scaleX","originX"],rN=["y","scaleY","originY"];function r$(t,e,i,r){rU(t.x,e,rz,i?i.x:void 0,r?r.x:void 0),rU(t.y,e,rN,i?i.y:void 0,r?r.y:void 0)}function r_(t){return 0===t.translate&&1===t.scale}function rW(t){return r_(t.x)&&r_(t.y)}function rH(t,e){return t.min===e.min&&t.max===e.max}function rY(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function rK(t,e){return rY(t.x,e.x)&&rY(t.y,e.y)}function rX(t){return iR(t.x)/iR(t.y)}function rq(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class rG{constructor(){this.members=[]}add(t){J(this.members,t),t.scheduleRender()}remove(t){if(tt(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e;let i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:r}=t.options;!1===r&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let rZ={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},rQ=["","X","Y","Z"],rJ={visibility:"hidden"},r1=0;function r0(t,e,i,r){let{latestValues:n}=e;n[t]&&(i[t]=n[t],e.setStaticValue(t,0),r&&(r[t]=0))}function r2({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:r,resetTransform:n}){return class{constructor(t={},i=null==e?void 0:e()){this.id=r1++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,H.value&&(rZ.nodes=rZ.calculatedTargetDeltas=rZ.calculatedProjections=0),this.nodes.forEach(r4),this.nodes.forEach(ni),this.nodes.forEach(nr),this.nodes.forEach(r5),H.addProjectionMetrics&&H.addProjectionMetrics(rZ)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new rA)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new te),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e,i=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=e instanceof SVGElement&&"svg"!==e.tagName,this.instance=e;let{layoutId:r,layout:n,visualElement:s}=this.options;if(s&&!s.current&&s.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),i&&(n||r)&&(this.isLayoutDirty=!0),t){let i;let r=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=Q.now(),r=({timestamp:e})=>{let n=e-i;n>=250&&(X(r),t(n-250))};return K.read(r,!0),()=>X(r)}(r,250),rm.hasAnimatedSinceResize&&(rm.hasAnimatedSinceResize=!1,this.nodes.forEach(ne))})}r&&this.root.registerSharedNode(r,this),!1!==this.options.animate&&s&&(r||n)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:i,layout:r})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let n=this.options.transition||s.getDefaultTransition()||nu,{onLayoutAnimationStart:o,onLayoutAnimationComplete:a}=s.getProps(),l=!this.targetLayout||!rK(this.targetLayout,r),u=!e&&i;if(this.options.layoutRoot||this.resumeFrom||u||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(t,u);let e={...c(n,"layout"),onPlay:o,onComplete:a};(s.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e)}else e||ne(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=r})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,X(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(nn),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let r=i.props[tu];if(window.MotionHasOptimisedAnimation(r,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(r,"transform",K,!(t||i))}let{parent:n}=e;n&&!n.hasCheckedOptimisedAppear&&t(n)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let r=this.getTransformTemplate();this.prevTransformTemplateValue=r?r(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(r7);return}this.isUpdating||this.nodes.forEach(r8),this.isUpdating=!1,this.nodes.forEach(nt),this.nodes.forEach(r3),this.nodes.forEach(r6),this.clearAllSnapshots();let t=Q.now();q.delta=tT(0,1e3/60,t-q.timestamp),q.timestamp=t,q.isProcessing=!0,G.update.process(q),G.preRender.process(q),G.render.process(q),q.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,rx.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(r9),this.sharedNodes.forEach(ns)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,K.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){K.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure(),!this.snapshot||iR(this.snapshot.measuredBox.x)||iR(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=i$(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e){let e=r(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!n)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!rW(this.projectionDelta),i=this.getTransformTemplate(),r=i?i(this.latestValues,""):void 0,s=r!==this.prevTransformTemplateValue;t&&(e||iY(this.latestValues)||s)&&(n(this.instance,r),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),r=this.removeElementScroll(i);return t&&(r=this.removeTransform(r)),nd((e=r).x),nd(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:r,latestValues:{},source:this.id}}measurePageBox(){var t;let{visualElement:e}=this.options;if(!e)return i$();let i=e.measureViewportBox();if(!((null===(t=this.scroll)||void 0===t?void 0:t.wasRoot)||this.path.some(nf))){let{scroll:t}=this.root;t&&(iZ(i.x,t.offset.x),iZ(i.y,t.offset.y))}return i}removeElementScroll(t){var e;let i=i$();if(rB(i,t),null===(e=this.scroll)||void 0===e?void 0:e.wasRoot)return i;for(let e=0;e<this.path.length;e++){let r=this.path[e],{scroll:n,options:s}=r;r!==this.root&&n&&s.layoutScroll&&(n.wasRoot&&rB(i,t),iZ(i.x,n.offset.x),iZ(i.y,n.offset.y))}return i}applyTransform(t,e=!1){let i=i$();rB(i,t);for(let t=0;t<this.path.length;t++){let r=this.path[t];!e&&r.options.layoutScroll&&r.scroll&&r!==r.root&&iJ(i,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),iY(r.latestValues)&&iJ(i,r.latestValues)}return iY(this.latestValues)&&iJ(i,this.latestValues),i}removeTransform(t){let e=i$();rB(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!iY(i.latestValues))continue;iH(i.latestValues)&&i.updateSnapshot();let r=i$();rB(r,i.measurePageBox()),r$(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,r)}return iY(this.latestValues)&&r$(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==q.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){var e,i,r,n;let s=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=s.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=s.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=s.isSharedProjectionDirty);let o=!!this.resumingFrom||this!==s;if(!(t||o&&this.isSharedProjectionDirty||this.isProjectionDirty||(null===(e=this.parent)||void 0===e?void 0:e.isProjectionDirty)||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:a,layoutId:l}=this.options;if(this.layout&&(a||l)){if(this.resolvedRelativeTargetAt=q.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=i$(),this.relativeTargetOrigin=i$(),iI(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),rB(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if((this.target||(this.target=i$(),this.targetWithTransforms=i$()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target)?(this.forceRelativeParentToResolveTarget(),i=this.target,r=this.relativeTarget,n=this.relativeParent.target,iB(i.x,r.x,n.x),iB(i.y,r.y,n.y)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):rB(this.target,this.layout.layoutBox),iG(this.target,this.targetDelta)):rB(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=i$(),this.relativeTargetOrigin=i$(),iI(this.relativeTargetOrigin,this.target,t.target),rB(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}H.value&&rZ.calculatedTargetDeltas++}}}getClosestProjectingParent(){return!this.parent||iH(this.parent.latestValues)||iK(this.parent.latestValues)?void 0:this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var t;let e=this.getLead(),i=!!this.resumingFrom||this!==e,r=!0;if((this.isProjectionDirty||(null===(t=this.parent)||void 0===t?void 0:t.isProjectionDirty))&&(r=!1),i&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(r=!1),this.resolvedRelativeTargetAt===q.timestamp&&(r=!1),r)return;let{layout:n,layoutId:s}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(n||s))return;rB(this.layoutCorrected,this.layout.layoutBox);let o=this.treeScale.x,a=this.treeScale.y;(function(t,e,i,r=!1){let n,s;let o=i.length;if(o){e.x=e.y=1;for(let a=0;a<o;a++){s=(n=i[a]).projectionDelta;let{visualElement:o}=n.options;(!o||!o.props.style||"contents"!==o.props.style.display)&&(r&&n.options.layoutScroll&&n.scroll&&n!==n.root&&iJ(t,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),s&&(e.x*=s.x.scale,e.y*=s.y.scale,iG(t,s)),r&&iY(n.latestValues)&&iJ(t,n.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}})(this.layoutCorrected,this.treeScale,this.path,i),e.layout&&!e.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(e.target=e.layout.layoutBox,e.targetWithTransforms=i$());let{target:l}=e;if(!l){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(rF(this.prevProjectionDelta.x,this.projectionDelta.x),rF(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),iL(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.treeScale.x===o&&this.treeScale.y===a&&rq(this.projectionDelta.x,this.prevProjectionDelta.x)&&rq(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),H.value&&rZ.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){var e;if(null===(e=this.options.visualElement)||void 0===e||e.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=iz(),this.projectionDelta=iz(),this.projectionDeltaWithTransform=iz()}setAnimationOrigin(t,e=!1){let i;let r=this.snapshot,n=r?r.latestValues:{},s={...this.latestValues},o=iz();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let a=i$(),l=(r?r.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),h=!u||u.members.length<=1,c=!!(l&&!h&&!0===this.options.crossfade&&!this.path.some(nl));this.animationProgress=0,this.mixTargetDelta=e=>{let r=e/1e3;if(no(o.x,t.x,r),no(o.y,t.y,r),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,d,p,f;iI(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,f=this.relativeTargetOrigin,na(p.x,f.x,a.x,r),na(p.y,f.y,a.y,r),i&&(u=this.relativeTarget,d=i,rH(u.x,d.x)&&rH(u.y,d.y))&&(this.isProjectionDirty=!1),i||(i=i$()),rB(i,this.relativeTarget)}l&&(this.animationValues=s,function(t,e,i,r,n,s){n?(t.opacity=eC(0,void 0!==i.opacity?i.opacity:1,rO(r)),t.opacityExit=eC(void 0!==e.opacity?e.opacity:1,0,rR(r))):s&&(t.opacity=eC(void 0!==e.opacity?e.opacity:1,void 0!==i.opacity?i.opacity:1,r));for(let n=0;n<rC;n++){let s=`border${rE[n]}Radius`,o=rD(e,s),a=rD(i,s);(void 0!==o||void 0!==a)&&(o||(o=0),a||(a=0),0===o||0===a||rV(o)===rV(a)?(t[s]=Math.max(eC(rj(o),rj(a),r),0),(tI.test(a)||tI.test(o))&&(t[s]+="%")):t[s]=a)}(e.rotate||i.rotate)&&(t.rotate=eC(e.rotate||0,i.rotate||0,r))}(s,n,this.latestValues,r,c,h)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=r},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(X(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=K.update(()=>{rm.hasAnimatedSinceResize=!0,eE.layout++,this.currentAnimation=function(t,e,i){let r=to(0)?0:ts(0);return r.start(ih("",r,1e3,i)),r.animation}(0,0,{...t,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{eE.layout--},onComplete:()=>{eE.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:r,latestValues:n}=t;if(e&&i&&r){if(this!==t&&this.layout&&r&&np(this.options.animationType,this.layout.layoutBox,r.layoutBox)){i=this.target||i$();let e=iR(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let r=iR(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+r}rB(e,i),iJ(e,n),iL(this.projectionDeltaWithTransform,this.layoutCorrected,e,n)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new rG),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){var t;let{layoutId:e}=this.options;return e&&(null===(t=this.getStack())||void 0===t?void 0:t.lead)||this}getPrevLead(){var t;let{layoutId:e}=this.options;return e?null===(t=this.getStack())||void 0===t?void 0:t.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let r=this.getStack();r&&r.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let r={};i.z&&r0("z",t,r,this.animationValues);for(let e=0;e<rQ.length;e++)r0(`rotate${rQ[e]}`,t,r,this.animationValues),r0(`skew${rQ[e]}`,t,r,this.animationValues);for(let e in t.render(),r)t.setStaticValue(e,r[e]),this.animationValues&&(this.animationValues[e]=r[e]);t.scheduleRender()}getProjectionStyles(t){var e,i;if(!this.instance||this.isSVG)return;if(!this.isVisible)return rJ;let r={visibility:""},n=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,r.opacity="",r.pointerEvents=rM(null==t?void 0:t.pointerEvents)||"",r.transform=n?n(this.latestValues,""):"none",r;let s=this.getLead();if(!this.projectionDelta||!this.layout||!s.target){let e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=rM(null==t?void 0:t.pointerEvents)||""),this.hasProjected&&!iY(this.latestValues)&&(e.transform=n?n({},""):"none",this.hasProjected=!1),e}let o=s.animationValues||s.latestValues;this.applyTransformsToTarget(),r.transform=function(t,e,i){let r="",n=t.x.translate/e.x,s=t.y.translate/e.y,o=(null==i?void 0:i.z)||0;if((n||s||o)&&(r=`translate3d(${n}px, ${s}px, ${o}px) `),(1!==e.x||1!==e.y)&&(r+=`scale(${1/e.x}, ${1/e.y}) `),i){let{transformPerspective:t,rotate:e,rotateX:n,rotateY:s,skewX:o,skewY:a}=i;t&&(r=`perspective(${t}px) ${r}`),e&&(r+=`rotate(${e}deg) `),n&&(r+=`rotateX(${n}deg) `),s&&(r+=`rotateY(${s}deg) `),o&&(r+=`skewX(${o}deg) `),a&&(r+=`skewY(${a}deg) `)}let a=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==a||1!==l)&&(r+=`scale(${a}, ${l})`),r||"none"}(this.projectionDeltaWithTransform,this.treeScale,o),n&&(r.transform=n(o,r.transform));let{x:a,y:l}=this.projectionDelta;for(let t in r.transformOrigin=`${100*a.origin}% ${100*l.origin}% 0`,s.animationValues?r.opacity=s===this?null!==(i=null!==(e=o.opacity)&&void 0!==e?e:this.latestValues.opacity)&&void 0!==i?i:1:this.preserveOpacity?this.latestValues.opacity:o.opacityExit:r.opacity=s===this?void 0!==o.opacity?o.opacity:"":void 0!==o.opacityExit?o.opacityExit:0,ry){if(void 0===o[t])continue;let{correct:e,applyTo:i,isCSSVariable:n}=ry[t],a="none"===r.transform?o[t]:e(o[t],s);if(i){let t=i.length;for(let e=0;e<t;e++)r[i[e]]=a}else n?this.options.visualElement.renderState.vars[t]=a:r[t]=a}return this.options.layoutId&&(r.pointerEvents=s===this?rM(null==t?void 0:t.pointerEvents)||"":"none"),r}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>{var e;return null===(e=t.currentAnimation)||void 0===e?void 0:e.stop()}),this.root.nodes.forEach(r7),this.root.sharedNodes.clear()}}}function r3(t){t.updateLayout()}function r6(t){var e;let i=(null===(e=t.resumeFrom)||void 0===e?void 0:e.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&i&&t.hasListeners("didUpdate")){let{layoutBox:e,measuredBox:r}=t.layout,{animationType:n}=t.options,s=i.source!==t.layout.source;"size"===n?i_(t=>{let r=s?i.measuredBox[t]:i.layoutBox[t],n=iR(r);r.min=e[t].min,r.max=r.min+n}):np(n,i.layoutBox,e)&&i_(r=>{let n=s?i.measuredBox[r]:i.layoutBox[r],o=iR(e[r]);n.max=n.min+o,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[r].max=t.relativeTarget[r].min+o)});let o=iz();iL(o,e,i.layoutBox);let a=iz();s?iL(a,t.applyTransform(r,!0),i.measuredBox):iL(a,e,i.layoutBox);let l=!rW(o),u=!1;if(!t.resumeFrom){let r=t.getClosestProjectingParent();if(r&&!r.resumeFrom){let{snapshot:n,layout:s}=r;if(n&&s){let o=i$();iI(o,i.layoutBox,n.layoutBox);let a=i$();iI(a,e,s.layoutBox),rK(o,a)||(u=!0),r.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=o,t.relativeParent=r)}}}t.notifyListeners("didUpdate",{layout:e,snapshot:i,delta:a,layoutDelta:o,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function r4(t){H.value&&rZ.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function r5(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function r9(t){t.clearSnapshot()}function r7(t){t.clearMeasurements()}function r8(t){t.isLayoutDirty=!1}function nt(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function ne(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function ni(t){t.resolveTargetDelta()}function nr(t){t.calcProjection()}function nn(t){t.resetSkewAndRotation()}function ns(t){t.removeLeadSnapshot()}function no(t,e,i){t.translate=eC(e.translate,0,i),t.scale=eC(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function na(t,e,i,r){t.min=eC(e.min,i.min,r),t.max=eC(e.max,i.max,r)}function nl(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let nu={duration:.45,ease:[.4,0,.1,1]},nh=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),nc=nh("applewebkit/")&&!nh("chrome/")?Math.round:B;function nd(t){t.min=nc(t.min),t.max=nc(t.max)}function np(t,e,i){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(rX(e)-rX(i)))}function nf(t){var e;return t!==t.root&&(null===(e=t.scroll)||void 0===e?void 0:e.wasRoot)}let nm=r2({attachResizeListener:(t,e)=>iC(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),nv={current:void 0},ng=r2({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!nv.current){let t=new nm({});t.mount(window),t.setOptions({layoutScroll:!0}),nv.current=t}return nv.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});function ny(t,e,i){let{props:r}=t;t.animationState&&r.whileHover&&t.animationState.setActive("whileHover","Start"===i);let n=r["onHover"+i];n&&K.postRender(()=>n(e,ij(e)))}class nx extends iS{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[r,n,s]=T(t,i),o=t=>{if(!S(t))return;let{target:i}=t,r=e(i,t);if("function"!=typeof r||!i)return;let s=t=>{S(t)&&(r(t),i.removeEventListener("pointerleave",s))};i.addEventListener("pointerleave",s,n)};return r.forEach(t=>{t.addEventListener("pointerenter",o,n)}),s}(t,(t,e)=>(ny(this.node,e,"Start"),t=>ny(this.node,t,"End"))))}unmount(){}}class nb extends iS{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=eF(iC(this.node.current,"focus",()=>this.onFocus()),iC(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function nP(t,e,i){let{props:r}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&r.whileTap&&t.animationState.setActive("whileTap","Start"===i);let n=r["onTap"+("End"===i?"":i)];n&&K.postRender(()=>n(e,ij(e)))}class nw extends iS{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[r,n,s]=T(t,i),o=t=>{let i=t.currentTarget;if(!i||!R(t)||j.has(i))return;j.add(i),A(t,"set");let r=e(i,t),s=(t,e)=>{i.removeEventListener("pointerup",o),i.removeEventListener("pointercancel",a),A(t,"release"),R(t)&&j.has(i)&&(j.delete(i),"function"==typeof r&&r(t,{success:e}))},o=t=>{var e;t.isTrusted&&(e=i instanceof Element?i.getBoundingClientRect():{left:0,top:0,right:window.innerWidth,bottom:window.innerHeight},t.clientX<e.left||t.clientX>e.right||t.clientY<e.top||t.clientY>e.bottom)?s(t,!1):s(t,!(i instanceof Element)||M(i,t.target))},a=t=>{s(t,!1)};i.addEventListener("pointerup",o,n),i.addEventListener("pointercancel",a,n),i.addEventListener("lostpointercapture",a,n)};return r.forEach(t=>{t=i.useGlobalTarget?window:t;let e=!1;if(t instanceof HTMLElement){var r;e=!0,r=t,C.has(r.tagName)||-1!==r.tabIndex||null!==t.getAttribute("tabindex")||(t.tabIndex=0)}t.addEventListener("pointerdown",o,n),e&&t.addEventListener("focus",t=>O(t,n),n)}),s}(t,(t,e)=>(nP(this.node,e,"Start"),(t,{success:e})=>nP(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let nT=new WeakMap,nS=new WeakMap,nA=t=>{let e=nT.get(t.target);e&&e(t)},nM=t=>{t.forEach(nA)},nE={some:0,all:1};class nC extends iS{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:r="some",once:n}=t,s={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof r?r:nE[r]};return function(t,e,i){let r=function({root:t,...e}){let i=t||document;nS.has(i)||nS.set(i,{});let r=nS.get(i),n=JSON.stringify(e);return r[n]||(r[n]=new IntersectionObserver(nM,{root:t,...e})),r[n]}(e);return nT.set(t,i),r.observe(t),()=>{nT.delete(t),r.unobserve(t)}}(this.node.current,s,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,n&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:r}=this.node.getProps(),s=e?i:r;s&&s(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}let nj=(0,rc.createContext)({strict:!1});var nV=i(5785);let nD=(0,rc.createContext)({});function nO(t){return n(t.animate)||iy.some(e=>iv(t[e]))}function nR(t){return!!(nO(t)||t.variants)}function nk(t){return Array.isArray(t)?t.join(" "):t}var nL=i(3969);let nB={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},nF={};for(let t in nB)nF[t]={isEnabled:e=>nB[t].some(t=>!!e[t])};let nI=Symbol.for("motionComponentSymbol");var nU=i(9e3),nz=i(3607);function nN(t,{layout:e,layoutId:i}){return I.has(t)||t.startsWith("origin")||(e||void 0!==i)&&(!!ry[t]||"opacity"===t)}let n$=(t,e)=>e&&"number"==typeof t?e.transform(t):t,n_={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},nW=F.length;function nH(t,e,i){let{style:r,vars:n,transformOrigin:s}=t,o=!1,a=!1;for(let t in e){let i=e[t];if(I.has(t)){o=!0;continue}if(ef(t)){n[t]=i;continue}{let e=n$(i,t4[t]);t.startsWith("origin")?(a=!0,s[t]=e):r[t]=e}}if(!e.transform&&(o||i?r.transform=function(t,e,i){let r="",n=!0;for(let s=0;s<nW;s++){let o=F[s],a=t[o];if(void 0===a)continue;let l=!0;if(!(l="number"==typeof a?a===(o.startsWith("scale")?1:0):0===parseFloat(a))||i){let t=n$(a,t4[o]);if(!l){n=!1;let e=n_[o]||o;r+=`${e}(${t}) `}i&&(e[o]=t)}}return r=r.trim(),i?r=i(e,n?"":r):n&&(r="none"),r}(e,t.transform,i):r.transform&&(r.transform="none")),a){let{originX:t="50%",originY:e="50%",originZ:i=0}=s;r.transformOrigin=`${t} ${e} ${i}`}}let nY=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function nK(t,e,i){for(let r in e)to(e[r])||nN(r,i)||(t[r]=e[r])}let nX=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function nq(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||nX.has(t)}let nG=t=>!nq(t);try{!function(t){t&&(nG=e=>e.startsWith("on")?!nq(e):t(e))}(require("@emotion/is-prop-valid").default)}catch(t){}let nZ=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function nQ(t){if("string"!=typeof t||t.includes("-"));else if(nZ.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}let nJ={offset:"stroke-dashoffset",array:"stroke-dasharray"},n1={offset:"strokeDashoffset",array:"strokeDasharray"};function n0(t,e,i){return"string"==typeof t?t:tU.transform(e+i*t)}function n2(t,{attrX:e,attrY:i,attrScale:r,originX:n,originY:s,pathLength:o,pathSpacing:a=1,pathOffset:l=0,...u},h,c){if(nH(t,u,c),h){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:d,style:p,dimensions:f}=t;d.transform&&(f&&(p.transform=d.transform),delete d.transform),f&&(void 0!==n||void 0!==s||p.transform)&&(p.transformOrigin=function(t,e,i){let r=n0(e,t.x,t.width),n=n0(i,t.y,t.height);return`${r} ${n}`}(f,void 0!==n?n:.5,void 0!==s?s:.5)),void 0!==e&&(d.x=e),void 0!==i&&(d.y=i),void 0!==r&&(d.scale=r),void 0!==o&&function(t,e,i=1,r=0,n=!0){t.pathLength=1;let s=n?nJ:n1;t[s.offset]=tU.transform(-r);let o=tU.transform(e),a=tU.transform(i);t[s.array]=`${o} ${a}`}(d,o,a,l,!1)}let n3=()=>({...nY(),attrs:{}}),n6=t=>"string"==typeof t&&"svg"===t.toLowerCase();var n4=i(5248);let n5=t=>(e,i)=>{let r=(0,rc.useContext)(nD),o=(0,rc.useContext)(nU.t),a=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e,onUpdate:i},r,o,a){let l={latestValues:function(t,e,i,r){let o={},a=r(t,{});for(let t in a)o[t]=rM(a[t]);let{initial:l,animate:u}=t,h=nO(t),c=nR(t);e&&c&&!h&&!1!==t.inherit&&(void 0===l&&(l=e.initial),void 0===u&&(u=e.animate));let d=!!i&&!1===i.initial,p=(d=d||!1===l)?u:l;if(p&&"boolean"!=typeof p&&!n(p)){let e=Array.isArray(p)?p:[p];for(let i=0;i<e.length;i++){let r=s(t,e[i]);if(r){let{transitionEnd:t,transition:e,...i}=r;for(let t in i){let e=i[t];if(Array.isArray(e)){let t=d?e.length-1:0;e=e[t]}null!==e&&(o[t]=e)}for(let e in t)o[e]=t[e]}}}return o}(r,o,a,t),renderState:e()};return i&&(l.onMount=t=>i({props:r,current:t,...l}),l.onUpdate=t=>i(t)),l})(t,e,r,o);return i?a():(0,n4.M)(a)};function n9(t,e,i){var r;let{style:n}=t,s={};for(let o in n)(to(n[o])||e.style&&to(e.style[o])||nN(o,t)||(null===(r=null==i?void 0:i.getValue(o))||void 0===r?void 0:r.liveStyle)!==void 0)&&(s[o]=n[o]);return s}let n7={useVisualState:n5({scrapeMotionValuesFromProps:n9,createRenderState:nY})};function n8(t,e){try{e.dimensions="function"==typeof t.getBBox?t.getBBox():t.getBoundingClientRect()}catch(t){e.dimensions={x:0,y:0,width:0,height:0}}}function st(t,{style:e,vars:i},r,n){for(let s in Object.assign(t.style,e,n&&n.getProjectionStyles(r)),i)t.style.setProperty(s,i[s])}let se=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function si(t,e,i,r){for(let i in st(t,e,void 0,r),e.attrs)t.setAttribute(se.has(i)?i:tl(i),e.attrs[i])}function sr(t,e,i){let r=n9(t,e,i);for(let i in t)(to(t[i])||to(e[i]))&&(r[-1!==F.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return r}let sn=["x","y","width","height","cx","cy","r"],ss={useVisualState:n5({scrapeMotionValuesFromProps:sr,createRenderState:n3,onUpdate:({props:t,prevProps:e,current:i,renderState:r,latestValues:n})=>{if(!i)return;let s=!!t.drag;if(!s){for(let t in n)if(I.has(t)){s=!0;break}}if(!s)return;let o=!e;if(e)for(let i=0;i<sn.length;i++){let r=sn[i];t[r]!==e[r]&&(o=!0)}o&&K.read(()=>{n8(i,r),K.render(()=>{n2(r,n,n6(i.tagName),t.transformTemplate),si(i,r)})})}})},so={current:null},sa={current:!1},sl=[...eb,tW,tJ],su=t=>sl.find(ex(t)),sh=new WeakMap,sc=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class sd{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:r,blockInitialAnimation:n,visualState:s},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=ec,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=Q.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,K.render(this.render,!1,!0))};let{latestValues:a,renderState:l,onUpdate:u}=s;this.onUpdate=u,this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=r,this.options=o,this.blockInitialAnimation=!!n,this.isControllingVariants=nO(e),this.isVariantNode=nR(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:h,...c}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in c){let e=c[t];void 0!==a[t]&&to(e)&&e.set(a[t],!1)}}mount(t){this.current=t,sh.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),sa.current||function(){if(sa.current=!0,nL.B){if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>so.current=t.matches;t.addListener(e),e()}else so.current=!1}}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||so.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in this.projection&&this.projection.unmount(),X(this.notifyUpdate),X(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let r=I.has(t);r&&this.onBindTransform&&this.onBindTransform();let n=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&K.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0)}),s=e.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{n(),s(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in nF){let e=nF[t];if(!e)continue;let{isEnabled:i,Feature:r}=e;if(!this.features[t]&&r&&i(this.props)&&(this.features[t]=new r(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):i$()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<sc.length;e++){let i=sc[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let r=t["on"+i];r&&(this.propEventSubscriptions[i]=this.on(i,r))}this.prevMotionValues=function(t,e,i){for(let r in e){let n=e[r],s=i[r];if(to(n))t.addValue(r,n);else if(to(s))t.addValue(r,ts(n,{owner:t}));else if(s!==n){if(t.hasValue(r)){let e=t.getValue(r);!0===e.liveStyle?e.jump(n):e.hasAnimated||e.set(n)}else{let e=t.getStaticValue(r);t.addValue(r,ts(void 0!==e?e:n,{owner:t}))}}}for(let r in i)void 0===e[r]&&t.removeValue(r);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue(),this.onUpdate&&this.onUpdate(this)}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=ts(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){var i;let r=void 0===this.latestValues[t]&&this.current?null!==(i=this.getBaseTargetFromProps(this.props,t))&&void 0!==i?i:this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=r&&("string"==typeof r&&(ed(r)||tw(r))?r=parseFloat(r):!su(r)&&tJ.test(e)&&(r=t7(t,e)),this.setBaseTarget(t,to(r)?r.get():r)),to(r)?r.get():r}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){var e;let i;let{initial:r}=this.props;if("string"==typeof r||"object"==typeof r){let n=s(this.props,r,null===(e=this.presenceContext)||void 0===e?void 0:e.custom);n&&(i=n[t])}if(r&&void 0!==i)return i;let n=this.getBaseTargetFromProps(this.props,t);return void 0===n||to(n)?void 0!==this.initialValues[t]&&void 0===i?void 0:this.baseTarget[t]:n}on(t,e){return this.events[t]||(this.events[t]=new te),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class sp extends sd{constructor(){super(...arguments),this.KeyframeResolver=ew}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;to(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}class sf extends sp{constructor(){super(...arguments),this.type="html",this.renderInstance=st}readValueFromInstance(t,e){if(I.has(e)){let t=t9(e);return t&&t.default||0}{let i=window.getComputedStyle(t),r=(ef(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof r?r.trim():r}}measureInstanceViewportBox(t,{transformPagePoint:e}){return i1(t,e)}build(t,e,i){nH(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return n9(t,e,i)}}class sm extends sp{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=i$,this.updateDimensions=()=>{this.current&&!this.renderState.dimensions&&n8(this.current,this.renderState)}}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(I.has(e)){let t=t9(e);return t&&t.default||0}return e=se.has(e)?e:tl(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return sr(t,e,i)}onBindTransform(){this.current&&!this.renderState.dimensions&&K.postRender(this.updateDimensions)}build(t,e,i){n2(t,e,this.isSVGTag,i.transformTemplate)}renderInstance(t,e,i,r){si(t,e,i,r)}mount(t){this.isSVGTag=n6(t.tagName),super.mount(t)}}let sv=function(t){if("undefined"==typeof Proxy)return t;let e=new Map;return new Proxy((...e)=>t(...e),{get:(i,r)=>"create"===r?t:(e.has(r)||e.set(r,t(r)),e.get(r))})}((rl={animation:{Feature:iA},exit:{Feature:iE},inView:{Feature:nC},tap:{Feature:nw},focus:{Feature:nb},hover:{Feature:nx},pan:{Feature:ra},drag:{Feature:rs,ProjectionNode:ng,MeasureLayout:rw},layout:{ProjectionNode:ng,MeasureLayout:rw}},ru=(t,e)=>nQ(t)?new sm(e):new sf(e,{allowProjection:t!==rc.Fragment}),function(t,{forwardMotionProps:e}={forwardMotionProps:!1}){return function({preloadedFeatures:t,createVisualElement:e,useRender:i,useVisualState:r,Component:n}){var s,o;function a(t,s){var o;let a;let l={...(0,rc.useContext)(nV.Q),...t,layoutId:function({layoutId:t}){let e=(0,rc.useContext)(rp.L).id;return e&&void 0!==t?e+"-"+t:t}(t)},{isStatic:u}=l,h=function(t){let{initial:e,animate:i}=function(t,e){if(nO(t)){let{initial:e,animate:i}=t;return{initial:!1===e||iv(e)?e:void 0,animate:iv(i)?i:void 0}}return!1!==t.inherit?e:{}}(t,(0,rc.useContext)(nD));return(0,rc.useMemo)(()=>({initial:e,animate:i}),[nk(e),nk(i)])}(t),c=r(t,u);if(!u&&nL.B){(0,rc.useContext)(nj).strict;let t=function(t){let{drag:e,layout:i}=nF;if(!e&&!i)return{};let r={...e,...i};return{MeasureLayout:(null==e?void 0:e.isEnabled(t))||(null==i?void 0:i.isEnabled(t))?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}(l);a=t.MeasureLayout,h.visualElement=function(t,e,i,r,n){var s,o;let{visualElement:a}=(0,rc.useContext)(nD),l=(0,rc.useContext)(nj),u=(0,rc.useContext)(nU.t),h=(0,rc.useContext)(nV.Q).reducedMotion,c=(0,rc.useRef)(null);r=r||l.renderer,!c.current&&r&&(c.current=r(t,{visualState:e,parent:a,props:i,presenceContext:u,blockInitialAnimation:!!u&&!1===u.initial,reducedMotionConfig:h}));let d=c.current,p=(0,rc.useContext)(rf);d&&!d.projection&&n&&("html"===d.type||"svg"===d.type)&&function(t,e,i,r){let{layoutId:n,layout:s,drag:o,dragConstraints:a,layoutScroll:l,layoutRoot:u}=e;t.projection=new i(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:n,layout:s,alwaysMeasureLayout:!!o||a&&i0(a),visualElement:t,animationType:"string"==typeof s?s:"both",initialPromotionConfig:r,layoutScroll:l,layoutRoot:u})}(c.current,i,n,p);let f=(0,rc.useRef)(!1);(0,rc.useInsertionEffect)(()=>{d&&f.current&&d.update(i,u)});let m=i[tu],v=(0,rc.useRef)(!!m&&!(null===(s=window.MotionHandoffIsComplete)||void 0===s?void 0:s.call(window,m))&&(null===(o=window.MotionHasOptimisedAnimation)||void 0===o?void 0:o.call(window,m)));return(0,nz.E)(()=>{d&&(f.current=!0,window.MotionIsMounted=!0,d.updateFeatures(),rx.render(d.render),v.current&&d.animationState&&d.animationState.animateChanges())}),(0,rc.useEffect)(()=>{d&&(!v.current&&d.animationState&&d.animationState.animateChanges(),v.current&&(queueMicrotask(()=>{var t;null===(t=window.MotionHandoffMarkAsComplete)||void 0===t||t.call(window,m)}),v.current=!1))}),d}(n,c,l,e,t.ProjectionNode)}return(0,rh.jsxs)(nD.Provider,{value:h,children:[a&&h.visualElement?(0,rh.jsx)(a,{visualElement:h.visualElement,...l}):null,i(n,t,(o=h.visualElement,(0,rc.useCallback)(t=>{t&&c.onMount&&c.onMount(t),o&&(t?o.mount(t):o.unmount()),s&&("function"==typeof s?s(t):i0(s)&&(s.current=t))},[o])),c,u,h.visualElement)]})}t&&function(t){for(let e in t)nF[e]={...nF[e],...t[e]}}(t),a.displayName=`motion.${"string"==typeof n?n:`create(${null!==(o=null!==(s=n.displayName)&&void 0!==s?s:n.name)&&void 0!==o?o:""})`}`;let l=(0,rc.forwardRef)(a);return l[nI]=n,l}({...nQ(t)?ss:n7,preloadedFeatures:rl,useRender:function(t=!1){return(e,i,r,{latestValues:n},s)=>{let o=(nQ(e)?function(t,e,i,r){let n=(0,rc.useMemo)(()=>{let i=n3();return n2(i,e,n6(r),t.transformTemplate),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};nK(e,t.style,t),n.style={...e,...n.style}}return n}:function(t,e){let i={},r=function(t,e){let i=t.style||{},r={};return nK(r,i,t),Object.assign(r,function({transformTemplate:t},e){return(0,rc.useMemo)(()=>{let i=nY();return nH(i,e,t),Object.assign({},i.vars,i.style)},[e])}(t,e)),r}(t,e);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=r,i})(i,n,s,e),a=function(t,e,i){let r={};for(let n in t)("values"!==n||"object"!=typeof t.values)&&(nG(n)||!0===i&&nq(n)||!e&&!nq(n)||t.draggable&&n.startsWith("onDrag"))&&(r[n]=t[n]);return r}(i,"string"==typeof e,t),l=e!==rc.Fragment?{...a,...o,ref:r}:{},{children:u}=i,h=(0,rc.useMemo)(()=>to(u)?u.get():u,[u]);return(0,rc.createElement)(e,{...l,children:h})}}(e),createVisualElement:ru,Component:t})}))},3969:(t,e,i)=>{"use strict";i.d(e,{B:()=>r});let r="undefined"!=typeof window},5248:(t,e,i)=>{"use strict";i.d(e,{M:()=>n});var r=i(8009);function n(t){let e=(0,r.useRef)(null);return null===e.current&&(e.current=t()),e.current}},3607:(t,e,i)=>{"use strict";i.d(e,{E:()=>n});var r=i(8009);let n=i(3969).B?r.useLayoutEffect:r.useEffect},4098:(t,e,i)=>{"use strict";i.d(e,{Z0P:()=>p,OXb:()=>f,ymh:()=>m,CMH:()=>v,w_X:()=>g,Cab:()=>y,SMR:()=>x,QCr:()=>b,xdT:()=>P,PYU:()=>w,EcP:()=>d,Ph:()=>T});var r=i(8009),n={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},s=r.createContext&&r.createContext(n),o=["attr","size","title"];function a(){return(a=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var i=arguments[e];for(var r in i)Object.prototype.hasOwnProperty.call(i,r)&&(t[r]=i[r])}return t}).apply(this,arguments)}function l(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),i.push.apply(i,r)}return i}function u(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?l(Object(i),!0).forEach(function(e){var r,n;r=e,n=i[e],(r=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var r=i.call(t,e||"default");if("object"!=typeof r)return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(r))in t?Object.defineProperty(t,r,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[r]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):l(Object(i)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))})}return t}function h(t){return e=>r.createElement(c,a({attr:u({},t.attr)},e),function t(e){return e&&e.map((e,i)=>r.createElement(e.tag,u({key:i},e.attr),t(e.child)))}(t.child))}function c(t){var e=e=>{var i,{attr:n,size:s,title:l}=t,h=function(t,e){if(null==t)return{};var i,r,n=function(t,e){if(null==t)return{};var i={};for(var r in t)if(Object.prototype.hasOwnProperty.call(t,r)){if(e.indexOf(r)>=0)continue;i[r]=t[r]}return i}(t,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(t);for(r=0;r<s.length;r++)i=s[r],!(e.indexOf(i)>=0)&&Object.prototype.propertyIsEnumerable.call(t,i)&&(n[i]=t[i])}return n}(t,o),c=s||e.size||"1em";return e.className&&(i=e.className),t.className&&(i=(i?i+" ":"")+t.className),r.createElement("svg",a({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},e.attr,n,h,{className:i,style:u(u({color:t.color||e.color},e.style),t.style),height:c,width:c,xmlns:"http://www.w3.org/2000/svg"}),l&&r.createElement("title",null,l),t.children)};return void 0!==s?r.createElement(s.Consumer,null,t=>e(t)):e(n)}function d(t){return h({tag:"svg",attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M380.9 97.1C339 55.1 283.2 32 223.9 32c-122.4 0-222 99.6-222 222 0 39.1 10.2 77.3 29.6 111L0 480l117.7-30.9c32.4 17.7 68.9 27 106.1 27h.1c122.3 0 224.1-99.6 224.1-222 0-59.3-25.2-115-67.1-157zm-157 341.6c-33.2 0-65.7-8.9-94-25.7l-6.7-4-69.8 18.3L72 359.2l-4.4-7c-18.5-29.4-28.2-63.3-28.2-98.2 0-101.7 82.8-184.5 184.6-184.5 49.3 0 95.6 19.2 130.4 54.1 34.8 34.9 56.2 81.2 56.1 130.5 0 101.8-84.9 184.6-186.6 184.6zm101.2-138.2c-5.5-2.8-32.8-16.2-37.9-18-5.1-1.9-8.8-2.8-12.5 2.8-3.7 5.6-14.3 18-17.6 21.8-3.2 3.7-6.5 4.2-12 1.4-32.6-16.3-54-29.1-75.5-66-5.7-9.8 5.7-9.1 16.3-30.3 1.8-3.7.9-6.9-.5-9.7-1.4-2.8-12.5-30.1-17.1-41.2-4.5-10.8-9.1-9.3-12.5-9.5-3.2-.2-6.9-.2-10.6-.2-3.7 0-9.7 1.4-14.8 6.9-5.1 5.6-19.4 19-19.4 46.3 0 27.3 19.9 53.7 22.6 57.4 2.8 3.7 39.1 59.7 94.8 83.8 35.2 15.2 49 16.5 66.6 13.9 10.7-1.6 32.8-13.4 37.4-26.4 4.6-13 4.6-24.1 3.2-26.4-1.3-2.5-5-3.9-10.5-6.6z"},child:[]}]})(t)}function p(t){return h({tag:"svg",attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M190.5 66.9l22.2-22.2c9.4-9.4 24.6-9.4 33.9 0L441 239c9.4 9.4 9.4 24.6 0 33.9L246.6 467.3c-9.4 9.4-24.6 9.4-33.9 0l-22.2-22.2c-9.5-9.5-9.3-25 .4-34.3L311.4 296H24c-13.3 0-24-10.7-24-24v-32c0-13.3 10.7-24 24-24h287.4L190.9 101.2c-9.8-9.3-10-24.8-.4-34.3z"},child:[]}]})(t)}function f(t){return h({tag:"svg",attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M16 132h416c8.837 0 16-7.163 16-16V76c0-8.837-7.163-16-16-16H16C7.163 60 0 67.163 0 76v40c0 8.837 7.163 16 16 16zm0 160h416c8.837 0 16-7.163 16-16v-40c0-8.837-7.163-16-16-16H16c-8.837 0-16 7.163-16 16v40c0 8.837 7.163 16 16 16zm0 160h416c8.837 0 16-7.163 16-16v-40c0-8.837-7.163-16-16-16H16c-8.837 0-16 7.163-16 16v40c0 8.837 7.163 16 16 16z"},child:[]}]})(t)}function m(t){return h({tag:"svg",attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M436 480h-20V24c0-13.255-10.745-24-24-24H56C42.745 0 32 10.745 32 24v456H12c-6.627 0-12 5.373-12 12v20h448v-20c0-6.627-5.373-12-12-12zM128 76c0-6.627 5.373-12 12-12h40c6.627 0 12 5.373 12 12v40c0 6.627-5.373 12-12 12h-40c-6.627 0-12-5.373-12-12V76zm0 96c0-6.627 5.373-12 12-12h40c6.627 0 12 5.373 12 12v40c0 6.627-5.373 12-12 12h-40c-6.627 0-12-5.373-12-12v-40zm52 148h-40c-6.627 0-12-5.373-12-12v-40c0-6.627 5.373-12 12-12h40c6.627 0 12 5.373 12 12v40c0 6.627-5.373 12-12 12zm76 160h-64v-84c0-6.627 5.373-12 12-12h40c6.627 0 12 5.373 12 12v84zm64-172c0 6.627-5.373 12-12 12h-40c-6.627 0-12-5.373-12-12v-40c0-6.627 5.373-12 12-12h40c6.627 0 12 5.373 12 12v40zm0-96c0 6.627-5.373 12-12 12h-40c-6.627 0-12-5.373-12-12v-40c0-6.627 5.373-12 12-12h40c6.627 0 12 5.373 12 12v40zm0-96c0 6.627-5.373 12-12 12h-40c-6.627 0-12-5.373-12-12V76c0-6.627 5.373-12 12-12h40c6.627 0 12 5.373 12 12v40z"},child:[]}]})(t)}function v(t){return h({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M173.898 439.404l-166.4-166.4c-9.997-9.997-9.997-26.206 0-36.204l36.203-36.204c9.997-9.998 26.207-9.998 36.204 0L192 312.69 432.095 72.596c9.997-9.997 26.207-9.997 36.204 0l36.203 36.204c9.997 9.997 9.997 26.206 0 36.204l-294.4 294.401c-9.998 9.997-26.207 9.997-36.204-.001z"},child:[]}]})(t)}function g(t){return h({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M256,8C119,8,8,119,8,256S119,504,256,504,504,393,504,256,393,8,256,8Zm92.49,313h0l-20,25a16,16,0,0,1-22.49,2.5h0l-67-49.72a40,40,0,0,1-15-31.23V112a16,16,0,0,1,16-16h32a16,16,0,0,1,16,16V256l58,42.5A16,16,0,0,1,348.49,321Z"},child:[]}]})(t)}function y(t){return h({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M493.4 24.6l-104-24c-11.3-2.6-22.9 3.3-27.5 13.9l-48 112c-4.2 9.8-1.4 21.3 6.9 28l60.6 49.6c-36 76.7-98.9 140.5-177.2 177.2l-49.6-60.6c-6.8-8.3-18.2-11.1-28-6.9l-112 48C3.9 366.5-2 378.1.6 389.4l24 104C27.1 504.2 36.7 512 48 512c256.1 0 464-207.5 464-464 0-11.2-7.7-20.9-18.6-23.4z"},child:[]}]})(t)}function x(t){return h({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M466.5 83.7l-192-80a48.15 48.15 0 0 0-36.9 0l-192 80C27.7 91.1 16 108.6 16 128c0 198.5 114.5 335.7 221.5 380.3 11.8 4.9 25.1 4.9 36.9 0C360.1 472.6 496 349.3 496 128c0-19.4-11.7-36.9-29.5-44.3zM256.1 446.3l-.1-381 175.9 73.3c-3.3 151.4-82.1 261.1-175.8 307.7z"},child:[]}]})(t)}function b(t){return h({tag:"svg",attr:{viewBox:"0 0 352 512"},child:[{tag:"path",attr:{d:"M242.72 256l100.07-100.07c12.28-12.28 12.28-32.19 0-44.48l-22.24-22.24c-12.28-12.28-32.19-12.28-44.48 0L176 189.28 75.93 89.21c-12.28-12.28-32.19-12.28-44.48 0L9.21 111.45c-12.28 12.28-12.28 32.19 0 44.48L109.28 256 9.21 356.07c-12.28 12.28-12.28 32.19 0 44.48l22.24 22.24c12.28 12.28 32.2 12.28 44.48 0L176 322.72l100.07 100.07c12.28 12.28 32.2 12.28 44.48 0l22.24-22.24c12.28-12.28 12.28-32.19 0-44.48L242.72 256z"},child:[]}]})(t)}function P(t){return h({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M501.1 395.7L384 278.6c-23.1-23.1-57.6-27.6-85.4-13.9L192 158.1V96L64 0 0 64l96 128h62.1l106.6 106.6c-13.6 27.8-9.2 62.3 13.9 85.4l117.1 117.1c14.6 14.6 38.2 14.6 52.7 0l52.7-52.7c14.5-14.6 14.5-38.2 0-52.7zM331.7 225c28.3 0 54.9 11 74.9 31l19.4 19.4c15.8-6.9 30.8-16.5 43.8-29.5 37.1-37.1 49.7-89.3 37.9-136.7-2.2-9-13.5-12.1-20.1-5.5l-74.4 74.4-67.9-11.3L334 98.9l74.4-74.4c6.6-6.6 3.4-17.9-5.7-20.2-47.4-11.7-99.6.9-136.6 37.9-28.5 28.5-41.9 66.1-41.2 103.6l82.1 82.1c8.1-1.9 16.5-2.9 24.7-2.9zm-103.9 82l-56.7-56.7L18.7 402.8c-25 25-25 65.5 0 90.5s65.5 25 90.5 0l123.6-123.6c-7.6-19.9-9.9-41.6-5-62.7zM64 472c-13.2 0-24-10.8-24-24 0-13.3 10.7-24 24-24s24 10.7 24 24c0 13.2-10.7 24-24 24z"},child:[]}]})(t)}function w(t){return h({tag:"svg",attr:{viewBox:"0 0 576 512"},child:[{tag:"path",attr:{d:"M562.1 383.9c-21.5-2.4-42.1-10.5-57.9-22.9-14.1-11.1-34.2-11.3-48.2 0-37.9 30.4-107.2 30.4-145.7-1.5-13.5-11.2-33-9.1-46.7 1.8-38 30.1-106.9 30-145.2-1.7-13.5-11.2-33.3-8.9-47.1 2-15.5 12.2-36 20.1-57.7 22.4-7.9.8-13.6 7.8-13.6 15.7v32.2c0 9.1 7.6 16.8 16.7 16 28.8-2.5 56.1-11.4 79.4-25.9 56.5 34.6 137 34.1 192 0 56.5 34.6 137 34.1 192 0 23.3 14.2 50.9 23.3 79.1 25.8 9.1.8 16.7-6.9 16.7-16v-31.6c.1-8-5.7-15.4-13.8-16.3zm0-144c-21.5-2.4-42.1-10.5-57.9-22.9-14.1-11.1-34.2-11.3-48.2 0-37.9 30.4-107.2 30.4-145.7-1.5-13.5-11.2-33-9.1-46.7 1.8-38 30.1-106.9 30-145.2-1.7-13.5-11.2-33.3-8.9-47.1 2-15.5 12.2-36 20.1-57.7 22.4-7.9.8-13.6 7.8-13.6 15.7v32.2c0 9.1 7.6 16.8 16.7 16 28.8-2.5 56.1-11.4 79.4-25.9 56.5 34.6 137 34.1 192 0 56.5 34.6 137 34.1 192 0 23.3 14.2 50.9 23.3 79.1 25.8 9.1.8 16.7-6.9 16.7-16v-31.6c.1-8-5.7-15.4-13.8-16.3zm0-144C540.6 93.4 520 85.4 504.2 73 490.1 61.9 470 61.7 456 73c-37.9 30.4-107.2 30.4-145.7-1.5-13.5-11.2-33-9.1-46.7 1.8-38 30.1-106.9 30-145.2-1.7-13.5-11.2-33.3-8.9-47.1 2-15.5 12.2-36 20.1-57.7 22.4-7.9.8-13.6 7.8-13.6 15.7v32.2c0 9.1 7.6 16.8 16.7 16 28.8-2.5 56.1-11.4 79.4-25.9 56.5 34.6 137 34.1 192 0 56.5 34.6 137 34.1 192 0 23.3 14.2 50.9 23.3 79.1 25.8 9.1.8 16.7-6.9 16.7-16v-31.6c.1-8-5.7-15.4-13.8-16.3z"},child:[]}]})(t)}function T(t){return h({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M507.73 109.1c-2.24-9.03-13.54-12.09-20.12-5.51l-74.36 74.36-67.88-11.31-11.31-67.88 74.36-74.36c6.62-6.62 3.43-17.9-5.66-20.16-47.38-11.74-99.55.91-136.58 37.93-39.64 39.64-50.55 97.1-34.05 147.2L18.74 402.76c-24.99 24.99-24.99 65.51 0 90.5 24.99 24.99 65.51 24.99 90.5 0l213.21-213.21c50.12 16.71 107.47 5.68 147.37-34.22 37.07-37.07 49.7-89.32 37.91-136.73zM64 472c-13.25 0-24-10.75-24-24 0-13.26 10.75-24 24-24s24 10.74 24 24c0 13.25-10.75 24-24 24z"},child:[]}]})(t)}},82:(t,e,i)=>{"use strict";i.d(e,{uN:()=>d,maD:()=>p,Kni:()=>f,vq8:()=>m,Cab:()=>v,xdT:()=>g,PYU:()=>y});var r=i(6301),n={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},s=r.createContext&&r.createContext(n),o=["attr","size","title"];function a(){return(a=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var i=arguments[e];for(var r in i)Object.prototype.hasOwnProperty.call(i,r)&&(t[r]=i[r])}return t}).apply(this,arguments)}function l(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),i.push.apply(i,r)}return i}function u(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?l(Object(i),!0).forEach(function(e){var r,n;r=e,n=i[e],(r=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var r=i.call(t,e||"default");if("object"!=typeof r)return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(r))in t?Object.defineProperty(t,r,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[r]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):l(Object(i)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))})}return t}function h(t){return e=>r.createElement(c,a({attr:u({},t.attr)},e),function t(e){return e&&e.map((e,i)=>r.createElement(e.tag,u({key:i},e.attr),t(e.child)))}(t.child))}function c(t){var e=e=>{var i,{attr:n,size:s,title:l}=t,h=function(t,e){if(null==t)return{};var i,r,n=function(t,e){if(null==t)return{};var i={};for(var r in t)if(Object.prototype.hasOwnProperty.call(t,r)){if(e.indexOf(r)>=0)continue;i[r]=t[r]}return i}(t,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(t);for(r=0;r<s.length;r++)i=s[r],!(e.indexOf(i)>=0)&&Object.prototype.propertyIsEnumerable.call(t,i)&&(n[i]=t[i])}return n}(t,o),c=s||e.size||"1em";return e.className&&(i=e.className),t.className&&(i=(i?i+" ":"")+t.className),r.createElement("svg",a({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},e.attr,n,h,{className:i,style:u(u({color:t.color||e.color},e.style),t.style),height:c,width:c,xmlns:"http://www.w3.org/2000/svg"}),l&&r.createElement("title",null,l),t.children)};return void 0!==s?r.createElement(s.Consumer,null,t=>e(t)):e(n)}function d(t){return h({tag:"svg",attr:{viewBox:"0 0 576 512"},child:[{tag:"path",attr:{d:"M416 192c0-88.4-93.1-160-208-160S0 103.6 0 192c0 34.3 14.1 65.9 38 92-13.4 30.2-35.5 54.2-35.8 54.5-2.2 2.3-2.8 5.7-1.5 8.7S4.8 352 8 352c36.6 0 66.9-12.3 88.7-25 32.2 15.7 70.3 25 111.3 25 114.9 0 208-71.6 208-160zm122 220c23.9-26 38-57.7 38-92 0-66.9-53.5-124.2-129.3-148.1.9 6.6 1.3 13.3 1.3 20.1 0 105.9-107.7 192-240 192-10.8 0-21.3-.8-31.7-1.9C207.8 439.6 281.8 480 368 480c41 0 79.1-9.2 111.3-25 21.8 12.7 52.1 25 88.7 25 3.2 0 6.1-1.9 7.3-4.8 1.3-2.9.7-6.3-1.5-8.7-.3-.3-22.4-24.2-35.8-54.5z"},child:[]}]})(t)}function p(t){return h({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M502.3 190.8c3.9-3.1 9.7-.2 9.7 4.7V400c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48V195.6c0-5 5.7-7.8 9.7-4.7 22.4 17.4 52.1 39.5 154.1 113.6 21.1 15.4 56.7 47.8 92.2 47.6 35.7.3 72-32.8 92.3-47.6 102-74.1 131.6-96.3 154-113.7zM256 320c23.2.4 56.6-29.2 73.4-41.4 132.7-96.3 142.8-104.7 173.4-128.7 5.8-4.5 9.2-11.5 9.2-18.9v-19c0-26.5-21.5-48-48-48H48C21.5 64 0 85.5 0 112v19c0 7.4 3.4 14.3 9.2 18.9 30.6 23.9 40.7 32.4 173.4 128.7 16.8 12.2 50.2 41.8 73.4 41.4z"},child:[]}]})(t)}function f(t){return h({tag:"svg",attr:{viewBox:"0 0 576 512"},child:[{tag:"path",attr:{d:"M571.31 193.94l-22.63-22.63c-6.25-6.25-16.38-6.25-22.63 0l-11.31 11.31-28.9-28.9c5.63-21.31.36-44.9-16.35-61.61l-45.25-45.25c-62.48-62.48-163.79-62.48-226.28 0l90.51 45.25v18.75c0 16.97 6.74 33.25 18.75 45.25l49.14 49.14c16.71 16.71 40.3 21.98 61.61 16.35l28.9 28.9-11.31 11.31c-6.25 6.25-6.25 16.38 0 22.63l22.63 22.63c6.25 6.25 16.38 6.25 22.63 0l90.51-90.51c6.23-6.24 6.23-16.37-.02-22.62zm-286.72-15.2c-3.7-3.7-6.84-7.79-9.85-11.95L19.64 404.96c-25.57 23.88-26.26 64.19-1.53 88.93s65.05 24.05 88.93-1.53l238.13-255.07c-3.96-2.91-7.9-5.87-11.44-9.41l-49.14-49.14z"},child:[]}]})(t)}function m(t){return h({tag:"svg",attr:{viewBox:"0 0 384 512"},child:[{tag:"path",attr:{d:"M172.268 501.67C26.97 291.031 0 269.413 0 192 0 85.961 85.961 0 192 0s192 85.961 192 192c0 77.413-26.97 99.031-172.268 309.67-9.535 13.774-29.93 13.773-39.464 0zM192 272c44.183 0 80-35.817 80-80s-35.817-80-80-80-80 35.817-80 80 35.817 80 80 80z"},child:[]}]})(t)}function v(t){return h({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M493.4 24.6l-104-24c-11.3-2.6-22.9 3.3-27.5 13.9l-48 112c-4.2 9.8-1.4 21.3 6.9 28l60.6 49.6c-36 76.7-98.9 140.5-177.2 177.2l-49.6-60.6c-6.8-8.3-18.2-11.1-28-6.9l-112 48C3.9 366.5-2 378.1.6 389.4l24 104C27.1 504.2 36.7 512 48 512c256.1 0 464-207.5 464-464 0-11.2-7.7-20.9-18.6-23.4z"},child:[]}]})(t)}function g(t){return h({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M501.1 395.7L384 278.6c-23.1-23.1-57.6-27.6-85.4-13.9L192 158.1V96L64 0 0 64l96 128h62.1l106.6 106.6c-13.6 27.8-9.2 62.3 13.9 85.4l117.1 117.1c14.6 14.6 38.2 14.6 52.7 0l52.7-52.7c14.5-14.6 14.5-38.2 0-52.7zM331.7 225c28.3 0 54.9 11 74.9 31l19.4 19.4c15.8-6.9 30.8-16.5 43.8-29.5 37.1-37.1 49.7-89.3 37.9-136.7-2.2-9-13.5-12.1-20.1-5.5l-74.4 74.4-67.9-11.3L334 98.9l74.4-74.4c6.6-6.6 3.4-17.9-5.7-20.2-47.4-11.7-99.6.9-136.6 37.9-28.5 28.5-41.9 66.1-41.2 103.6l82.1 82.1c8.1-1.9 16.5-2.9 24.7-2.9zm-103.9 82l-56.7-56.7L18.7 402.8c-25 25-25 65.5 0 90.5s65.5 25 90.5 0l123.6-123.6c-7.6-19.9-9.9-41.6-5-62.7zM64 472c-13.2 0-24-10.8-24-24 0-13.3 10.7-24 24-24s24 10.7 24 24c0 13.2-10.7 24-24 24z"},child:[]}]})(t)}function y(t){return h({tag:"svg",attr:{viewBox:"0 0 576 512"},child:[{tag:"path",attr:{d:"M562.1 383.9c-21.5-2.4-42.1-10.5-57.9-22.9-14.1-11.1-34.2-11.3-48.2 0-37.9 30.4-107.2 30.4-145.7-1.5-13.5-11.2-33-9.1-46.7 1.8-38 30.1-106.9 30-145.2-1.7-13.5-11.2-33.3-8.9-47.1 2-15.5 12.2-36 20.1-57.7 22.4-7.9.8-13.6 7.8-13.6 15.7v32.2c0 9.1 7.6 16.8 16.7 16 28.8-2.5 56.1-11.4 79.4-25.9 56.5 34.6 137 34.1 192 0 56.5 34.6 137 34.1 192 0 23.3 14.2 50.9 23.3 79.1 25.8 9.1.8 16.7-6.9 16.7-16v-31.6c.1-8-5.7-15.4-13.8-16.3zm0-144c-21.5-2.4-42.1-10.5-57.9-22.9-14.1-11.1-34.2-11.3-48.2 0-37.9 30.4-107.2 30.4-145.7-1.5-13.5-11.2-33-9.1-46.7 1.8-38 30.1-106.9 30-145.2-1.7-13.5-11.2-33.3-8.9-47.1 2-15.5 12.2-36 20.1-57.7 22.4-7.9.8-13.6 7.8-13.6 15.7v32.2c0 9.1 7.6 16.8 16.7 16 28.8-2.5 56.1-11.4 79.4-25.9 56.5 34.6 137 34.1 192 0 56.5 34.6 137 34.1 192 0 23.3 14.2 50.9 23.3 79.1 25.8 9.1.8 16.7-6.9 16.7-16v-31.6c.1-8-5.7-15.4-13.8-16.3zm0-144C540.6 93.4 520 85.4 504.2 73 490.1 61.9 470 61.7 456 73c-37.9 30.4-107.2 30.4-145.7-1.5-13.5-11.2-33-9.1-46.7 1.8-38 30.1-106.9 30-145.2-1.7-13.5-11.2-33.3-8.9-47.1 2-15.5 12.2-36 20.1-57.7 22.4-7.9.8-13.6 7.8-13.6 15.7v32.2c0 9.1 7.6 16.8 16.7 16 28.8-2.5 56.1-11.4 79.4-25.9 56.5 34.6 137 34.1 192 0 56.5 34.6 137 34.1 192 0 23.3 14.2 50.9 23.3 79.1 25.8 9.1.8 16.7-6.9 16.7-16v-31.6c.1-8-5.7-15.4-13.8-16.3z"},child:[]}]})(t)}}};