[{"/home/<USER>/CascadeProjects/inparo-web/src/app/[lang]/contact/ContactClient.tsx": "1", "/home/<USER>/CascadeProjects/inparo-web/src/app/[lang]/contact/page.tsx": "2", "/home/<USER>/CascadeProjects/inparo-web/src/app/[lang]/emergency/page.tsx": "3", "/home/<USER>/CascadeProjects/inparo-web/src/app/[lang]/layout.tsx": "4", "/home/<USER>/CascadeProjects/inparo-web/src/app/[lang]/page.tsx": "5", "/home/<USER>/CascadeProjects/inparo-web/src/app/[lang]/services/ServicesClient.tsx": "6", "/home/<USER>/CascadeProjects/inparo-web/src/app/[lang]/services/page.tsx": "7", "/home/<USER>/CascadeProjects/inparo-web/src/app/layout.tsx": "8", "/home/<USER>/CascadeProjects/inparo-web/src/app/providers.tsx": "9", "/home/<USER>/CascadeProjects/inparo-web/src/components/ClientWrapper.tsx": "10", "/home/<USER>/CascadeProjects/inparo-web/src/components/animations/Fade.tsx": "11", "/home/<USER>/CascadeProjects/inparo-web/src/components/animations/FadeInView.tsx": "12", "/home/<USER>/CascadeProjects/inparo-web/src/components/footer/Footer.tsx": "13", "/home/<USER>/CascadeProjects/inparo-web/src/components/forms/EmergencyContactForm.tsx": "14", "/home/<USER>/CascadeProjects/inparo-web/src/components/layout/Header.tsx": "15", "/home/<USER>/CascadeProjects/inparo-web/src/components/navigation/Navigation.tsx": "16", "/home/<USER>/CascadeProjects/inparo-web/src/components/navigation/NavigationWrapper.tsx": "17", "/home/<USER>/CascadeProjects/inparo-web/src/components/services/CategorySection.tsx": "18", "/home/<USER>/CascadeProjects/inparo-web/src/components/services/ServiceCard.tsx": "19", "/home/<USER>/CascadeProjects/inparo-web/src/components/services/ServiceIcons.tsx": "20", "/home/<USER>/CascadeProjects/inparo-web/src/components/shared/LanguageSelector.tsx": "21", "/home/<USER>/CascadeProjects/inparo-web/src/i18n/server.ts": "22", "/home/<USER>/CascadeProjects/inparo-web/src/i18n/settings.ts": "23", "/home/<USER>/CascadeProjects/inparo-web/src/i18n/useTranslation.ts": "24", "/home/<USER>/CascadeProjects/inparo-web/src/middleware.ts": "25", "/home/<USER>/CascadeProjects/inparo-web/src/mocks/emergency.ts": "26", "/home/<USER>/CascadeProjects/inparo-web/src/mocks/renovation.tsx": "27", "/home/<USER>/CascadeProjects/inparo-web/src/mocks/services_new.ts": "28", "/home/<USER>/CascadeProjects/inparo-web/src/types/translations.ts": "29", "/home/<USER>/CascadeProjects/inparo-web/src/app/[lang]/privacy-policy/PrivacyPolicyClient.tsx": "30", "/home/<USER>/CascadeProjects/inparo-web/src/app/[lang]/privacy-policy/page.tsx": "31", "/home/<USER>/CascadeProjects/inparo-web/src/app/[lang]/terms/TermsClient.tsx": "32", "/home/<USER>/CascadeProjects/inparo-web/src/app/[lang]/terms/page.tsx": "33", "/home/<USER>/CascadeProjects/inparo-web/src/app/api/send-email/route.ts": "34", "/home/<USER>/CascadeProjects/inparo-web/src/app/page.tsx": "35", "/home/<USER>/CascadeProjects/inparo-web/src/components/emergency/EmergencyClient.tsx": "36"}, {"size": 35313, "mtime": 1741718931848, "results": "37", "hashOfConfig": "38"}, {"size": 445, "mtime": 1740344247313, "results": "39", "hashOfConfig": "38"}, {"size": 825, "mtime": 1741718577946, "results": "40", "hashOfConfig": "38"}, {"size": 3300, "mtime": 1741363594702, "results": "41", "hashOfConfig": "38"}, {"size": 11869, "mtime": 1741701180895, "results": "42", "hashOfConfig": "38"}, {"size": 8072, "mtime": 1741700458670, "results": "43", "hashOfConfig": "38"}, {"size": 670, "mtime": 1741620551252, "results": "44", "hashOfConfig": "38"}, {"size": 280, "mtime": 1741634940217, "results": "45", "hashOfConfig": "38"}, {"size": 2797, "mtime": 1741362618362, "results": "46", "hashOfConfig": "38"}, {"size": 544, "mtime": 1740342830527, "results": "47", "hashOfConfig": "38"}, {"size": 435, "mtime": 1740340384157, "results": "48", "hashOfConfig": "38"}, {"size": 519, "mtime": 1741372776584, "results": "49", "hashOfConfig": "38"}, {"size": 3575, "mtime": 1741631929352, "results": "50", "hashOfConfig": "38"}, {"size": 22008, "mtime": 1741718901668, "results": "51", "hashOfConfig": "38"}, {"size": 4682, "mtime": 1740333158158, "results": "52", "hashOfConfig": "38"}, {"size": 5421, "mtime": 1741711323579, "results": "53", "hashOfConfig": "38"}, {"size": 413, "mtime": 1740344764736, "results": "54", "hashOfConfig": "38"}, {"size": 5954, "mtime": 1741700291952, "results": "55", "hashOfConfig": "38"}, {"size": 10180, "mtime": 1741700236043, "results": "56", "hashOfConfig": "38"}, {"size": 888, "mtime": 1741618949840, "results": "57", "hashOfConfig": "38"}, {"size": 2772, "mtime": 1741362452311, "results": "58", "hashOfConfig": "38"}, {"size": 316, "mtime": 1740341202037, "results": "59", "hashOfConfig": "38"}, {"size": 536, "mtime": 1740343576628, "results": "60", "hashOfConfig": "38"}, {"size": 740, "mtime": 1740333996771, "results": "61", "hashOfConfig": "38"}, {"size": 1748, "mtime": 1740335082705, "results": "62", "hashOfConfig": "38"}, {"size": 16700, "mtime": 1741691362842, "results": "63", "hashOfConfig": "38"}, {"size": 9695, "mtime": 1740342011569, "results": "64", "hashOfConfig": "38"}, {"size": 27165, "mtime": 1741700746998, "results": "65", "hashOfConfig": "38"}, {"size": 4558, "mtime": 1741711152214, "results": "66", "hashOfConfig": "38"}, {"size": 9469, "mtime": 1741632003594, "results": "67", "hashOfConfig": "38"}, {"size": 675, "mtime": 1741632543104, "results": "68", "hashOfConfig": "38"}, {"size": 8594, "mtime": 1741632141347, "results": "69", "hashOfConfig": "38"}, {"size": 579, "mtime": 1741632617808, "results": "70", "hashOfConfig": "38"}, {"size": 2853, "mtime": 1741633084244, "results": "71", "hashOfConfig": "38"}, {"size": 163, "mtime": 1741634960078, "results": "72", "hashOfConfig": "38"}, {"size": 26028, "mtime": 1741712342615, "results": "73", "hashOfConfig": "38"}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "xmmzqi", {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 8, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 11, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 15, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/home/<USER>/CascadeProjects/inparo-web/src/app/[lang]/contact/ContactClient.tsx", ["182"], [], "/home/<USER>/CascadeProjects/inparo-web/src/app/[lang]/contact/page.tsx", [], [], "/home/<USER>/CascadeProjects/inparo-web/src/app/[lang]/emergency/page.tsx", [], [], "/home/<USER>/CascadeProjects/inparo-web/src/app/[lang]/layout.tsx", [], [], "/home/<USER>/CascadeProjects/inparo-web/src/app/[lang]/page.tsx", ["183"], [], "/home/<USER>/CascadeProjects/inparo-web/src/app/[lang]/services/ServicesClient.tsx", [], [], "/home/<USER>/CascadeProjects/inparo-web/src/app/[lang]/services/page.tsx", [], [], "/home/<USER>/CascadeProjects/inparo-web/src/app/layout.tsx", [], [], "/home/<USER>/CascadeProjects/inparo-web/src/app/providers.tsx", [], [], "/home/<USER>/CascadeProjects/inparo-web/src/components/ClientWrapper.tsx", ["184"], [], "/home/<USER>/CascadeProjects/inparo-web/src/components/animations/Fade.tsx", [], [], "/home/<USER>/CascadeProjects/inparo-web/src/components/animations/FadeInView.tsx", [], [], "/home/<USER>/CascadeProjects/inparo-web/src/components/footer/Footer.tsx", [], [], "/home/<USER>/CascadeProjects/inparo-web/src/components/forms/EmergencyContactForm.tsx", ["185", "186", "187"], [], "/home/<USER>/CascadeProjects/inparo-web/src/components/layout/Header.tsx", [], [], "/home/<USER>/CascadeProjects/inparo-web/src/components/navigation/Navigation.tsx", [], [], "/home/<USER>/CascadeProjects/inparo-web/src/components/navigation/NavigationWrapper.tsx", [], [], "/home/<USER>/CascadeProjects/inparo-web/src/components/services/CategorySection.tsx", ["188"], [], "/home/<USER>/CascadeProjects/inparo-web/src/components/services/ServiceCard.tsx", ["189", "190", "191", "192", "193", "194", "195", "196"], [], "/home/<USER>/CascadeProjects/inparo-web/src/components/services/ServiceIcons.tsx", [], [], "/home/<USER>/CascadeProjects/inparo-web/src/components/shared/LanguageSelector.tsx", ["197"], [], "/home/<USER>/CascadeProjects/inparo-web/src/i18n/server.ts", ["198"], [], "/home/<USER>/CascadeProjects/inparo-web/src/i18n/settings.ts", [], [], "/home/<USER>/CascadeProjects/inparo-web/src/i18n/useTranslation.ts", ["199"], [], "/home/<USER>/CascadeProjects/inparo-web/src/middleware.ts", [], [], "/home/<USER>/CascadeProjects/inparo-web/src/mocks/emergency.ts", [], [], "/home/<USER>/CascadeProjects/inparo-web/src/mocks/renovation.tsx", ["200", "201", "202", "203", "204"], [], "/home/<USER>/CascadeProjects/inparo-web/src/mocks/services_new.ts", [], [], "/home/<USER>/CascadeProjects/inparo-web/src/types/translations.ts", [], [], "/home/<USER>/CascadeProjects/inparo-web/src/app/[lang]/privacy-policy/PrivacyPolicyClient.tsx", ["205", "206", "207", "208", "209", "210", "211", "212", "213", "214", "215"], [], "/home/<USER>/CascadeProjects/inparo-web/src/app/[lang]/privacy-policy/page.tsx", ["216"], [], "/home/<USER>/CascadeProjects/inparo-web/src/app/[lang]/terms/TermsClient.tsx", ["217", "218", "219", "220", "221", "222", "223", "224", "225", "226", "227", "228", "229", "230", "231"], [], "/home/<USER>/CascadeProjects/inparo-web/src/app/[lang]/terms/page.tsx", ["232"], [], "/home/<USER>/CascadeProjects/inparo-web/src/app/api/send-email/route.ts", [], [], "/home/<USER>/CascadeProjects/inparo-web/src/app/page.tsx", [], [], "/home/<USER>/CascadeProjects/inparo-web/src/components/emergency/EmergencyClient.tsx", ["233", "234", "235", "236", "237"], [], {"ruleId": "238", "severity": 2, "message": "239", "line": 3, "column": 27, "nodeType": null, "messageId": "240", "endLine": 3, "endColumn": 36}, {"ruleId": "238", "severity": 2, "message": "241", "line": 4, "column": 29, "nodeType": null, "messageId": "240", "endLine": 4, "endColumn": 36}, {"ruleId": "238", "severity": 2, "message": "242", "line": 3, "column": 10, "nodeType": null, "messageId": "240", "endLine": 3, "endColumn": 21}, {"ruleId": "238", "severity": 2, "message": "243", "line": 5, "column": 10, "nodeType": null, "messageId": "240", "endLine": 5, "endColumn": 17}, {"ruleId": "238", "severity": 2, "message": "244", "line": 5, "column": 19, "nodeType": null, "messageId": "240", "endLine": 5, "endColumn": 26}, {"ruleId": "238", "severity": 2, "message": "245", "line": 14, "column": 43, "nodeType": null, "messageId": "240", "endLine": 14, "endColumn": 47}, {"ruleId": "238", "severity": 2, "message": "246", "line": 4, "column": 10, "nodeType": null, "messageId": "240", "endLine": 4, "endColumn": 22}, {"ruleId": "238", "severity": 2, "message": "246", "line": 7, "column": 19, "nodeType": null, "messageId": "240", "endLine": 7, "endColumn": 31}, {"ruleId": "238", "severity": 2, "message": "247", "line": 7, "column": 33, "nodeType": null, "messageId": "240", "endLine": 7, "endColumn": 40}, {"ruleId": "238", "severity": 2, "message": "248", "line": 7, "column": 42, "nodeType": null, "messageId": "240", "endLine": 7, "endColumn": 53}, {"ruleId": "238", "severity": 2, "message": "249", "line": 8, "column": 10, "nodeType": null, "messageId": "240", "endLine": 8, "endColumn": 21}, {"ruleId": "238", "severity": 2, "message": "250", "line": 175, "column": 3, "nodeType": null, "messageId": "240", "endLine": 175, "endColumn": 7}, {"ruleId": "238", "severity": 2, "message": "251", "line": 176, "column": 3, "nodeType": null, "messageId": "240", "endLine": 176, "endColumn": 8}, {"ruleId": "238", "severity": 2, "message": "252", "line": 177, "column": 3, "nodeType": null, "messageId": "240", "endLine": 177, "endColumn": 11}, {"ruleId": "238", "severity": 2, "message": "253", "line": 178, "column": 3, "nodeType": null, "messageId": "240", "endLine": 178, "endColumn": 11}, {"ruleId": "238", "severity": 2, "message": "254", "line": 21, "column": 9, "nodeType": null, "messageId": "240", "endLine": 21, "endColumn": 20}, {"ruleId": "238", "severity": 2, "message": "255", "line": 1, "column": 10, "nodeType": null, "messageId": "240", "endLine": 1, "endColumn": 26}, {"ruleId": "256", "severity": 2, "message": "257", "line": 22, "column": 16, "nodeType": "258", "messageId": "259", "endLine": 22, "endColumn": 19, "suggestions": "260"}, {"ruleId": "238", "severity": 2, "message": "261", "line": 1, "column": 10, "nodeType": null, "messageId": "240", "endLine": 1, "endColumn": 18}, {"ruleId": "238", "severity": 2, "message": "262", "line": 1, "column": 52, "nodeType": null, "messageId": "240", "endLine": 1, "endColumn": 59}, {"ruleId": "238", "severity": 2, "message": "263", "line": 1, "column": 61, "nodeType": null, "messageId": "240", "endLine": 1, "endColumn": 72}, {"ruleId": "238", "severity": 2, "message": "264", "line": 2, "column": 10, "nodeType": null, "messageId": "240", "endLine": 2, "endColumn": 19}, {"ruleId": "256", "severity": 2, "message": "257", "line": 5, "column": 9, "nodeType": "258", "messageId": "259", "endLine": 5, "endColumn": 12, "suggestions": "265"}, {"ruleId": "238", "severity": 2, "message": "266", "line": 11, "column": 47, "nodeType": null, "messageId": "240", "endLine": 11, "endColumn": 48}, {"ruleId": "238", "severity": 2, "message": "245", "line": 11, "column": 50, "nodeType": null, "messageId": "240", "endLine": 11, "endColumn": 54}, {"ruleId": "267", "severity": 2, "message": "268", "line": 54, "column": 267, "nodeType": "269", "messageId": "270", "suggestions": "271"}, {"ruleId": "267", "severity": 2, "message": "272", "line": 111, "column": 56, "nodeType": "269", "messageId": "270", "suggestions": "273"}, {"ruleId": "267", "severity": 2, "message": "268", "line": 111, "column": 68, "nodeType": "269", "messageId": "270", "suggestions": "274"}, {"ruleId": "267", "severity": 2, "message": "272", "line": 111, "column": 74, "nodeType": "269", "messageId": "270", "suggestions": "275"}, {"ruleId": "267", "severity": 2, "message": "268", "line": 117, "column": 77, "nodeType": "269", "messageId": "270", "suggestions": "276"}, {"ruleId": "267", "severity": 2, "message": "268", "line": 122, "column": 128, "nodeType": "269", "messageId": "270", "suggestions": "277"}, {"ruleId": "267", "severity": 2, "message": "268", "line": 122, "column": 145, "nodeType": "269", "messageId": "270", "suggestions": "278"}, {"ruleId": "267", "severity": 2, "message": "268", "line": 122, "column": 189, "nodeType": "269", "messageId": "270", "suggestions": "279"}, {"ruleId": "267", "severity": 2, "message": "268", "line": 142, "column": 157, "nodeType": "269", "messageId": "270", "suggestions": "280"}, {"ruleId": "238", "severity": 2, "message": "281", "line": 9, "column": 42, "nodeType": null, "messageId": "240", "endLine": 9, "endColumn": 48}, {"ruleId": "238", "severity": 2, "message": "266", "line": 11, "column": 39, "nodeType": null, "messageId": "240", "endLine": 11, "endColumn": 40}, {"ruleId": "238", "severity": 2, "message": "245", "line": 11, "column": 42, "nodeType": null, "messageId": "240", "endLine": 11, "endColumn": 46}, {"ruleId": "267", "severity": 2, "message": "272", "line": 54, "column": 48, "nodeType": "269", "messageId": "270", "suggestions": "282"}, {"ruleId": "267", "severity": 2, "message": "272", "line": 54, "column": 56, "nodeType": "269", "messageId": "270", "suggestions": "283"}, {"ruleId": "267", "severity": 2, "message": "268", "line": 54, "column": 69, "nodeType": "269", "messageId": "270", "suggestions": "284"}, {"ruleId": "267", "severity": 2, "message": "268", "line": 54, "column": 168, "nodeType": "269", "messageId": "270", "suggestions": "285"}, {"ruleId": "267", "severity": 2, "message": "268", "line": 69, "column": 176, "nodeType": "269", "messageId": "270", "suggestions": "286"}, {"ruleId": "267", "severity": 2, "message": "268", "line": 74, "column": 84, "nodeType": "269", "messageId": "270", "suggestions": "287"}, {"ruleId": "267", "severity": 2, "message": "268", "line": 84, "column": 275, "nodeType": "269", "messageId": "270", "suggestions": "288"}, {"ruleId": "267", "severity": 2, "message": "268", "line": 89, "column": 331, "nodeType": "269", "messageId": "270", "suggestions": "289"}, {"ruleId": "267", "severity": 2, "message": "268", "line": 94, "column": 16, "nodeType": "269", "messageId": "270", "suggestions": "290"}, {"ruleId": "267", "severity": 2, "message": "268", "line": 109, "column": 234, "nodeType": "269", "messageId": "270", "suggestions": "291"}, {"ruleId": "267", "severity": 2, "message": "268", "line": 114, "column": 198, "nodeType": "269", "messageId": "270", "suggestions": "292"}, {"ruleId": "267", "severity": 2, "message": "268", "line": 114, "column": 266, "nodeType": "269", "messageId": "270", "suggestions": "293"}, {"ruleId": "267", "severity": 2, "message": "268", "line": 119, "column": 110, "nodeType": "269", "messageId": "270", "suggestions": "294"}, {"ruleId": "238", "severity": 2, "message": "281", "line": 9, "column": 42, "nodeType": null, "messageId": "240", "endLine": 9, "endColumn": 48}, {"ruleId": "238", "severity": 2, "message": "295", "line": 5, "column": 8, "nodeType": null, "messageId": "240", "endLine": 5, "endColumn": 13}, {"ruleId": "256", "severity": 2, "message": "257", "line": 12, "column": 9, "nodeType": "258", "messageId": "259", "endLine": 12, "endColumn": 12, "suggestions": "296"}, {"ruleId": "256", "severity": 2, "message": "257", "line": 167, "column": 42, "nodeType": "258", "messageId": "259", "endLine": 167, "endColumn": 45, "suggestions": "297"}, {"ruleId": "256", "severity": 2, "message": "257", "line": 212, "column": 40, "nodeType": "258", "messageId": "259", "endLine": 212, "endColumn": 43, "suggestions": "298"}, {"ruleId": "256", "severity": 2, "message": "257", "line": 252, "column": 49, "nodeType": "258", "messageId": "259", "endLine": 252, "endColumn": 52, "suggestions": "299"}, "@typescript-eslint/no-unused-vars", "'FormEvent' is defined but never used.", "unusedVar", "'FaBroom' is defined but never used.", "'usePathname' is defined but never used.", "'FaCheck' is defined but never used.", "'FaTimes' is defined but never used.", "'lang' is defined but never used.", "'FaArrowRight' is defined but never used.", "'FaClock' is defined but never used.", "'FaShieldAlt' is defined but never used.", "'ServiceIcon' is defined but never used.", "'icon' is defined but never used.", "'image' is defined but never used.", "'duration' is defined but never used.", "'warranty' is defined but never used.", "'newPathname' is assigned a value but never used.", "'createTranslator' is defined but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["300", "301"], "'FaHammer' is defined but never used.", "'FaRuler' is defined but never used.", "'FaHandshake' is defined but never used.", "'ReactNode' is defined but never used.", ["302", "303"], "'t' is defined but never used.", "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["304", "305", "306", "307"], "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", ["308", "309", "310", "311"], ["312", "313", "314", "315"], ["316", "317", "318", "319"], ["320", "321", "322", "323"], ["324", "325", "326", "327"], ["328", "329", "330", "331"], ["332", "333", "334", "335"], ["336", "337", "338", "339"], "'params' is defined but never used.", ["340", "341", "342", "343"], ["344", "345", "346", "347"], ["348", "349", "350", "351"], ["352", "353", "354", "355"], ["356", "357", "358", "359"], ["360", "361", "362", "363"], ["364", "365", "366", "367"], ["368", "369", "370", "371"], ["372", "373", "374", "375"], ["376", "377", "378", "379"], ["380", "381", "382", "383"], ["384", "385", "386", "387"], ["388", "389", "390", "391"], "'Image' is defined but never used.", ["392", "393"], ["394", "395"], ["396", "397"], ["398", "399"], {"messageId": "400", "fix": "401", "desc": "402"}, {"messageId": "403", "fix": "404", "desc": "405"}, {"messageId": "400", "fix": "406", "desc": "402"}, {"messageId": "403", "fix": "407", "desc": "405"}, {"messageId": "408", "data": "409", "fix": "410", "desc": "411"}, {"messageId": "408", "data": "412", "fix": "413", "desc": "414"}, {"messageId": "408", "data": "415", "fix": "416", "desc": "417"}, {"messageId": "408", "data": "418", "fix": "419", "desc": "420"}, {"messageId": "408", "data": "421", "fix": "422", "desc": "423"}, {"messageId": "408", "data": "424", "fix": "425", "desc": "426"}, {"messageId": "408", "data": "427", "fix": "428", "desc": "429"}, {"messageId": "408", "data": "430", "fix": "431", "desc": "432"}, {"messageId": "408", "data": "433", "fix": "434", "desc": "411"}, {"messageId": "408", "data": "435", "fix": "436", "desc": "414"}, {"messageId": "408", "data": "437", "fix": "438", "desc": "417"}, {"messageId": "408", "data": "439", "fix": "440", "desc": "420"}, {"messageId": "408", "data": "441", "fix": "442", "desc": "423"}, {"messageId": "408", "data": "443", "fix": "444", "desc": "426"}, {"messageId": "408", "data": "445", "fix": "446", "desc": "429"}, {"messageId": "408", "data": "447", "fix": "448", "desc": "432"}, {"messageId": "408", "data": "449", "fix": "450", "desc": "411"}, {"messageId": "408", "data": "451", "fix": "452", "desc": "414"}, {"messageId": "408", "data": "453", "fix": "454", "desc": "417"}, {"messageId": "408", "data": "455", "fix": "456", "desc": "420"}, {"messageId": "408", "data": "457", "fix": "458", "desc": "411"}, {"messageId": "408", "data": "459", "fix": "460", "desc": "414"}, {"messageId": "408", "data": "461", "fix": "462", "desc": "417"}, {"messageId": "408", "data": "463", "fix": "464", "desc": "420"}, {"messageId": "408", "data": "465", "fix": "466", "desc": "411"}, {"messageId": "408", "data": "467", "fix": "468", "desc": "414"}, {"messageId": "408", "data": "469", "fix": "470", "desc": "417"}, {"messageId": "408", "data": "471", "fix": "472", "desc": "420"}, {"messageId": "408", "data": "473", "fix": "474", "desc": "411"}, {"messageId": "408", "data": "475", "fix": "476", "desc": "414"}, {"messageId": "408", "data": "477", "fix": "478", "desc": "417"}, {"messageId": "408", "data": "479", "fix": "480", "desc": "420"}, {"messageId": "408", "data": "481", "fix": "482", "desc": "411"}, {"messageId": "408", "data": "483", "fix": "484", "desc": "414"}, {"messageId": "408", "data": "485", "fix": "486", "desc": "417"}, {"messageId": "408", "data": "487", "fix": "488", "desc": "420"}, {"messageId": "408", "data": "489", "fix": "490", "desc": "423"}, {"messageId": "408", "data": "491", "fix": "492", "desc": "426"}, {"messageId": "408", "data": "493", "fix": "494", "desc": "429"}, {"messageId": "408", "data": "495", "fix": "496", "desc": "432"}, {"messageId": "408", "data": "497", "fix": "498", "desc": "423"}, {"messageId": "408", "data": "499", "fix": "500", "desc": "426"}, {"messageId": "408", "data": "501", "fix": "502", "desc": "429"}, {"messageId": "408", "data": "503", "fix": "504", "desc": "432"}, {"messageId": "408", "data": "505", "fix": "506", "desc": "411"}, {"messageId": "408", "data": "507", "fix": "508", "desc": "414"}, {"messageId": "408", "data": "509", "fix": "510", "desc": "417"}, {"messageId": "408", "data": "511", "fix": "512", "desc": "420"}, {"messageId": "408", "data": "513", "fix": "514", "desc": "411"}, {"messageId": "408", "data": "515", "fix": "516", "desc": "414"}, {"messageId": "408", "data": "517", "fix": "518", "desc": "417"}, {"messageId": "408", "data": "519", "fix": "520", "desc": "420"}, {"messageId": "408", "data": "521", "fix": "522", "desc": "411"}, {"messageId": "408", "data": "523", "fix": "524", "desc": "414"}, {"messageId": "408", "data": "525", "fix": "526", "desc": "417"}, {"messageId": "408", "data": "527", "fix": "528", "desc": "420"}, {"messageId": "408", "data": "529", "fix": "530", "desc": "411"}, {"messageId": "408", "data": "531", "fix": "532", "desc": "414"}, {"messageId": "408", "data": "533", "fix": "534", "desc": "417"}, {"messageId": "408", "data": "535", "fix": "536", "desc": "420"}, {"messageId": "408", "data": "537", "fix": "538", "desc": "411"}, {"messageId": "408", "data": "539", "fix": "540", "desc": "414"}, {"messageId": "408", "data": "541", "fix": "542", "desc": "417"}, {"messageId": "408", "data": "543", "fix": "544", "desc": "420"}, {"messageId": "408", "data": "545", "fix": "546", "desc": "411"}, {"messageId": "408", "data": "547", "fix": "548", "desc": "414"}, {"messageId": "408", "data": "549", "fix": "550", "desc": "417"}, {"messageId": "408", "data": "551", "fix": "552", "desc": "420"}, {"messageId": "408", "data": "553", "fix": "554", "desc": "411"}, {"messageId": "408", "data": "555", "fix": "556", "desc": "414"}, {"messageId": "408", "data": "557", "fix": "558", "desc": "417"}, {"messageId": "408", "data": "559", "fix": "560", "desc": "420"}, {"messageId": "408", "data": "561", "fix": "562", "desc": "411"}, {"messageId": "408", "data": "563", "fix": "564", "desc": "414"}, {"messageId": "408", "data": "565", "fix": "566", "desc": "417"}, {"messageId": "408", "data": "567", "fix": "568", "desc": "420"}, {"messageId": "408", "data": "569", "fix": "570", "desc": "411"}, {"messageId": "408", "data": "571", "fix": "572", "desc": "414"}, {"messageId": "408", "data": "573", "fix": "574", "desc": "417"}, {"messageId": "408", "data": "575", "fix": "576", "desc": "420"}, {"messageId": "408", "data": "577", "fix": "578", "desc": "411"}, {"messageId": "408", "data": "579", "fix": "580", "desc": "414"}, {"messageId": "408", "data": "581", "fix": "582", "desc": "417"}, {"messageId": "408", "data": "583", "fix": "584", "desc": "420"}, {"messageId": "408", "data": "585", "fix": "586", "desc": "411"}, {"messageId": "408", "data": "587", "fix": "588", "desc": "414"}, {"messageId": "408", "data": "589", "fix": "590", "desc": "417"}, {"messageId": "408", "data": "591", "fix": "592", "desc": "420"}, {"messageId": "400", "fix": "593", "desc": "402"}, {"messageId": "403", "fix": "594", "desc": "405"}, {"messageId": "400", "fix": "595", "desc": "402"}, {"messageId": "403", "fix": "596", "desc": "405"}, {"messageId": "400", "fix": "597", "desc": "402"}, {"messageId": "403", "fix": "598", "desc": "405"}, {"messageId": "400", "fix": "599", "desc": "402"}, {"messageId": "403", "fix": "600", "desc": "405"}, "suggestUnknown", {"range": "601", "text": "602"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "603", "text": "604"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "605", "text": "602"}, {"range": "606", "text": "604"}, "replaceWithAlt", {"alt": "607"}, {"range": "608", "text": "609"}, "Replace with `&apos;`.", {"alt": "610"}, {"range": "611", "text": "612"}, "Replace with `&lsquo;`.", {"alt": "613"}, {"range": "614", "text": "615"}, "Replace with `&#39;`.", {"alt": "616"}, {"range": "617", "text": "618"}, "Replace with `&rsquo;`.", {"alt": "619"}, {"range": "620", "text": "621"}, "Replace with `&quot;`.", {"alt": "622"}, {"range": "623", "text": "624"}, "Replace with `&ldquo;`.", {"alt": "625"}, {"range": "626", "text": "627"}, "Replace with `&#34;`.", {"alt": "628"}, {"range": "629", "text": "630"}, "Replace with `&rdquo;`.", {"alt": "607"}, {"range": "631", "text": "632"}, {"alt": "610"}, {"range": "633", "text": "634"}, {"alt": "613"}, {"range": "635", "text": "636"}, {"alt": "616"}, {"range": "637", "text": "638"}, {"alt": "619"}, {"range": "639", "text": "640"}, {"alt": "622"}, {"range": "641", "text": "642"}, {"alt": "625"}, {"range": "643", "text": "644"}, {"alt": "628"}, {"range": "645", "text": "646"}, {"alt": "607"}, {"range": "647", "text": "648"}, {"alt": "610"}, {"range": "649", "text": "650"}, {"alt": "613"}, {"range": "651", "text": "652"}, {"alt": "616"}, {"range": "653", "text": "654"}, {"alt": "607"}, {"range": "655", "text": "656"}, {"alt": "610"}, {"range": "657", "text": "658"}, {"alt": "613"}, {"range": "659", "text": "660"}, {"alt": "616"}, {"range": "661", "text": "662"}, {"alt": "607"}, {"range": "663", "text": "664"}, {"alt": "610"}, {"range": "665", "text": "666"}, {"alt": "613"}, {"range": "667", "text": "668"}, {"alt": "616"}, {"range": "669", "text": "670"}, {"alt": "607"}, {"range": "671", "text": "672"}, {"alt": "610"}, {"range": "673", "text": "674"}, {"alt": "613"}, {"range": "675", "text": "676"}, {"alt": "616"}, {"range": "677", "text": "678"}, {"alt": "607"}, {"range": "679", "text": "680"}, {"alt": "610"}, {"range": "681", "text": "682"}, {"alt": "613"}, {"range": "683", "text": "684"}, {"alt": "616"}, {"range": "685", "text": "686"}, {"alt": "619"}, {"range": "687", "text": "688"}, {"alt": "622"}, {"range": "689", "text": "690"}, {"alt": "625"}, {"range": "691", "text": "692"}, {"alt": "628"}, {"range": "693", "text": "694"}, {"alt": "619"}, {"range": "695", "text": "696"}, {"alt": "622"}, {"range": "697", "text": "698"}, {"alt": "625"}, {"range": "699", "text": "700"}, {"alt": "628"}, {"range": "701", "text": "702"}, {"alt": "607"}, {"range": "703", "text": "704"}, {"alt": "610"}, {"range": "705", "text": "706"}, {"alt": "613"}, {"range": "707", "text": "708"}, {"alt": "616"}, {"range": "709", "text": "710"}, {"alt": "607"}, {"range": "711", "text": "712"}, {"alt": "610"}, {"range": "713", "text": "714"}, {"alt": "613"}, {"range": "715", "text": "716"}, {"alt": "616"}, {"range": "717", "text": "718"}, {"alt": "607"}, {"range": "719", "text": "720"}, {"alt": "610"}, {"range": "721", "text": "722"}, {"alt": "613"}, {"range": "723", "text": "724"}, {"alt": "616"}, {"range": "725", "text": "726"}, {"alt": "607"}, {"range": "727", "text": "728"}, {"alt": "610"}, {"range": "729", "text": "730"}, {"alt": "613"}, {"range": "731", "text": "732"}, {"alt": "616"}, {"range": "733", "text": "734"}, {"alt": "607"}, {"range": "735", "text": "736"}, {"alt": "610"}, {"range": "737", "text": "738"}, {"alt": "613"}, {"range": "739", "text": "740"}, {"alt": "616"}, {"range": "741", "text": "742"}, {"alt": "607"}, {"range": "743", "text": "744"}, {"alt": "610"}, {"range": "745", "text": "746"}, {"alt": "613"}, {"range": "747", "text": "748"}, {"alt": "616"}, {"range": "749", "text": "750"}, {"alt": "607"}, {"range": "751", "text": "752"}, {"alt": "610"}, {"range": "753", "text": "754"}, {"alt": "613"}, {"range": "755", "text": "756"}, {"alt": "616"}, {"range": "757", "text": "758"}, {"alt": "607"}, {"range": "759", "text": "760"}, {"alt": "610"}, {"range": "761", "text": "762"}, {"alt": "613"}, {"range": "763", "text": "764"}, {"alt": "616"}, {"range": "765", "text": "766"}, {"alt": "607"}, {"range": "767", "text": "768"}, {"alt": "610"}, {"range": "769", "text": "770"}, {"alt": "613"}, {"range": "771", "text": "772"}, {"alt": "616"}, {"range": "773", "text": "774"}, {"alt": "607"}, {"range": "775", "text": "776"}, {"alt": "610"}, {"range": "777", "text": "778"}, {"alt": "613"}, {"range": "779", "text": "780"}, {"alt": "616"}, {"range": "781", "text": "782"}, {"alt": "607"}, {"range": "783", "text": "784"}, {"alt": "610"}, {"range": "785", "text": "786"}, {"alt": "613"}, {"range": "787", "text": "788"}, {"alt": "616"}, {"range": "789", "text": "790"}, {"range": "791", "text": "602"}, {"range": "792", "text": "604"}, {"range": "793", "text": "602"}, {"range": "794", "text": "604"}, {"range": "795", "text": "602"}, {"range": "796", "text": "604"}, {"range": "797", "text": "602"}, {"range": "798", "text": "604"}, [477, 480], "unknown", [477, 480], "never", [165, 168], [165, 168], "&apos;", [2575, 2866], "\n              La protezione dei vostri dati personali è una priorità per Inparo GmbH. Questa Informativa sulla privacy è conforme alla Legge federale sulla protezione dei dati (LPD) svizzera e, dove applicabile, al Regolamento generale sulla protezione dei dati dell&apos;UE (GDPR).\n            ", "&lsquo;", [2575, 2866], "\n              La protezione dei vostri dati personali è una priorità per Inparo GmbH. Questa Informativa sulla privacy è conforme alla Legge federale sulla protezione dei dati (LPD) svizzera e, dove applicabile, al Regolamento generale sulla protezione dei dati dell&lsquo;UE (GDPR).\n            ", "&#39;", [2575, 2866], "\n              La protezione dei vostri dati personali è una priorità per Inparo GmbH. Questa Informativa sulla privacy è conforme alla Legge federale sulla protezione dei dati (LPD) svizzera e, dove applicabile, al Regolamento generale sulla protezione dei dati dell&#39;UE (GDPR).\n            ", "&rsquo;", [2575, 2866], "\n              La protezione dei vostri dati personali è una priorità per Inparo GmbH. Questa Informativa sulla privacy è conforme alla Legge federale sulla protezione dei dati (LPD) svizzera e, dove applicabile, al Regolamento generale sulla protezione dei dati dell&rsquo;UE (GDPR).\n            ", "&quot;", [6194, 6251], "Diritto alla cancellazione dei dati (&quot;diritto all'oblio\")", "&ldquo;", [6194, 6251], "Diritto alla cancellazione dei dati (&ldquo;diritto all'oblio\")", "&#34;", [6194, 6251], "Diritto alla cancellazione dei dati (&#34;diritto all'oblio\")", "&rdquo;", [6194, 6251], "Diritto alla cancellazione dei dati (&rdquo;diritto all'oblio\")", [6194, 6251], "Diritto alla cancellazione dei dati (\"diritto all&apos;oblio\")", [6194, 6251], "Diritto alla cancellazione dei dati (\"diritto all&lsquo;oblio\")", [6194, 6251], "Diritto alla cancellazione dei dati (\"diritto all&#39;oblio\")", [6194, 6251], "Diritto alla cancellazione dei dati (\"diritto all&rsquo;oblio\")", [6194, 6251], "Diritto alla cancellazione dei dati (\"diritto all'oblio&quot;)", [6194, 6251], "Diritto alla cancellazione dei dati (\"diritto all'oblio&ldquo;)", [6194, 6251], "Diritto alla cancellazione dei dati (\"diritto all'oblio&#34;)", [6194, 6251], "Diritto alla cancellazione dei dati (\"diritto all'oblio&rdquo;)", [6501, 6623], "\n              Per esercitare questi diritti, vi preghiamo di contattarci all&apos;indirizzo email: <EMAIL>\n            ", [6501, 6623], "\n              Per esercitare questi diritti, vi preghiamo di contattarci all&lsquo;indirizzo email: <EMAIL>\n            ", [6501, 6623], "\n              Per esercitare questi diritti, vi preghiamo di contattarci all&#39;indirizzo email: <EMAIL>\n            ", [6501, 6623], "\n              Per esercitare questi diritti, vi preghiamo di contattarci all&rsquo;indirizzo email: <EMAIL>\n            ", [6777, 7009], "\n              Adottiamo misure tecniche e organizzative appropriate per proteggere i vostri dati personali contro la perdita, l&apos;uso improprio, l'accesso non autorizzato, la divulgazione, l'alterazione o la distruzione.\n            ", [6777, 7009], "\n              Adottiamo misure tecniche e organizzative appropriate per proteggere i vostri dati personali contro la perdita, l&lsquo;uso improprio, l'accesso non autorizzato, la divulgazione, l'alterazione o la distruzione.\n            ", [6777, 7009], "\n              Adottiamo misure tecniche e organizzative appropriate per proteggere i vostri dati personali contro la perdita, l&#39;uso improprio, l'accesso non autorizzato, la divulgazione, l'alterazione o la distruzione.\n            ", [6777, 7009], "\n              Adottiamo misure tecniche e organizzative appropriate per proteggere i vostri dati personali contro la perdita, l&rsquo;uso improprio, l'accesso non autorizzato, la divulgazione, l'alterazione o la distruzione.\n            ", [6777, 7009], "\n              Adottiamo misure tecniche e organizzative appropriate per proteggere i vostri dati personali contro la perdita, l'uso improprio, l&apos;accesso non autorizzato, la divulgazione, l'alterazione o la distruzione.\n            ", [6777, 7009], "\n              Adottiamo misure tecniche e organizzative appropriate per proteggere i vostri dati personali contro la perdita, l'uso improprio, l&lsquo;accesso non autorizzato, la divulgazione, l'alterazione o la distruzione.\n            ", [6777, 7009], "\n              Adottiamo misure tecniche e organizzative appropriate per proteggere i vostri dati personali contro la perdita, l'uso improprio, l&#39;accesso non autorizzato, la divulgazione, l'alterazione o la distruzione.\n            ", [6777, 7009], "\n              Adottiamo misure tecniche e organizzative appropriate per proteggere i vostri dati personali contro la perdita, l'uso improprio, l&rsquo;accesso non autorizzato, la divulgazione, l'alterazione o la distruzione.\n            ", [6777, 7009], "\n              Adottiamo misure tecniche e organizzative appropriate per proteggere i vostri dati personali contro la perdita, l'uso improprio, l'accesso non autorizzato, la divulgazione, l&apos;alterazione o la distruzione.\n            ", [6777, 7009], "\n              Adottiamo misure tecniche e organizzative appropriate per proteggere i vostri dati personali contro la perdita, l'uso improprio, l'accesso non autorizzato, la divulgazione, l&lsquo;alterazione o la distruzione.\n            ", [6777, 7009], "\n              Adottiamo misure tecniche e organizzative appropriate per proteggere i vostri dati personali contro la perdita, l'uso improprio, l'accesso non autorizzato, la divulgazione, l&#39;alterazione o la distruzione.\n            ", [6777, 7009], "\n              Adottiamo misure tecniche e organizzative appropriate per proteggere i vostri dati personali contro la perdita, l'uso improprio, l'accesso non autorizzato, la divulgazione, l&rsquo;alterazione o la distruzione.\n            ", [8709, 8890], "\n              Per qualsiasi domanda o richiesta relativa a questa Privacy Policy o al trattamento dei vostri dati personali, vi preghiamo di contattarci all&apos;indirizzo:\n            ", [8709, 8890], "\n              Per qualsiasi domanda o richiesta relativa a questa Privacy Policy o al trattamento dei vostri dati personali, vi preghiamo di contattarci all&lsquo;indirizzo:\n            ", [8709, 8890], "\n              Per qualsiasi domanda o richiesta relativa a questa Privacy Policy o al trattamento dei vostri dati personali, vi preghiamo di contattarci all&#39;indirizzo:\n            ", [8709, 8890], "\n              Per qualsiasi domanda o richiesta relativa a questa Privacy Policy o al trattamento dei vostri dati personali, vi preghiamo di contattarci all&rsquo;indirizzo:\n            ", [2547, 2756], "\n              I presenti Termini e Condizioni (&quot;<PERSON>rm<PERSON>\") regolano l'utilizzo dei servizi offerti da Inparo GmbH e costituiscono un accordo legalmente vincolante tra l'utente e la nostra azienda.\n            ", [2547, 2756], "\n              I presenti Termini e Condizioni (&ldquo;Termini\") regolano l'utilizzo dei servizi offerti da Inparo GmbH e costituiscono un accordo legalmente vincolante tra l'utente e la nostra azienda.\n            ", [2547, 2756], "\n              I presenti Termini e Condizioni (&#34;<PERSON><PERSON><PERSON>\") regolano l'utilizzo dei servizi offerti da Inparo GmbH e costituiscono un accordo legalmente vincolante tra l'utente e la nostra azienda.\n            ", [2547, 2756], "\n              I presenti Termini e Condizioni (&rdquo;Termini\") regolano l'utilizzo dei servizi offerti da Inparo GmbH e costituiscono un accordo legalmente vincolante tra l'utente e la nostra azienda.\n            ", [2547, 2756], "\n              I presenti Termini e Condizioni (\"Termini&quot;) regolano l'utilizzo dei servizi offerti da Inparo GmbH e costituiscono un accordo legalmente vincolante tra l'utente e la nostra azienda.\n            ", [2547, 2756], "\n              I presenti Termini e Condizioni (\"Termini&ldquo;) regolano l'utilizzo dei servizi offerti da Inparo GmbH e costituiscono un accordo legalmente vincolante tra l'utente e la nostra azienda.\n            ", [2547, 2756], "\n              I presenti Termini e Condizioni (\"Termini&#34;) regolano l'utilizzo dei servizi offerti da Inparo GmbH e costituiscono un accordo legalmente vincolante tra l'utente e la nostra azienda.\n            ", [2547, 2756], "\n              I presenti Termini e Condizioni (\"Termini&rdquo;) regolano l'utilizzo dei servizi offerti da Inparo GmbH e costituiscono un accordo legalmente vincolante tra l'utente e la nostra azienda.\n            ", [2547, 2756], "\n              I presenti Termini e Condizioni (\"Termini\") regolano l&apos;utilizzo dei servizi offerti da Inparo GmbH e costituiscono un accordo legalmente vincolante tra l'utente e la nostra azienda.\n            ", [2547, 2756], "\n              I presenti Termini e Condizioni (\"Termini\") regolano l&lsquo;utilizzo dei servizi offerti da Inparo GmbH e costituiscono un accordo legalmente vincolante tra l'utente e la nostra azienda.\n            ", [2547, 2756], "\n              I presenti Termini e Condizioni (\"Termini\") regolano l&#39;utilizzo dei servizi offerti da Inparo GmbH e costituiscono un accordo legalmente vincolante tra l'utente e la nostra azienda.\n            ", [2547, 2756], "\n              I presenti Termini e Condizioni (\"Termini\") regolano l&rsquo;utilizzo dei servizi offerti da Inparo GmbH e costituiscono un accordo legalmente vincolante tra l'utente e la nostra azienda.\n            ", [2547, 2756], "\n              I presenti Termini e Condizioni (\"Termini\") regolano l'utilizzo dei servizi offerti da Inparo GmbH e costituiscono un accordo legalmente vincolante tra l&apos;utente e la nostra azienda.\n            ", [2547, 2756], "\n              I presenti Termini e Condizioni (\"Termini\") regolano l'utilizzo dei servizi offerti da Inparo GmbH e costituiscono un accordo legalmente vincolante tra l&lsquo;utente e la nostra azienda.\n            ", [2547, 2756], "\n              I presenti Termini e Condizioni (\"Termini\") regolano l'utilizzo dei servizi offerti da Inparo GmbH e costituiscono un accordo legalmente vincolante tra l&#39;utente e la nostra azienda.\n            ", [2547, 2756], "\n              I presenti Termini e Condizioni (\"Termini\") regolano l'utilizzo dei servizi offerti da Inparo GmbH e costituiscono un accordo legalmente vincolante tra l&rsquo;utente e la nostra azienda.\n            ", [3308, 3527], "\n              Inparo GmbH offre servizi di idraulica, riscaldamento e climatizzazione come descritto nel sito web. Questi Termini disciplinano la fornitura di tali servizi e l&apos;utilizzo del nostro sito web.\n            ", [3308, 3527], "\n              Inparo GmbH offre servizi di idraulica, riscaldamento e climatizzazione come descritto nel sito web. Questi Termini disciplinano la fornitura di tali servizi e l&lsquo;utilizzo del nostro sito web.\n            ", [3308, 3527], "\n              Inparo GmbH offre servizi di idraulica, riscaldamento e climatizzazione come descritto nel sito web. Questi Termini disciplinano la fornitura di tali servizi e l&#39;utilizzo del nostro sito web.\n            ", [3308, 3527], "\n              Inparo GmbH offre servizi di idraulica, riscaldamento e climatizzazione come descritto nel sito web. Questi Termini disciplinano la fornitura di tali servizi e l&rsquo;utilizzo del nostro sito web.\n            ", [3709, 3990], "\n              Il contratto di servizio viene concluso quando Inparo GmbH conferma l&apos;accettazione di un ordine di servizio del cliente tramite conferma scritta (email, SMS o altri mezzi di comunicazione). Il contratto è soggetto al diritto svizzero delle obbligazioni.\n            ", [3709, 3990], "\n              Il contratto di servizio viene concluso quando Inparo GmbH conferma l&lsquo;accettazione di un ordine di servizio del cliente tramite conferma scritta (email, SMS o altri mezzi di comunicazione). Il contratto è soggetto al diritto svizzero delle obbligazioni.\n            ", [3709, 3990], "\n              Il contratto di servizio viene concluso quando Inparo GmbH conferma l&#39;accettazione di un ordine di servizio del cliente tramite conferma scritta (email, SMS o altri mezzi di comunicazione). Il contratto è soggetto al diritto svizzero delle obbligazioni.\n            ", [3709, 3990], "\n              Il contratto di servizio viene concluso quando Inparo GmbH conferma l&rsquo;accettazione di un ordine di servizio del cliente tramite conferma scritta (email, SMS o altri mezzi di comunicazione). Il contratto è soggetto al diritto svizzero delle obbligazioni.\n            ", [4644, 4956], "\n              I servizi saranno eseguiti nei tempi e nei modi concordati tra le parti. Eventuali ritardi dovuti a cause di forza maggiore non comporteranno responsabilità per Inparo GmbH. Il cliente si impegna a fornire accesso ai locali e alle informazioni necessarie per l&apos;esecuzione dei servizi.\n            ", [4644, 4956], "\n              I servizi saranno eseguiti nei tempi e nei modi concordati tra le parti. Eventuali ritardi dovuti a cause di forza maggiore non comporteranno responsabilità per Inparo GmbH. Il cliente si impegna a fornire accesso ai locali e alle informazioni necessarie per l&lsquo;esecuzione dei servizi.\n            ", [4644, 4956], "\n              I servizi saranno eseguiti nei tempi e nei modi concordati tra le parti. Eventuali ritardi dovuti a cause di forza maggiore non comporteranno responsabilità per Inparo GmbH. Il cliente si impegna a fornire accesso ai locali e alle informazioni necessarie per l&#39;esecuzione dei servizi.\n            ", [4644, 4956], "\n              I servizi saranno eseguiti nei tempi e nei modi concordati tra le parti. Eventuali ritardi dovuti a cause di forza maggiore non comporteranno responsabilità per Inparo GmbH. Il cliente si impegna a fornire accesso ai locali e alle informazioni necessarie per l&rsquo;esecuzione dei servizi.\n            ", [5117, 5518], "\n              Inparo GmbH garantisce che i servizi saranno eseguiti con la dovuta diligenza professionale. La garanzia sui lavori eseguiti è di 24 mesi dalla data di completamento, salvo diversa indicazione. La responsabilità di Inparo GmbH è limitata ai danni diretti causati da negligenza grave o dolo ed è comunque limitata all&apos;importo pagato dal cliente per il servizio in questione.\n            ", [5117, 5518], "\n              Inparo GmbH garantisce che i servizi saranno eseguiti con la dovuta diligenza professionale. La garanzia sui lavori eseguiti è di 24 mesi dalla data di completamento, salvo diversa indicazione. La responsabilità di Inparo GmbH è limitata ai danni diretti causati da negligenza grave o dolo ed è comunque limitata all&lsquo;importo pagato dal cliente per il servizio in questione.\n            ", [5117, 5518], "\n              Inparo GmbH garantisce che i servizi saranno eseguiti con la dovuta diligenza professionale. La garanzia sui lavori eseguiti è di 24 mesi dalla data di completamento, salvo diversa indicazione. La responsabilità di Inparo GmbH è limitata ai danni diretti causati da negligenza grave o dolo ed è comunque limitata all&#39;importo pagato dal cliente per il servizio in questione.\n            ", [5117, 5518], "\n              Inparo GmbH garantisce che i servizi saranno eseguiti con la dovuta diligenza professionale. La garanzia sui lavori eseguiti è di 24 mesi dalla data di completamento, salvo diversa indicazione. La responsabilità di Inparo GmbH è limitata ai danni diretti causati da negligenza grave o dolo ed è comunque limitata all&rsquo;importo pagato dal cliente per il servizio in questione.\n            ", [5675, 5919], "\n              L&apos;utente si impegna a utilizzare il sito web di Inparo GmbH in modo legale e conforme ai presenti Termini. È vietato qualsiasi utilizzo che possa causare danni al sito o comprometterne la funzionalità o la sicurezza.\n            ", [5675, 5919], "\n              L&lsquo;utente si impegna a utilizzare il sito web di Inparo GmbH in modo legale e conforme ai presenti Termini. È vietato qualsiasi utilizzo che possa causare danni al sito o comprometterne la funzionalità o la sicurezza.\n            ", [5675, 5919], "\n              L&#39;utente si impegna a utilizzare il sito web di Inparo GmbH in modo legale e conforme ai presenti Termini. È vietato qualsiasi utilizzo che possa causare danni al sito o comprometterne la funzionalità o la sicurezza.\n            ", [5675, 5919], "\n              L&rsquo;utente si impegna a utilizzare il sito web di Inparo GmbH in modo legale e conforme ai presenti Termini. È vietato qualsiasi utilizzo che possa causare danni al sito o comprometterne la funzionalità o la sicurezza.\n            ", [6898, 7268], "\n              I presenti Termini sono regolati dal diritto s<PERSON>zzero, con esclusione delle norme di diritto internazionale privato e della Convenzione di Vienna sulla vendita internazionale di merci. Per ogni controversia relativa all&apos;interpretazione, esecuzione o risoluzione dei presenti Termini sarà competente in via esclusiva il foro di Zugo, Svizzera.\n            ", [6898, 7268], "\n              I presenti Termini sono regolati dal diritto s<PERSON>zzero, con esclusione delle norme di diritto internazionale privato e della Convenzione di Vienna sulla vendita internazionale di merci. Per ogni controversia relativa all&lsquo;interpretazione, esecuzione o risoluzione dei presenti Termini sarà competente in via esclusiva il foro di Zugo, Svizzera.\n            ", [6898, 7268], "\n              I presenti Termini sono regolati dal diritto s<PERSON>zzero, con esclusione delle norme di diritto internazionale privato e della Convenzione di Vienna sulla vendita internazionale di merci. Per ogni controversia relativa all&#39;interpretazione, esecuzione o risoluzione dei presenti Termini sarà competente in via esclusiva il foro di Zugo, Svizzera.\n            ", [6898, 7268], "\n              I presenti Termini sono regolati dal diritto s<PERSON>zzero, con esclusione delle norme di diritto internazionale privato e della Convenzione di Vienna sulla vendita internazionale di merci. Per ogni controversia relativa all&rsquo;interpretazione, esecuzione o risoluzione dei presenti Termini sarà competente in via esclusiva il foro di Zugo, Svizzera.\n            ", [7425, 7734], "\n              Inparo GmbH si riserva il diritto di modificare i presenti Termini in qualsiasi momento. Le modifiche saranno pubblicate sul sito web e saranno efficaci dalla data di pubblicazione. L&apos;utilizzo continuato del sito o dei servizi dopo tale data implica l'accettazione delle modifiche.\n            ", [7425, 7734], "\n              Inparo GmbH si riserva il diritto di modificare i presenti Termini in qualsiasi momento. Le modifiche saranno pubblicate sul sito web e saranno efficaci dalla data di pubblicazione. L&lsquo;utilizzo continuato del sito o dei servizi dopo tale data implica l'accettazione delle modifiche.\n            ", [7425, 7734], "\n              Inparo GmbH si riserva il diritto di modificare i presenti Termini in qualsiasi momento. Le modifiche saranno pubblicate sul sito web e saranno efficaci dalla data di pubblicazione. L&#39;utilizzo continuato del sito o dei servizi dopo tale data implica l'accettazione delle modifiche.\n            ", [7425, 7734], "\n              Inparo GmbH si riserva il diritto di modificare i presenti Termini in qualsiasi momento. Le modifiche saranno pubblicate sul sito web e saranno efficaci dalla data di pubblicazione. L&rsquo;utilizzo continuato del sito o dei servizi dopo tale data implica l'accettazione delle modifiche.\n            ", [7425, 7734], "\n              Inparo GmbH si riserva il diritto di modificare i presenti Termini in qualsiasi momento. Le modifiche saranno pubblicate sul sito web e saranno efficaci dalla data di pubblicazione. L'utilizzo continuato del sito o dei servizi dopo tale data implica l&apos;accettazione delle modifiche.\n            ", [7425, 7734], "\n              Inparo GmbH si riserva il diritto di modificare i presenti Termini in qualsiasi momento. Le modifiche saranno pubblicate sul sito web e saranno efficaci dalla data di pubblicazione. L'utilizzo continuato del sito o dei servizi dopo tale data implica l&lsquo;accettazione delle modifiche.\n            ", [7425, 7734], "\n              Inparo GmbH si riserva il diritto di modificare i presenti Termini in qualsiasi momento. Le modifiche saranno pubblicate sul sito web e saranno efficaci dalla data di pubblicazione. L'utilizzo continuato del sito o dei servizi dopo tale data implica l&#39;accettazione delle modifiche.\n            ", [7425, 7734], "\n              Inparo GmbH si riserva il diritto di modificare i presenti Termini in qualsiasi momento. Le modifiche saranno pubblicate sul sito web e saranno efficaci dalla data di pubblicazione. L'utilizzo continuato del sito o dei servizi dopo tale data implica l&rsquo;accettazione delle modifiche.\n            ", [7879, 8013], "\n              Per qualsiasi domanda o richiesta relativa ai presenti Termini, vi preghiamo di contattarci all&apos;indirizzo:\n            ", [7879, 8013], "\n              Per qualsiasi domanda o richiesta relativa ai presenti Termini, vi preghiamo di contattarci all&lsquo;indirizzo:\n            ", [7879, 8013], "\n              Per qualsiasi domanda o richiesta relativa ai presenti Termini, vi preghiamo di contattarci all&#39;indirizzo:\n            ", [7879, 8013], "\n              Per qualsiasi domanda o richiesta relativa ai presenti Termini, vi preghiamo di contattarci all&rsquo;indirizzo:\n            ", [466, 469], [466, 469], [9257, 9260], [9257, 9260], [11704, 11707], [11704, 11707], [14053, 14056], [14053, 14056]]