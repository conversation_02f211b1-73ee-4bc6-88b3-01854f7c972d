{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:file((?!\\.well-known(?:/.*)?)(?:[^/]+/)*[^/]+\\.\\w+)/", "destination": "/:file", "internal": true, "missing": [{"type": "header", "key": "x-nextjs-data"}], "statusCode": 308, "regex": "^(?:/((?!\\.well-known(?:/.*)?)(?:[^/]+/)*[^/]+\\.\\w+))/$"}, {"source": "/:notfile((?!\\.well-known(?:/.*)?)(?:[^/]+/)*[^/\\.]+)", "destination": "/:notfile/", "internal": true, "statusCode": 308, "regex": "^(?:/((?!\\.well-known(?:/.*)?)(?:[^/]+/)*[^/\\.]+))$"}], "headers": [], "dynamicRoutes": [{"page": "/[lang]", "regex": "^/([^/]+?)(?:/)?$", "routeKeys": {"nxtPlang": "nxtPlang"}, "namedRegex": "^/(?<nxtPlang>[^/]+?)(?:/)?$"}, {"page": "/[lang]/contact", "regex": "^/([^/]+?)/contact(?:/)?$", "routeKeys": {"nxtPlang": "nxtPlang"}, "namedRegex": "^/(?<nxtPlang>[^/]+?)/contact(?:/)?$"}, {"page": "/[lang]/emergency", "regex": "^/([^/]+?)/emergency(?:/)?$", "routeKeys": {"nxtPlang": "nxtPlang"}, "namedRegex": "^/(?<nxtPlang>[^/]+?)/emergency(?:/)?$"}, {"page": "/[lang]/privacy-policy", "regex": "^/([^/]+?)/privacy\\-policy(?:/)?$", "routeKeys": {"nxtPlang": "nxtPlang"}, "namedRegex": "^/(?<nxtPlang>[^/]+?)/privacy\\-policy(?:/)?$"}, {"page": "/[lang]/services", "regex": "^/([^/]+?)/services(?:/)?$", "routeKeys": {"nxtPlang": "nxtPlang"}, "namedRegex": "^/(?<nxtPlang>[^/]+?)/services(?:/)?$"}, {"page": "/[lang]/terms", "regex": "^/([^/]+?)/terms(?:/)?$", "routeKeys": {"nxtPlang": "nxtPlang"}, "namedRegex": "^/(?<nxtPlang>[^/]+?)/terms(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc"}, "rewrites": []}