"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[468],{7468:(e,r,t)=>{t.d(r,{I:()=>i});var s=t(5155),o=t(2115),a=t(3478),n=t(5683);function i(e){var r,t,i,d;let{t:l,lang:c}=e,[m,h]=(0,o.useState)({name:"",phone:"",address:"",emergency_type:"",message:"",privacy:!1}),[g,u]=(0,o.useState)({}),[p,x]=(0,o.useState)(!1),[v,w]=(0,o.useState)("idle"),f=()=>{let e={};return m.name.trim()||(e.name="Nome richiesto"),m.phone.trim()?/^[+]?[(]?[0-9]{3}[)]?[-\s.]?[0-9]{3}[-\s.]?[0-9]{4,6}$/.test(m.phone)||(e.phone="Numero di telefono non valido"):e.phone="Telefono richiesto",m.address.trim()||(e.address="Indirizzo richiesto"),m.emergency_type||(e.emergency_type="Tipo di emergenza richiesto"),m.privacy||(e.privacy="Accetta la privacy policy"),u(e),0===Object.keys(e).length},b=async e=>{if(e.preventDefault(),f()){x(!0),w("idle");try{let e=m.emergency_type||"Non specificato",r="EMERGENZA: ".concat(e," - ").concat(m.name);if((await fetch("/api/send-email.php",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:m.name,email:"<EMAIL>",subject:r,message:"RICHIESTA DI EMERGENZA\n\nNome: ".concat(m.name,"\nTelefono: ").concat(m.phone,"\nIndirizzo: ").concat(m.address,"\nTipo di emergenza: ").concat(e,"\nMessaggio: ").concat(m.message,"\n\nInviato dal form di emergenza del sito web."),isEmergency:!0})})).ok)w("success"),h({name:"",phone:"",address:"",emergency_type:"",message:"",privacy:!1});else throw Error("Errore nell'invio dell'email")}catch(e){console.error("Errore durante l'invio dell'email:",e),w("error")}finally{x(!1)}}};return(0,s.jsxs)(a.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"relative overflow-hidden bg-gradient-to-br from-red-50 via-white to-orange-50 p-8 rounded-2xl shadow-xl border border-red-100",children:[(0,s.jsx)("div",{className:"absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-red-500/10 to-orange-500/10 rounded-full -mr-16 -mt-16"}),(0,s.jsx)("div",{className:"absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-red-500/10 to-orange-500/10 rounded-full -ml-12 -mb-12"}),(0,s.jsxs)("div",{className:"flex items-center mb-6",children:[(0,s.jsx)("div",{className:"bg-gradient-to-r from-red-600 to-red-500 p-3 rounded-full shadow-md mr-4",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 text-white",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 10V3L4 14h7v7l9-11h-7z"})})}),(0,s.jsx)("h3",{className:"text-2xl font-bold text-gray-900",children:l.contact.emergency})]}),(0,s.jsx)("p",{className:"text-gray-700 mb-8 ml-16",children:l.contact.description}),(0,s.jsxs)("form",{onSubmit:b,className:"space-y-6 relative z-10 mx-auto max-w-3xl",children:[(0,s.jsxs)("div",{className:"relative group",children:[(0,s.jsx)("div",{className:"absolute -inset-0.5 bg-gradient-to-r from-red-200 to-orange-200 rounded-lg blur opacity-30 group-hover:opacity-50 transition duration-300"}),(0,s.jsxs)("div",{className:"relative bg-white p-5 rounded-lg shadow-sm hover:shadow transition",children:[(0,s.jsxs)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-1 flex items-center",children:[(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-2 text-red-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})}),l.contact.form.name]}),(0,s.jsx)("input",{type:"text",id:"name",name:"name",value:m.name,onChange:e=>h(r=>({...r,name:e.target.value})),className:"block w-full px-4 py-3 rounded-lg bg-gray-50 border-transparent text-gray-900 focus:border-red-500 focus:bg-white focus:ring-2 focus:ring-red-200 transition duration-200 ".concat(g.name?"border-red-500 ring-2 ring-red-200":""),placeholder:(null===(r=l.contact.form.placeholders)||void 0===r?void 0:r.name)||"Es. Mario Rossi","aria-invalid":g.name?"true":"false","aria-describedby":g.name?"name-error":void 0}),g.name&&(0,s.jsxs)("p",{id:"name-error",className:"mt-1 text-sm text-red-600 flex items-center",children:[(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"})}),g.name]})]})]}),(0,s.jsxs)("div",{className:"relative group",children:[(0,s.jsx)("div",{className:"absolute -inset-0.5 bg-gradient-to-r from-red-200 to-orange-200 rounded-lg blur opacity-30 group-hover:opacity-50 transition duration-300"}),(0,s.jsxs)("div",{className:"relative bg-white p-5 rounded-lg shadow-sm hover:shadow transition",children:[(0,s.jsxs)("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700 mb-1 flex items-center",children:[(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-2 text-red-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"})}),l.contact.form.phone]}),(0,s.jsx)("input",{type:"tel",id:"phone",name:"phone",value:m.phone,onChange:e=>h(r=>({...r,phone:e.target.value})),className:"block w-full px-4 py-3 rounded-lg bg-gray-50 border-transparent text-gray-900 focus:border-red-500 focus:bg-white focus:ring-2 focus:ring-red-200 transition duration-200 ".concat(g.phone?"border-red-500 ring-2 ring-red-200":""),placeholder:(null===(t=l.contact.form.placeholders)||void 0===t?void 0:t.phone)||"+41 XX XXX XX XX","aria-invalid":g.phone?"true":"false","aria-describedby":g.phone?"phone-error":void 0}),g.phone&&(0,s.jsxs)("p",{id:"phone-error",className:"mt-1 text-sm text-red-600 flex items-center",children:[(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"})}),g.phone]})]})]}),(0,s.jsxs)("div",{className:"relative group",children:[(0,s.jsx)("div",{className:"absolute -inset-0.5 bg-gradient-to-r from-red-200 to-orange-200 rounded-lg blur opacity-30 group-hover:opacity-50 transition duration-300"}),(0,s.jsxs)("div",{className:"relative bg-white p-5 rounded-lg shadow-sm hover:shadow transition",children:[(0,s.jsxs)("label",{htmlFor:"address",className:"block text-sm font-medium text-gray-700 mb-1 flex items-center",children:[(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-2 text-red-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:[(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})]}),l.contact.form.address||"Indirizzo"]}),(0,s.jsx)("input",{type:"text",id:"address",name:"address",value:m.address,onChange:e=>h(r=>({...r,address:e.target.value})),className:"block w-full px-4 py-3 rounded-lg bg-gray-50 border-transparent text-gray-900 focus:border-red-500 focus:bg-white focus:ring-2 focus:ring-red-200 transition duration-200 ".concat(g.address?"border-red-500 ring-2 ring-red-200":""),placeholder:(null===(i=l.contact.form.placeholders)||void 0===i?void 0:i.address)||"Via, numero civico, cap, citt\xe0","aria-invalid":g.address?"true":"false","aria-describedby":g.address?"address-error":void 0}),g.address&&(0,s.jsxs)("p",{id:"address-error",className:"mt-1 text-sm text-red-600 flex items-center",children:[(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"})}),g.address]})]})]}),(0,s.jsxs)("div",{className:"relative group",children:[(0,s.jsx)("div",{className:"absolute -inset-0.5 bg-gradient-to-r from-red-200 to-orange-200 rounded-lg blur opacity-30 group-hover:opacity-50 transition duration-300"}),(0,s.jsxs)("div",{className:"relative bg-white p-5 rounded-lg shadow-sm hover:shadow transition",children:[(0,s.jsxs)("label",{htmlFor:"emergency_type",className:"block text-sm font-medium text-gray-700 mb-1 flex items-center",children:[(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-2 text-red-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 10V3L4 14h7v7l9-11h-7z"})}),l.contact.form.emergency_type||"Tipo di emergenza"]}),(0,s.jsxs)("select",{id:"emergency_type",name:"emergency_type",value:m.emergency_type,onChange:e=>h(r=>({...r,emergency_type:e.target.value})),className:"block w-full px-4 py-3 rounded-lg bg-gray-50 border-transparent text-gray-900 focus:border-red-500 focus:bg-white focus:ring-2 focus:ring-red-200 transition duration-200 ".concat(g.emergency_type?"border-red-500 ring-2 ring-red-200":""),"aria-invalid":g.emergency_type?"true":"false","aria-describedby":g.emergency_type?"emergency-type-error":void 0,children:[(0,s.jsx)("option",{value:"",children:"it"===c?"Seleziona il tipo di emergenza":"fr"===c?"S\xe9lectionnez le type d'urgence":"W\xe4hlen Sie den Notfalltyp"}),"it"===c?["Allagamento","Perdita d'acqua","Tubature rotte","Altro"].map(e=>(0,s.jsx)("option",{value:e,children:e},e)):"fr"===c?["Inondation","Fuite d'eau","Tuyaux cass\xe9s","Autre"].map(e=>(0,s.jsx)("option",{value:e,children:e},e)):["\xdcberschwemmung","Wasserleck","Gebrochene Rohre","Andere"].map(e=>(0,s.jsx)("option",{value:e,children:e},e))]}),g.emergency_type&&(0,s.jsxs)("p",{id:"emergency-type-error",className:"mt-1 text-sm text-red-600 flex items-center",children:[(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"})}),g.emergency_type]})]})]}),(0,s.jsxs)("div",{className:"relative group",children:[(0,s.jsx)("div",{className:"absolute -inset-0.5 bg-gradient-to-r from-red-200 to-orange-200 rounded-lg blur opacity-30 group-hover:opacity-50 transition duration-300"}),(0,s.jsxs)("div",{className:"relative bg-white p-5 rounded-lg shadow-sm hover:shadow transition",children:[(0,s.jsxs)("label",{htmlFor:"message",className:"block text-sm font-medium text-gray-700 mb-1 flex items-center",children:[(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-2 text-red-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"})}),l.contact.form.message]}),(0,s.jsx)("textarea",{id:"message",name:"message",rows:4,value:m.message,onChange:e=>h(r=>({...r,message:e.target.value})),className:"block w-full px-4 py-3 rounded-lg bg-gray-50 border-transparent text-gray-900 focus:border-red-500 focus:bg-white focus:ring-2 focus:ring-red-200 transition duration-200",placeholder:(null===(d=l.contact.form.placeholders)||void 0===d?void 0:d.message)||"Scrivi qui il tuo messaggio..."})]})]}),(0,s.jsxs)("div",{className:"relative group",children:[(0,s.jsx)("div",{className:"absolute -inset-0.5 bg-gradient-to-r from-red-200 to-orange-200 rounded-lg blur opacity-30 group-hover:opacity-50 transition duration-300"}),(0,s.jsx)("div",{className:"relative bg-white p-5 rounded-lg shadow-sm hover:shadow transition",children:(0,s.jsxs)("div",{className:"flex items-start",children:[(0,s.jsx)("div",{className:"flex items-center h-5",children:(0,s.jsx)("input",{id:"privacy",name:"privacy",type:"checkbox",checked:m.privacy,onChange:e=>h(r=>({...r,privacy:e.target.checked})),className:"h-5 w-5 rounded border-transparent bg-gray-100 text-red-600 focus:ring-red-400 transition-colors ".concat(g.privacy?"border-red-500 ring-2 ring-red-200":""),"aria-invalid":g.privacy?"true":"false","aria-describedby":g.privacy?"privacy-error":void 0})}),(0,s.jsxs)("div",{className:"ml-3",children:[(0,s.jsxs)("label",{htmlFor:"privacy",className:"text-sm font-medium text-gray-700 flex items-center",children:[(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-2 text-red-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"})}),"it"===c?"Accetto la privacy policy":"fr"===c?"J'accepte la politique de confidentialit\xe9":"Ich akzeptiere die Datenschutzrichtlinie"]}),g.privacy&&(0,s.jsxs)("p",{id:"privacy-error",className:"mt-1 text-sm text-red-600 flex items-center ml-6",children:[(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"})}),g.privacy]})]})]})})]}),(0,s.jsxs)("div",{className:"relative group mt-8",children:[(0,s.jsx)("div",{className:"absolute -inset-1 bg-gradient-to-r from-red-600 to-orange-500 rounded-lg blur opacity-40 group-hover:opacity-75 transition duration-300 animate-pulse",style:{zIndex:0}}),(0,s.jsx)(a.P.button,{type:"submit",disabled:p,className:"relative w-full flex justify-center items-center py-4 px-6 border border-transparent rounded-lg text-base font-bold text-white bg-gradient-to-r from-red-600 to-red-500 shadow-lg hover:from-red-500 hover:to-red-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:from-gray-400 disabled:to-gray-300 disabled:cursor-not-allowed transition-all duration-300 hover:shadow-xl z-10",whileHover:{translateY:-2},whileTap:{scale:.98},children:p?(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(a.P.div,{className:"h-5 w-5 mr-3 border-2 border-white border-t-transparent rounded-full",animate:{rotate:360},transition:{duration:1,repeat:1/0,ease:"linear"}}),(0,s.jsx)("span",{children:"Invio in corso..."})]}):(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:[(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 14l5-5-5-5"}),(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9H5"})]}),l.contact.form.submit||"Invia richiesta"]})})]})]}),(0,s.jsx)(n.N,{children:"idle"!==v&&(0,s.jsxs)(a.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{type:"spring",damping:20},className:"mt-8 p-6 rounded-xl shadow-lg border ".concat("success"===v?"bg-gradient-to-r from-green-50 to-green-100 border-green-200":"bg-gradient-to-r from-red-50 to-red-100 border-red-200"),children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"flex-shrink-0 p-3 rounded-full ".concat("success"===v?"bg-green-100":"bg-red-100"),children:"success"===v?(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 text-green-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}):(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 text-red-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})}),(0,s.jsxs)("div",{className:"ml-4",children:[(0,s.jsx)("h3",{className:"text-lg font-bold ".concat("success"===v?"text-green-800":"text-red-800"),children:"success"===v?"Richiesta inviata con successo!":"Errore durante l'invio"}),(0,s.jsx)("p",{className:"mt-1 text-sm ".concat("success"===v?"text-green-700":"text-red-700"),children:"success"===v?"Ti contatteremo al pi\xf9 presto.":"Si \xe8 verificato un errore. Riprova pi\xf9 tardi."})]})]}),"success"===v&&(0,s.jsx)("div",{className:"mt-4 flex justify-end",children:(0,s.jsx)(a.P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>w("idle"),className:"px-4 py-2 text-sm font-medium text-green-700 bg-green-100 rounded-md hover:bg-green-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500",children:"Chiudi messaggio"})})]})})]})}}}]);