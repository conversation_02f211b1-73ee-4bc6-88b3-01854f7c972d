"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[333],{5683:(e,t,r)=>{r.d(t,{N:()=>y});var n=r(5155),o=r(2115),i=r(4710),l=r(9234),s=r(9656),c=r(7249);class a extends o.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,r=e instanceof HTMLElement&&e.offsetWidth||0,n=this.props.sizeRef.current;n.height=t.offsetHeight||0,n.width=t.offsetWidth||0,n.top=t.offsetTop,n.left=t.offsetLeft,n.right=r-n.width-n.left}return null}componentDidUpdate(){}render(){return this.props.children}}function u(e){let{children:t,isPresent:r,anchorX:i}=e,l=(0,o.useId)(),s=(0,o.useRef)(null),u=(0,o.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:f}=(0,o.useContext)(c.Q);return(0,o.useInsertionEffect)(()=>{let{width:e,height:t,top:n,left:o,right:c}=u.current;if(r||!s.current||!e||!t)return;s.current.dataset.motionPopId=l;let a=document.createElement("style");return f&&(a.nonce=f),document.head.appendChild(a),a.sheet&&a.sheet.insertRule('\n          [data-motion-pop-id="'.concat(l,'"] {\n            position: absolute !important;\n            width: ').concat(e,"px !important;\n            height: ").concat(t,"px !important;\n            ").concat("left"===i?"left: ".concat(o):"right: ".concat(c),"px !important;\n            top: ").concat(n,"px !important;\n          }\n        ")),()=>{document.head.removeChild(a)}},[r]),(0,n.jsx)(a,{isPresent:r,childRef:s,sizeRef:u,children:o.cloneElement(t,{ref:s})})}let f=e=>{let{children:t,initial:r,isPresent:i,onExitComplete:c,custom:a,presenceAffectsLayout:f,mode:h,anchorX:d}=e,m=(0,l.M)(p),v=(0,o.useId)(),y=(0,o.useCallback)(e=>{for(let t of(m.set(e,!0),m.values()))if(!t)return;c&&c()},[m,c]),b=(0,o.useMemo)(()=>({id:v,initial:r,isPresent:i,custom:a,onExitComplete:y,register:e=>(m.set(e,!1),()=>m.delete(e))}),f?[Math.random(),y]:[i,y]);return(0,o.useMemo)(()=>{m.forEach((e,t)=>m.set(t,!1))},[i]),o.useEffect(()=>{i||m.size||!c||c()},[i]),"popLayout"===h&&(t=(0,n.jsx)(u,{isPresent:i,anchorX:d,children:t})),(0,n.jsx)(s.t.Provider,{value:b,children:t})};function p(){return new Map}var h=r(5087);let d=e=>e.key||"";function m(e){let t=[];return o.Children.forEach(e,e=>{(0,o.isValidElement)(e)&&t.push(e)}),t}var v=r(5403);let y=e=>{let{children:t,custom:r,initial:s=!0,onExitComplete:c,presenceAffectsLayout:a=!0,mode:u="sync",propagate:p=!1,anchorX:y="left"}=e,[b,g]=(0,h.xQ)(p),O=(0,o.useMemo)(()=>m(t),[t]),j=p&&!b?[]:O.map(d),w=(0,o.useRef)(!0),E=(0,o.useRef)(O),P=(0,l.M)(()=>new Map),[x,C]=(0,o.useState)(O),[k,N]=(0,o.useState)(O);(0,v.E)(()=>{w.current=!1,E.current=O;for(let e=0;e<k.length;e++){let t=d(k[e]);j.includes(t)?P.delete(t):!0!==P.get(t)&&P.set(t,!1)}},[k,j.length,j.join("-")]);let M=[];if(O!==x){let e=[...O];for(let t=0;t<k.length;t++){let r=k[t],n=d(r);j.includes(n)||(e.splice(t,0,r),M.push(r))}return"wait"===u&&M.length&&(e=M),N(m(e)),C(O),null}let{forceRender:R}=(0,o.useContext)(i.L);return(0,n.jsx)(n.Fragment,{children:k.map(e=>{let t=d(e),o=(!p||!!b)&&(O===k||j.includes(t));return(0,n.jsx)(f,{isPresent:o,initial:(!w.current||!!s)&&void 0,custom:r,presenceAffectsLayout:a,mode:u,onExitComplete:o?void 0:()=>{if(!P.has(t))return;P.set(t,!0);let e=!0;P.forEach(t=>{t||(e=!1)}),e&&(null==R||R(),N(E.current),p&&(null==g||g()),c&&c())},anchorX:y,children:e},t)})})}},3435:(e,t,r)=>{r.d(t,{k5:()=>u});var n=r(2115),o={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},i=n.createContext&&n.createContext(o),l=["attr","size","title"];function s(){return(s=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function c(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function a(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?c(Object(r),!0).forEach(function(t){var n,o;n=t,o=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):c(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function u(e){return t=>n.createElement(f,s({attr:a({},e.attr)},t),function e(t){return t&&t.map((t,r)=>n.createElement(t.tag,a({key:r},t.attr),e(t.child)))}(e.child))}function f(e){var t=t=>{var r,{attr:o,size:i,title:c}=e,u=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,l),f=i||t.size||"1em";return t.className&&(r=t.className),e.className&&(r=(r?r+" ":"")+e.className),n.createElement("svg",s({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,o,u,{className:r,style:a(a({color:e.color||t.color},t.style),e.style),height:f,width:f,xmlns:"http://www.w3.org/2000/svg"}),c&&n.createElement("title",null,c),e.children)};return void 0!==i?n.createElement(i.Consumer,null,e=>t(e)):t(o)}}}]);