(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[160,333],{6859:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,8173,23)),Promise.resolve().then(r.t.bind(r,347,23)),Promise.resolve().then(r.t.bind(r,2813,23)),Promise.resolve().then(r.bind(r,8534)),Promise.resolve().then(r.bind(r,1640))},6046:(e,t,r)=>{"use strict";var n=r(6658);r.o(n,"usePathname")&&r.d(t,{usePathname:function(){return n.usePathname}}),r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}})},8534:(e,t,r)=>{"use strict";r.d(t,{LanguageProvider:()=>l,o:()=>d});var n=r(5155),a=r(2115),s=r(6046),i=r(5683),o=r(3478);let c=(0,a.createContext)({locale:"it",setLocale:()=>{}});function l(e){let{children:t,initialLocale:r="it"}=e,l=(0,s.useRouter)(),d=(0,s.usePathname)(),[u,m]=(0,a.useState)(!1),[f,h]=(0,a.useState)(r);(0,a.useEffect)(()=>{m(!0)},[]);let[p,x]=(0,a.useState)(!1);return u?(0,n.jsx)(c.Provider,{value:{locale:f,setLocale:e=>{e!==f&&u&&(x(!0),setTimeout(()=>{h(e);let t=(null==d?void 0:d.replace(/^\/[^\/]+/,"/".concat(e)))||"/".concat(e);l.push(t)},300))}},children:u?(0,n.jsx)(i.N,{mode:"wait",children:(0,n.jsx)(o.P.div,{initial:{opacity:p?0:1},animate:{opacity:1},exit:{opacity:0},transition:{duration:.3,ease:"easeInOut"},onAnimationComplete:()=>x(!1),className:"w-full",children:t},f)}):(0,n.jsx)("div",{className:"w-full",children:t})}):(0,n.jsx)(c.Provider,{value:{locale:r,setLocale:()=>{}},children:t})}function d(){let e=(0,a.useContext)(c);if(!e)throw Error("useLanguage must be used within a LanguageProvider");return e}},1640:(e,t,r)=>{"use strict";r.d(t,{default:()=>h});var n=r(5155),a=r(2115),s=r(8173),i=r.n(s),o=r(6046),c=r(5683),l=r(3478),d=r(1536),u=r(8534);let m=[{code:"it",name:"Italiano",flag:"\uD83C\uDDEE\uD83C\uDDF9"},{code:"de",name:"Deutsch",flag:"\uD83C\uDDE8\uD83C\uDDED"},{code:"fr",name:"Fran\xe7ais",flag:"\uD83C\uDDEB\uD83C\uDDF7"}];function f(e){let{currentLang:t}=e,r=(0,o.usePathname)(),{setLocale:a}=(0,u.o)(),s=(null==r?void 0:r.split("/"))||[];s.length>2&&s.slice(2).join("/");let i={active:{scale:1.05},inactive:{scale:1}},c={active:{fontWeight:600},inactive:{fontWeight:400}};return(0,n.jsx)("div",{className:"flex items-center space-x-2 bg-white/80 backdrop-blur-sm rounded-full p-1 shadow-lg",children:m.map(e=>{let r=t===e.code;return(0,n.jsxs)(l.P.button,{onClick:()=>a(e.code),className:"relative px-4 py-2 text-sm font-medium rounded-full",variants:i,animate:r?"active":"inactive",whileHover:{scale:1.05},whileTap:{scale:.95},transition:{type:"spring",stiffness:500,damping:30},children:[(0,n.jsxs)(l.P.span,{className:"flex items-center space-x-2",variants:c,animate:r?"active":"inactive",transition:{duration:.3},children:[(0,n.jsx)("span",{className:"text-base",children:e.flag}),(0,n.jsx)(l.P.span,{animate:{color:r?"#2563eb":"#4b5563"},transition:{duration:.3},children:e.name})]}),(0,n.jsx)(l.P.div,{layoutId:"activeLocale",className:"absolute inset-0 rounded-full -z-10",style:{backgroundColor:r?"rgba(219, 234, 254, 0.8)":"transparent"},initial:!1,animate:{backgroundColor:r?"rgba(219, 234, 254, 0.8)":"transparent"},transition:{type:"spring",stiffness:500,damping:30,mass:1}})]},e.code)})})}function h(e){let{lang:t,translations:r}=e,[s,u]=(0,a.useState)(!1),[m,h]=(0,a.useState)(!1),[p,x]=(0,a.useState)(!1),v=(0,o.usePathname)();if((0,a.useEffect)(()=>{x(!0);let e=()=>{h(window.scrollY>0)};return window.addEventListener("scroll",e),()=>{window.removeEventListener("scroll",e)}},[]),!p)return null;let b=v==="/".concat(t)||"/"===v,g=v.includes("/services"),y=v.includes("/contact"),j=v.includes("/emergency");return(0,n.jsxs)("nav",{className:"fixed w-full z-50 transition-all duration-300 ".concat(m?"bg-white/95 backdrop-blur-sm shadow-lg":"bg-white/80"),children:[(0,n.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,n.jsxs)("div",{className:"flex justify-between h-16",children:[(0,n.jsx)("div",{className:"flex",children:(0,n.jsx)(i(),{href:"/".concat(t),className:"flex-shrink-0 flex items-center",children:(0,n.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"Inparo"})})}),(0,n.jsxs)("div",{className:"hidden md:flex md:items-center md:space-x-8",children:[(0,n.jsx)(i(),{href:"/".concat(t),className:"text-gray-900 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium ".concat(b?"border-b-2 border-blue-600 font-medium":""),children:r.nav.home}),(0,n.jsx)(i(),{href:"/".concat(t,"/services"),className:"text-gray-900 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium ".concat(g?"border-b-2 border-blue-600 font-medium":""),children:r.nav.services}),(0,n.jsx)(i(),{href:"/".concat(t,"/contact"),className:"text-gray-900 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium ".concat(y?"border-b-2 border-blue-600 font-medium":""),children:r.nav.contact}),(0,n.jsx)(i(),{href:"/".concat(t,"/emergency"),className:"".concat(j?"bg-red-700":"bg-red-600"," text-white hover:bg-red-700 px-3 py-2 rounded-md text-sm font-medium"),children:r.nav.emergency}),(0,n.jsx)(f,{currentLang:t})]}),(0,n.jsx)("div",{className:"md:hidden flex items-center",children:(0,n.jsx)("button",{onClick:()=>u(!s),className:"inline-flex items-center justify-center p-2 rounded-md text-gray-900 hover:text-gray-700 focus:outline-none",children:s?(0,n.jsx)(d.QCr,{className:"h-6 w-6"}):(0,n.jsx)(d.OXb,{className:"h-6 w-6"})})})]})}),(0,n.jsx)(c.N,{children:s&&(0,n.jsx)(l.P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},exit:{opacity:0,y:-10},className:"md:hidden bg-white",children:(0,n.jsxs)("div",{className:"px-2 pt-2 pb-3 space-y-1 sm:px-3",children:[(0,n.jsx)(i(),{href:"/".concat(t),onClick:()=>u(!1),className:"block px-3 py-2 rounded-md text-base font-medium ".concat(b?"border-b-2 border-blue-600 text-gray-900":"text-gray-900 hover:bg-gray-50"),children:r.nav.home}),(0,n.jsx)(i(),{href:"/".concat(t,"/services"),onClick:()=>u(!1),className:"block px-3 py-2 rounded-md text-base font-medium ".concat(g?"border-b-2 border-blue-600 text-gray-900":"text-gray-900 hover:bg-gray-50"),children:r.nav.services}),(0,n.jsx)(i(),{href:"/".concat(t,"/contact"),onClick:()=>u(!1),className:"block px-3 py-2 rounded-md text-base font-medium ".concat(y?"border-b-2 border-blue-600 text-gray-900":"text-gray-900 hover:bg-gray-50"),children:r.nav.contact}),(0,n.jsx)(i(),{href:"/".concat(t,"/emergency"),onClick:()=>u(!1),className:"".concat(j?"bg-red-700":"bg-red-600"," text-white hover:bg-red-700 block px-3 py-2 rounded-md text-base font-medium"),children:r.nav.emergency}),(0,n.jsx)("div",{className:"px-3 py-2",children:(0,n.jsx)(f,{currentLang:t})})]})})})]})}},347:()=>{},2813:e=>{e.exports={style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"},className:"__className_d65c78"}},5683:(e,t,r)=>{"use strict";r.d(t,{N:()=>v});var n=r(5155),a=r(2115),s=r(4710),i=r(9234),o=r(9656),c=r(7249);class l extends a.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,r=e instanceof HTMLElement&&e.offsetWidth||0,n=this.props.sizeRef.current;n.height=t.offsetHeight||0,n.width=t.offsetWidth||0,n.top=t.offsetTop,n.left=t.offsetLeft,n.right=r-n.width-n.left}return null}componentDidUpdate(){}render(){return this.props.children}}function d(e){let{children:t,isPresent:r,anchorX:s}=e,i=(0,a.useId)(),o=(0,a.useRef)(null),d=(0,a.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:u}=(0,a.useContext)(c.Q);return(0,a.useInsertionEffect)(()=>{let{width:e,height:t,top:n,left:a,right:c}=d.current;if(r||!o.current||!e||!t)return;o.current.dataset.motionPopId=i;let l=document.createElement("style");return u&&(l.nonce=u),document.head.appendChild(l),l.sheet&&l.sheet.insertRule('\n          [data-motion-pop-id="'.concat(i,'"] {\n            position: absolute !important;\n            width: ').concat(e,"px !important;\n            height: ").concat(t,"px !important;\n            ").concat("left"===s?"left: ".concat(a):"right: ".concat(c),"px !important;\n            top: ").concat(n,"px !important;\n          }\n        ")),()=>{document.head.removeChild(l)}},[r]),(0,n.jsx)(l,{isPresent:r,childRef:o,sizeRef:d,children:a.cloneElement(t,{ref:o})})}let u=e=>{let{children:t,initial:r,isPresent:s,onExitComplete:c,custom:l,presenceAffectsLayout:u,mode:f,anchorX:h}=e,p=(0,i.M)(m),x=(0,a.useId)(),v=(0,a.useCallback)(e=>{for(let t of(p.set(e,!0),p.values()))if(!t)return;c&&c()},[p,c]),b=(0,a.useMemo)(()=>({id:x,initial:r,isPresent:s,custom:l,onExitComplete:v,register:e=>(p.set(e,!1),()=>p.delete(e))}),u?[Math.random(),v]:[s,v]);return(0,a.useMemo)(()=>{p.forEach((e,t)=>p.set(t,!1))},[s]),a.useEffect(()=>{s||p.size||!c||c()},[s]),"popLayout"===f&&(t=(0,n.jsx)(d,{isPresent:s,anchorX:h,children:t})),(0,n.jsx)(o.t.Provider,{value:b,children:t})};function m(){return new Map}var f=r(5087);let h=e=>e.key||"";function p(e){let t=[];return a.Children.forEach(e,e=>{(0,a.isValidElement)(e)&&t.push(e)}),t}var x=r(5403);let v=e=>{let{children:t,custom:r,initial:o=!0,onExitComplete:c,presenceAffectsLayout:l=!0,mode:d="sync",propagate:m=!1,anchorX:v="left"}=e,[b,g]=(0,f.xQ)(m),y=(0,a.useMemo)(()=>p(t),[t]),j=m&&!b?[]:y.map(h),w=(0,a.useRef)(!0),N=(0,a.useRef)(y),P=(0,i.M)(()=>new Map),[O,E]=(0,a.useState)(y),[C,k]=(0,a.useState)(y);(0,x.E)(()=>{w.current=!1,N.current=y;for(let e=0;e<C.length;e++){let t=h(C[e]);j.includes(t)?P.delete(t):!0!==P.get(t)&&P.set(t,!1)}},[C,j.length,j.join("-")]);let L=[];if(y!==O){let e=[...y];for(let t=0;t<C.length;t++){let r=C[t],n=h(r);j.includes(n)||(e.splice(t,0,r),L.push(r))}return"wait"===d&&L.length&&(e=L),k(p(e)),E(y),null}let{forceRender:S}=(0,a.useContext)(s.L);return(0,n.jsx)(n.Fragment,{children:C.map(e=>{let t=h(e),a=(!m||!!b)&&(y===C||j.includes(t));return(0,n.jsx)(u,{isPresent:a,initial:(!w.current||!!o)&&void 0,custom:r,presenceAffectsLayout:l,mode:d,onExitComplete:a?void 0:()=>{if(!P.has(t))return;P.set(t,!0);let e=!0;P.forEach(t=>{t||(e=!1)}),e&&(null==S||S(),k(N.current),m&&(null==g||g()),c&&c())},anchorX:v,children:e},t)})})}},3435:(e,t,r)=>{"use strict";r.d(t,{k5:()=>d});var n=r(2115),a={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},s=n.createContext&&n.createContext(a),i=["attr","size","title"];function o(){return(o=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function c(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function l(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?c(Object(r),!0).forEach(function(t){var n,a;n=t,a=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[n]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):c(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function d(e){return t=>n.createElement(u,o({attr:l({},e.attr)},t),function e(t){return t&&t.map((t,r)=>n.createElement(t.tag,l({key:r},t.attr),e(t.child)))}(e.child))}function u(e){var t=t=>{var r,{attr:a,size:s,title:c}=e,d=function(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(n=0;n<s.length;n++)r=s[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}(e,i),u=s||t.size||"1em";return t.className&&(r=t.className),e.className&&(r=(r?r+" ":"")+e.className),n.createElement("svg",o({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,a,d,{className:r,style:l(l({color:e.color||t.color},t.style),e.style),height:u,width:u,xmlns:"http://www.w3.org/2000/svg"}),c&&n.createElement("title",null,c),e.children)};return void 0!==s?n.createElement(s.Consumer,null,e=>t(e)):t(a)}}},e=>{var t=t=>e(e.s=t);e.O(0,[179,711,478,173,441,517,358],()=>t(6859)),_N_E=e.O()}]);