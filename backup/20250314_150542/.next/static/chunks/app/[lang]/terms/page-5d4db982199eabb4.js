(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[951],{92:(e,i,a)=>{Promise.resolve().then(a.bind(a,7889))},7889:(e,i,a)=>{"use strict";a.d(i,{default:()=>s});var t=a(5155),r=a(4889);function s(e){let{t:i,lang:a}=e;return(0,t.jsxs)("main",{className:"bg-white",children:[(0,t.jsxs)("section",{className:"relative h-[40vh] min-h-[300px] overflow-hidden bg-gradient-to-br from-gray-900 via-purple-900 to-red-900 pt-16",children:[(0,t.jsxs)("div",{className:"absolute inset-0 z-0",children:[(0,t.jsx)("div",{className:"absolute w-full h-full opacity-20",style:{backgroundImage:'url("/images/noise.png")',backgroundRepeat:"repeat"}}),(0,t.jsx)("div",{className:"absolute inset-0",style:{backgroundImage:"radial-gradient(circle at 30% 50%, rgba(255, 255, 255, 0.1) 0%, transparent 25%), radial-gradient(circle at 70% 30%, rgba(255, 255, 255, 0.08) 0%, transparent 25%)"}})]}),(0,t.jsx)("div",{className:"container max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10 h-full flex flex-col justify-center",children:(0,t.jsxs)("div",{className:"text-center max-w-3xl mx-auto",children:[(0,t.jsx)(r.A,{delay:.2,children:(0,t.jsx)("h1",{className:"text-4xl md:text-5xl font-manrope font-bold tracking-tighter leading-tight mb-6 text-white text-shadow-glow",children:"Termini e Condizioni"})}),(0,t.jsx)(r.A,{delay:.4,children:(0,t.jsxs)("div",{className:"relative h-[3px] w-40 mx-auto my-8 overflow-hidden rounded-full",children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-red-500 via-purple-600 to-blue-500"}),(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-500 via-red-500 to-purple-500 animate-gradient-x"})]})}),(0,t.jsx)(r.A,{delay:.6,children:(0,t.jsx)("p",{className:"text-base md:text-xl font-sans leading-relaxed text-white/90 max-w-2xl mx-auto",children:"Condizioni generali di contratto di Inparo GmbH"})})]})})]}),(0,t.jsx)("section",{className:"py-16 px-4 sm:px-6 lg:px-8",children:(0,t.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,t.jsxs)("div",{className:"prose prose-lg mx-auto",children:[(0,t.jsx)("h2",{className:"text-3xl font-bold mb-6 text-gray-900",children:"Termini e Condizioni di Utilizzo"}),(0,t.jsx)("p",{className:"mb-6 text-gray-700",children:"I presenti Termini e Condizioni (\"Termini\") regolano l'utilizzo dei servizi offerti da Inparo GmbH e costituiscono un accordo legalmente vincolante tra l'utente e la nostra azienda."}),(0,t.jsx)("h3",{className:"text-2xl font-semibold mt-8 mb-4 text-gray-900",children:"1. Informazioni generali"}),(0,t.jsxs)("p",{className:"mb-4 text-gray-700",children:[(0,t.jsx)("strong",{children:"Inparo GmbH"}),(0,t.jsx)("br",{}),"Gubelstrasse 15",(0,t.jsx)("br",{}),"6300 Zug",(0,t.jsx)("br",{}),"Svizzera",(0,t.jsx)("br",{}),"Email: <EMAIL>",(0,t.jsx)("br",{}),"Telefono: +41 76 466 2122"]}),(0,t.jsx)("h3",{className:"text-2xl font-semibold mt-8 mb-4 text-gray-900",children:"2. Oggetto del contratto"}),(0,t.jsx)("p",{className:"mb-4 text-gray-700",children:"Inparo GmbH offre servizi di idraulica, riscaldamento e climatizzazione come descritto nel sito web. Questi Termini disciplinano la fornitura di tali servizi e l'utilizzo del nostro sito web."}),(0,t.jsx)("h3",{className:"text-2xl font-semibold mt-8 mb-4 text-gray-900",children:"3. Procedura di ordine e formazione del contratto"}),(0,t.jsx)("p",{className:"mb-4 text-gray-700",children:"Il contratto di servizio viene concluso quando Inparo GmbH conferma l'accettazione di un ordine di servizio del cliente tramite conferma scritta (email, SMS o altri mezzi di comunicazione). Il contratto \xe8 soggetto al diritto svizzero delle obbligazioni."}),(0,t.jsx)("h3",{className:"text-2xl font-semibold mt-8 mb-4 text-gray-900",children:"4. Prezzi e pagamenti"}),(0,t.jsx)("p",{className:"mb-4 text-gray-700",children:"I prezzi sono indicati in franchi svizzeri (CHF) e, salvo diversa indicazione, sono IVA esclusa. Inparo GmbH si riserva il diritto di modificare i prezzi in qualsiasi momento prima della conclusione del contratto. Le modalit\xe0 di pagamento accettate sono specificate durante il processo di ordinazione del servizio."}),(0,t.jsx)("h3",{className:"text-2xl font-semibold mt-8 mb-4 text-gray-900",children:"5. Esecuzione dei servizi"}),(0,t.jsx)("p",{className:"mb-4 text-gray-700",children:"I servizi saranno eseguiti nei tempi e nei modi concordati tra le parti. Eventuali ritardi dovuti a cause di forza maggiore non comporteranno responsabilit\xe0 per Inparo GmbH. Il cliente si impegna a fornire accesso ai locali e alle informazioni necessarie per l'esecuzione dei servizi."}),(0,t.jsx)("h3",{className:"text-2xl font-semibold mt-8 mb-4 text-gray-900",children:"6. Garanzia e responsabilit\xe0"}),(0,t.jsx)("p",{className:"mb-4 text-gray-700",children:"Inparo GmbH garantisce che i servizi saranno eseguiti con la dovuta diligenza professionale. La garanzia sui lavori eseguiti \xe8 di 24 mesi dalla data di completamento, salvo diversa indicazione. La responsabilit\xe0 di Inparo GmbH \xe8 limitata ai danni diretti causati da negligenza grave o dolo ed \xe8 comunque limitata all'importo pagato dal cliente per il servizio in questione."}),(0,t.jsx)("h3",{className:"text-2xl font-semibold mt-8 mb-4 text-gray-900",children:"7. Utilizzo del sito web"}),(0,t.jsx)("p",{className:"mb-4 text-gray-700",children:"L'utente si impegna a utilizzare il sito web di Inparo GmbH in modo legale e conforme ai presenti Termini. \xc8 vietato qualsiasi utilizzo che possa causare danni al sito o comprometterne la funzionalit\xe0 o la sicurezza."}),(0,t.jsx)("h3",{className:"text-2xl font-semibold mt-8 mb-4 text-gray-900",children:"8. Propriet\xe0 intellettuale"}),(0,t.jsx)("p",{className:"mb-4 text-gray-700",children:"Tutti i contenuti del sito web (testi, immagini, loghi, marchi, ecc.) sono di propriet\xe0 di Inparo GmbH o dei suoi partner e sono protetti dalla legislazione svizzera e internazionale sulla propriet\xe0 intellettuale. \xc8 vietata qualsiasi forma di riproduzione o utilizzo non autorizzato."}),(0,t.jsx)("h3",{className:"text-2xl font-semibold mt-8 mb-4 text-gray-900",children:"9. Protezione dei dati"}),(0,t.jsx)("p",{className:"mb-4 text-gray-700",children:"Il trattamento dei dati personali \xe8 regolato dalla nostra Privacy Policy, disponibile sul sito web, che costituisce parte integrante dei presenti Termini."}),(0,t.jsx)("h3",{className:"text-2xl font-semibold mt-8 mb-4 text-gray-900",children:"10. Legge applicabile e foro competente"}),(0,t.jsx)("p",{className:"mb-4 text-gray-700",children:"I presenti Termini sono regolati dal diritto svizzero, con esclusione delle norme di diritto internazionale privato e della Convenzione di Vienna sulla vendita internazionale di merci. Per ogni controversia relativa all'interpretazione, esecuzione o risoluzione dei presenti Termini sar\xe0 competente in via esclusiva il foro di Zugo, Svizzera."}),(0,t.jsx)("h3",{className:"text-2xl font-semibold mt-8 mb-4 text-gray-900",children:"11. Modifiche ai Termini"}),(0,t.jsx)("p",{className:"mb-4 text-gray-700",children:"Inparo GmbH si riserva il diritto di modificare i presenti Termini in qualsiasi momento. Le modifiche saranno pubblicate sul sito web e saranno efficaci dalla data di pubblicazione. L'utilizzo continuato del sito o dei servizi dopo tale data implica l'accettazione delle modifiche."}),(0,t.jsx)("h3",{className:"text-2xl font-semibold mt-8 mb-4 text-gray-900",children:"12. Contatti"}),(0,t.jsx)("p",{className:"mb-4 text-gray-700",children:"Per qualsiasi domanda o richiesta relativa ai presenti Termini, vi preghiamo di contattarci all'indirizzo:"}),(0,t.jsxs)("p",{className:"mb-8 text-gray-700",children:[(0,t.jsx)("strong",{children:"Inparo GmbH"}),(0,t.jsx)("br",{}),"Gubelstrasse 15",(0,t.jsx)("br",{}),"6300 Zug",(0,t.jsx)("br",{}),"Svizzera",(0,t.jsx)("br",{}),"Email: <EMAIL>",(0,t.jsx)("br",{}),"Telefono: +41 76 466 2122"]}),(0,t.jsx)("div",{className:"border-t border-gray-200 pt-8 mt-8",children:(0,t.jsx)("p",{className:"text-gray-500 text-sm",children:"Ultima modifica: 10 Marzo 2025"})})]})})})]})}},4889:(e,i,a)=>{"use strict";a.d(i,{A:()=>s});var t=a(5155),r=a(3478);function s(e){let{children:i,className:a="",delay:s=0}=e;return(0,t.jsx)(r.P.div,{className:a,initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:s},children:i})}}},e=>{var i=i=>e(e.s=i);e.O(0,[478,441,517,358],()=>i(92)),_N_E=e.O()}]);