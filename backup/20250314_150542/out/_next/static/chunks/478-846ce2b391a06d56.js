"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[478],{5087:(t,e,i)=>{i.d(e,{xQ:()=>r});var s=i(2115),n=i(9656);function r(t=!0){let e=(0,s.useContext)(n.t);if(null===e)return[!0,null];let{isPresent:i,onExitComplete:o,register:a}=e,l=(0,s.useId)();(0,s.useEffect)(()=>{if(t)return a(l)},[t]);let u=(0,s.useCallback)(()=>t&&o&&o(l),[l,o,t]);return!i&&o?[!1,u]:[!0]}},4710:(t,e,i)=>{i.d(e,{L:()=>s});let s=(0,i(2115).createContext)({})},7249:(t,e,i)=>{i.d(e,{Q:()=>s});let s=(0,i(2115).createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"})},9656:(t,e,i)=>{i.d(e,{t:()=>s});let s=(0,i(2115).createContext)(null)},3478:(t,e,i)=>{let s;function n(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function r(t,e,i,s){if("function"==typeof e||("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e)){let[n,r]=function(t){let e=[{},{}];return null==t||t.values.forEach((t,i)=>{e[0][i]=t.get(),e[1][i]=t.getVelocity()}),e}(s);e=e(void 0!==i?i:t.custom,n,r)}return e}function o(t,e,i){let s=t.getProps();return r(s,e,void 0!==i?i:s.custom,t)}function a(t){let e;return()=>(void 0===e&&(e=t()),e)}i.d(e,{P:()=>rv});let l=a(()=>void 0!==window.ScrollTimeline);class u{constructor(t){this.stop=()=>this.runAll("stop"),this.animations=t.filter(Boolean)}get finished(){return Promise.all(this.animations.map(t=>"finished"in t?t.finished:t))}getAll(t){return this.animations[0][t]}setAll(t,e){for(let i=0;i<this.animations.length;i++)this.animations[i][t]=e}attachTimeline(t,e){let i=this.animations.map(i=>l()&&i.attachTimeline?i.attachTimeline(t):"function"==typeof e?e(i):void 0);return()=>{i.forEach((t,e)=>{t&&t(),this.animations[e].stop()})}}get time(){return this.getAll("time")}set time(t){this.setAll("time",t)}get speed(){return this.getAll("speed")}set speed(t){this.setAll("speed",t)}get startTime(){return this.getAll("startTime")}get duration(){let t=0;for(let e=0;e<this.animations.length;e++)t=Math.max(t,this.animations[e].duration);return t}runAll(t){this.animations.forEach(e=>e[t]())}flatten(){this.runAll("flatten")}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class h extends u{then(t,e){return Promise.all(this.animations).then(t).catch(e)}}function d(t,e){return t?t[e]||t.default||t:void 0}function c(t){let e=0,i=t.next(e);for(;!i.done&&e<2e4;)e+=50,i=t.next(e);return e>=2e4?1/0:e}function p(t){return"function"==typeof t}function m(t,e){t.timeline=e,t.onfinish=null}let f=t=>Array.isArray(t)&&"number"==typeof t[0],v={linearEasing:void 0},g=function(t,e){let i=a(t);return()=>{var t;return null!==(t=v[e])&&void 0!==t?t:i()}}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),y=(t,e,i)=>{let s=e-t;return 0===s?1:(i-t)/s},x=(t,e,i=10)=>{let s="",n=Math.max(Math.round(e/i),2);for(let e=0;e<n;e++)s+=t(y(0,n-1,e))+", ";return`linear(${s.substring(0,s.length-2)})`},P=([t,e,i,s])=>`cubic-bezier(${t}, ${e}, ${i}, ${s})`,T={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:P([0,.65,.55,1]),circOut:P([.55,0,1,.45]),backIn:P([.31,.01,.66,-.59]),backOut:P([.33,1.53,.69,.99])},w={x:!1,y:!1};function b(t,e){let i=function(t,e,i){if(t instanceof EventTarget)return[t];if("string"==typeof t){let e=document.querySelectorAll(t);return e?Array.from(e):[]}return Array.from(t)}(t),s=new AbortController;return[i,{passive:!0,...e,signal:s.signal},()=>s.abort()]}function S(t){return!("touch"===t.pointerType||w.x||w.y)}function A(t,e){let i=`${e}PointerCapture`;if(t.target instanceof Element&&i in t.target&&void 0!==t.pointerId)try{t.target[i](t.pointerId)}catch(t){}}let E=(t,e)=>!!e&&(t===e||E(t,e.parentElement)),M=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary,V=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),C=new WeakSet;function D(t){return e=>{"Enter"===e.key&&t(e)}}function k(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}let R=(t,e)=>{let i=t.currentTarget;if(!i)return;let s=D(()=>{if(C.has(i))return;k(i,"down");let t=D(()=>{k(i,"up")});i.addEventListener("keyup",t,e),i.addEventListener("blur",()=>k(i,"cancel"),e)});i.addEventListener("keydown",s,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",s),e)};function L(t){return M(t)&&!(w.x||w.y)}let j=t=>1e3*t,F=t=>t/1e3,B=t=>t,O=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],I=new Set(O),U=new Set(["width","height","top","left","right","bottom",...O]),$=t=>Array.isArray(t),N=t=>!!(t&&"object"==typeof t&&t.mix&&t.toValue),W=t=>$(t)?t[t.length-1]||0:t,z={skipAnimations:!1,useManualTiming:!1},H=["read","resolveKeyframes","update","preRender","render","postRender"],Y={value:null,addProjectionMetrics:null};function X(t,e){let i=!1,s=!0,n={delta:0,timestamp:0,isProcessing:!1},r=()=>i=!0,o=H.reduce((t,i)=>(t[i]=function(t,e){let i=new Set,s=new Set,n=!1,r=!1,o=new WeakSet,a={delta:0,timestamp:0,isProcessing:!1},l=0;function u(e){o.has(e)&&(h.schedule(e),t()),l++,e(a)}let h={schedule:(t,e=!1,r=!1)=>{let a=r&&n?i:s;return e&&o.add(t),a.has(t)||a.add(t),t},cancel:t=>{s.delete(t),o.delete(t)},process:t=>{if(a=t,n){r=!0;return}n=!0,[i,s]=[s,i],i.forEach(u),e&&Y.value&&Y.value.frameloop[e].push(l),l=0,i.clear(),n=!1,r&&(r=!1,h.process(t))}};return h}(r,e?i:void 0),t),{}),{read:a,resolveKeyframes:l,update:u,preRender:h,render:d,postRender:c}=o,p=()=>{let r=z.useManualTiming?n.timestamp:performance.now();i=!1,z.useManualTiming||(n.delta=s?1e3/60:Math.max(Math.min(r-n.timestamp,40),1)),n.timestamp=r,n.isProcessing=!0,a.process(n),l.process(n),u.process(n),h.process(n),d.process(n),c.process(n),n.isProcessing=!1,i&&e&&(s=!1,t(p))},m=()=>{i=!0,s=!0,n.isProcessing||t(p)};return{schedule:H.reduce((t,e)=>{let s=o[e];return t[e]=(t,e=!1,n=!1)=>(i||m(),s.schedule(t,e,n)),t},{}),cancel:t=>{for(let e=0;e<H.length;e++)o[H[e]].cancel(t)},state:n,steps:o}}let{schedule:K,cancel:q,state:G,steps:_}=X("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:B,!0);function Z(){s=void 0}let Q={now:()=>(void 0===s&&Q.set(G.isProcessing||z.useManualTiming?G.timestamp:performance.now()),s),set:t=>{s=t,queueMicrotask(Z)}};function J(t,e){-1===t.indexOf(e)&&t.push(e)}function tt(t,e){let i=t.indexOf(e);i>-1&&t.splice(i,1)}class te{constructor(){this.subscriptions=[]}add(t){return J(this.subscriptions,t),()=>tt(this.subscriptions,t)}notify(t,e,i){let s=this.subscriptions.length;if(s){if(1===s)this.subscriptions[0](t,e,i);else for(let n=0;n<s;n++){let s=this.subscriptions[n];s&&s(t,e,i)}}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}let ti=t=>!isNaN(parseFloat(t)),ts={current:void 0};class tn{constructor(t,e={}){this.version="12.4.7",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{let i=Q.now();this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),e&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=Q.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=ti(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new te);let i=this.events[t].add(e);return"change"===t?()=>{i(),K.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,i){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-i}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return ts.current&&ts.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var t;let e=Q.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||e-this.updatedAt>30)return 0;let i=Math.min(this.updatedAt-this.prevUpdatedAt,30);return t=parseFloat(this.current)-parseFloat(this.prevFrameValue),i?1e3/i*t:0}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function tr(t,e){return new tn(t,e)}let to=t=>!!(t&&t.getVelocity);function ta(t,e){let i=t.getValue("willChange");if(to(i)&&i.add)return i.add(e)}let tl=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),tu="data-"+tl("framerAppearId"),th={current:!1},td=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function tc(t,e,i,s){if(t===e&&i===s)return B;let n=e=>(function(t,e,i,s,n){let r,o;let a=0;do(r=td(o=e+(i-e)/2,s,n)-t)>0?i=o:e=o;while(Math.abs(r)>1e-7&&++a<12);return o})(e,0,1,t,i);return t=>0===t||1===t?t:td(n(t),e,s)}let tp=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,tm=t=>e=>1-t(1-e),tf=tc(.33,1.53,.69,.99),tv=tm(tf),tg=tp(tv),ty=t=>(t*=2)<1?.5*tv(t):.5*(2-Math.pow(2,-10*(t-1))),tx=t=>1-Math.sin(Math.acos(t)),tP=tm(tx),tT=tp(tx),tw=t=>/^0[^.\s]+$/u.test(t),tb=(t,e,i)=>i>e?e:i<t?t:i,tS={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},tA={...tS,transform:t=>tb(0,1,t)},tE={...tS,default:1},tM=t=>Math.round(1e5*t)/1e5,tV=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,tC=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,tD=(t,e)=>i=>!!("string"==typeof i&&tC.test(i)&&i.startsWith(t)||e&&null!=i&&Object.prototype.hasOwnProperty.call(i,e)),tk=(t,e,i)=>s=>{if("string"!=typeof s)return s;let[n,r,o,a]=s.match(tV);return{[t]:parseFloat(n),[e]:parseFloat(r),[i]:parseFloat(o),alpha:void 0!==a?parseFloat(a):1}},tR=t=>tb(0,255,t),tL={...tS,transform:t=>Math.round(tR(t))},tj={test:tD("rgb","red"),parse:tk("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:s=1})=>"rgba("+tL.transform(t)+", "+tL.transform(e)+", "+tL.transform(i)+", "+tM(tA.transform(s))+")"},tF={test:tD("#"),parse:function(t){let e="",i="",s="",n="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),s=t.substring(5,7),n=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),s=t.substring(3,4),n=t.substring(4,5),e+=e,i+=i,s+=s,n+=n),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(s,16),alpha:n?parseInt(n,16)/255:1}},transform:tj.transform},tB=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),tO=tB("deg"),tI=tB("%"),tU=tB("px"),t$=tB("vh"),tN=tB("vw"),tW={...tI,parse:t=>tI.parse(t)/100,transform:t=>tI.transform(100*t)},tz={test:tD("hsl","hue"),parse:tk("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:s=1})=>"hsla("+Math.round(t)+", "+tI.transform(tM(e))+", "+tI.transform(tM(i))+", "+tM(tA.transform(s))+")"},tH={test:t=>tj.test(t)||tF.test(t)||tz.test(t),parse:t=>tj.test(t)?tj.parse(t):tz.test(t)?tz.parse(t):tF.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?tj.transform(t):tz.transform(t)},tY=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,tX="number",tK="color",tq=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function tG(t){let e=t.toString(),i=[],s={color:[],number:[],var:[]},n=[],r=0,o=e.replace(tq,t=>(tH.test(t)?(s.color.push(r),n.push(tK),i.push(tH.parse(t))):t.startsWith("var(")?(s.var.push(r),n.push("var"),i.push(t)):(s.number.push(r),n.push(tX),i.push(parseFloat(t))),++r,"${}")).split("${}");return{values:i,split:o,indexes:s,types:n}}function t_(t){return tG(t).values}function tZ(t){let{split:e,types:i}=tG(t),s=e.length;return t=>{let n="";for(let r=0;r<s;r++)if(n+=e[r],void 0!==t[r]){let e=i[r];e===tX?n+=tM(t[r]):e===tK?n+=tH.transform(t[r]):n+=t[r]}return n}}let tQ=t=>"number"==typeof t?0:t,tJ={test:function(t){var e,i;return isNaN(t)&&"string"==typeof t&&((null===(e=t.match(tV))||void 0===e?void 0:e.length)||0)+((null===(i=t.match(tY))||void 0===i?void 0:i.length)||0)>0},parse:t_,createTransformer:tZ,getAnimatableNone:function(t){let e=t_(t);return tZ(t)(e.map(tQ))}},t0=new Set(["brightness","contrast","saturate","opacity"]);function t1(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[s]=i.match(tV)||[];if(!s)return t;let n=i.replace(s,""),r=t0.has(e)?1:0;return s!==i&&(r*=100),e+"("+r+n+")"}let t5=/\b([a-z-]*)\(.*?\)/gu,t2={...tJ,getAnimatableNone:t=>{let e=t.match(t5);return e?e.map(t1).join(" "):t}},t3={...tS,transform:Math.round},t9={borderWidth:tU,borderTopWidth:tU,borderRightWidth:tU,borderBottomWidth:tU,borderLeftWidth:tU,borderRadius:tU,radius:tU,borderTopLeftRadius:tU,borderTopRightRadius:tU,borderBottomRightRadius:tU,borderBottomLeftRadius:tU,width:tU,maxWidth:tU,height:tU,maxHeight:tU,top:tU,right:tU,bottom:tU,left:tU,padding:tU,paddingTop:tU,paddingRight:tU,paddingBottom:tU,paddingLeft:tU,margin:tU,marginTop:tU,marginRight:tU,marginBottom:tU,marginLeft:tU,backgroundPositionX:tU,backgroundPositionY:tU,rotate:tO,rotateX:tO,rotateY:tO,rotateZ:tO,scale:tE,scaleX:tE,scaleY:tE,scaleZ:tE,skew:tO,skewX:tO,skewY:tO,distance:tU,translateX:tU,translateY:tU,translateZ:tU,x:tU,y:tU,z:tU,perspective:tU,transformPerspective:tU,opacity:tA,originX:tW,originY:tW,originZ:tU,zIndex:t3,size:tU,fillOpacity:tA,strokeOpacity:tA,numOctaves:t3},t4={...t9,color:tH,backgroundColor:tH,outlineColor:tH,fill:tH,stroke:tH,borderColor:tH,borderTopColor:tH,borderRightColor:tH,borderBottomColor:tH,borderLeftColor:tH,filter:t2,WebkitFilter:t2},t6=t=>t4[t];function t7(t,e){let i=t6(t);return i!==t2&&(i=tJ),i.getAnimatableNone?i.getAnimatableNone(e):void 0}let t8=new Set(["auto","none","0"]),et=t=>t===tS||t===tU,ee=(t,e)=>parseFloat(t.split(", ")[e]),ei=(t,e)=>(i,{transform:s})=>{if("none"===s||!s)return 0;let n=s.match(/^matrix3d\((.+)\)$/u);if(n)return ee(n[1],e);{let e=s.match(/^matrix\((.+)\)$/u);return e?ee(e[1],t):0}},es=new Set(["x","y","z"]),en=O.filter(t=>!es.has(t)),er={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:ei(4,13),y:ei(5,14)};er.translateX=er.x,er.translateY=er.y;let eo=new Set,ea=!1,el=!1;function eu(){if(el){let t=Array.from(eo).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{let e=function(t){let e=[];return en.forEach(i=>{let s=t.getValue(i);void 0!==s&&(e.push([i,s.get()]),s.set(i.startsWith("scale")?1:0))}),e}(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=i.get(t);e&&e.forEach(([e,i])=>{var s;null===(s=t.getValue(e))||void 0===s||s.set(i)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}el=!1,ea=!1,eo.forEach(t=>t.complete()),eo.clear()}function eh(){eo.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(el=!0)})}class ed{constructor(t,e,i,s,n,r=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=s,this.element=n,this.isAsync=r}scheduleResolve(){this.isScheduled=!0,this.isAsync?(eo.add(this),ea||(ea=!0,K.read(eh),K.resolveKeyframes(eu))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:i,motionValue:s}=this;for(let n=0;n<t.length;n++)if(null===t[n]){if(0===n){let n=null==s?void 0:s.get(),r=t[t.length-1];if(void 0!==n)t[0]=n;else if(i&&e){let s=i.readValue(e,r);null!=s&&(t[0]=s)}void 0===t[0]&&(t[0]=r),s&&void 0===n&&s.set(t[0])}else t[n]=t[n-1]}}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),eo.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,eo.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}let ec=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),ep=t=>e=>"string"==typeof e&&e.startsWith(t),em=ep("--"),ef=ep("var(--"),ev=t=>!!ef(t)&&eg.test(t.split("/*")[0].trim()),eg=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,ey=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,ex=t=>e=>e.test(t),eP=[tS,tU,tI,tO,tN,t$,{test:t=>"auto"===t,parse:t=>t}],eT=t=>eP.find(ex(t));class ew extends ed{constructor(t,e,i,s,n){super(t,e,i,s,n,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let s=t[i];if("string"==typeof s&&ev(s=s.trim())){let n=function t(e,i,s=1){B(s<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[n,r]=function(t){let e=ey.exec(t);if(!e)return[,];let[,i,s,n]=e;return[`--${null!=i?i:s}`,n]}(e);if(!n)return;let o=window.getComputedStyle(i).getPropertyValue(n);if(o){let t=o.trim();return ec(t)?parseFloat(t):t}return ev(r)?t(r,i,s+1):r}(s,e.current);void 0!==n&&(t[i]=n),i===t.length-1&&(this.finalKeyframe=s)}}if(this.resolveNoneKeyframes(),!U.has(i)||2!==t.length)return;let[s,n]=t,r=eT(s),o=eT(n);if(r!==o){if(et(r)&&et(o))for(let e=0;e<t.length;e++){let i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else this.needsMeasurement=!0}}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++){var s;("number"==typeof(s=t[e])?0===s:null===s||"none"===s||"0"===s||tw(s))&&i.push(e)}i.length&&function(t,e,i){let s,n=0;for(;n<t.length&&!s;){let e=t[n];"string"==typeof e&&!t8.has(e)&&tG(e).values.length&&(s=t[n]),n++}if(s&&i)for(let n of e)t[n]=t7(i,s)}(t,i,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=er[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let s=e[e.length-1];void 0!==s&&t.getValue(i,s).jump(s,!1)}measureEndState(){var t;let{element:e,name:i,unresolvedKeyframes:s}=this;if(!e||!e.current)return;let n=e.getValue(i);n&&n.jump(this.measuredOrigin,!1);let r=s.length-1,o=s[r];s[r]=er[i](e.measureViewportBox(),window.getComputedStyle(e.current)),null!==o&&void 0===this.finalKeyframe&&(this.finalKeyframe=o),(null===(t=this.removedTransforms)||void 0===t?void 0:t.length)&&this.removedTransforms.forEach(([t,i])=>{e.getValue(t).set(i)}),this.resolveNoneKeyframes()}}let eb=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(tJ.test(t)||"0"===t)&&!t.startsWith("url(")),eS=t=>null!==t;function eA(t,{repeat:e,repeatType:i="loop"},s){let n=t.filter(eS),r=e&&"loop"!==i&&e%2==1?0:n.length-1;return r&&void 0!==s?s:n[r]}class eE{constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:s=0,repeatDelay:n=0,repeatType:r="loop",...o}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.createdAt=Q.now(),this.options={autoplay:t,delay:e,type:i,repeat:s,repeatDelay:n,repeatType:r,...o},this.updateFinishedPromise()}calcStartTime(){return this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt}get resolved(){return this._resolved||this.hasAttemptedResolve||(eh(),eu()),this._resolved}onKeyframesResolved(t,e){this.resolvedAt=Q.now(),this.hasAttemptedResolve=!0;let{name:i,type:s,velocity:n,delay:r,onComplete:o,onUpdate:a,isGenerator:l}=this.options;if(!l&&!function(t,e,i,s){let n=t[0];if(null===n)return!1;if("display"===e||"visibility"===e)return!0;let r=t[t.length-1],o=eb(n,e),a=eb(r,e);return B(o===a,`You are trying to animate ${e} from "${n}" to "${r}". ${n} is not an animatable value - to enable this animation set ${n} to a value animatable to ${r} via the \`style\` property.`),!!o&&!!a&&(function(t){let e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||p(i))&&s)}(t,i,s,n)){if(th.current||!r){a&&a(eA(t,this.options,e)),o&&o(),this.resolveFinishedPromise();return}this.options.duration=0}let u=this.initPlayback(t,e);!1!==u&&(this._resolved={keyframes:t,finalKeyframe:e,...u},this.onPostResolved())}onPostResolved(){}then(t,e){return this.currentFinishedPromise.then(t,e)}flatten(){this.options.type="keyframes",this.options.ease="linear"}updateFinishedPromise(){this.currentFinishedPromise=new Promise(t=>{this.resolveFinishedPromise=t})}}let eM={layout:0,mainThread:0,waapi:0},eV=(t,e,i)=>t+(e-t)*i;function eC(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}function eD(t,e){return i=>i>0?e:t}let ek=(t,e,i)=>{let s=t*t,n=i*(e*e-s)+s;return n<0?0:Math.sqrt(n)},eR=[tF,tj,tz],eL=t=>eR.find(e=>e.test(t));function ej(t){let e=eL(t);if(B(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`),!e)return!1;let i=e.parse(t);return e===tz&&(i=function({hue:t,saturation:e,lightness:i,alpha:s}){t/=360,i/=100;let n=0,r=0,o=0;if(e/=100){let s=i<.5?i*(1+e):i+e-i*e,a=2*i-s;n=eC(a,s,t+1/3),r=eC(a,s,t),o=eC(a,s,t-1/3)}else n=r=o=i;return{red:Math.round(255*n),green:Math.round(255*r),blue:Math.round(255*o),alpha:s}}(i)),i}let eF=(t,e)=>{let i=ej(t),s=ej(e);if(!i||!s)return eD(t,e);let n={...i};return t=>(n.red=ek(i.red,s.red,t),n.green=ek(i.green,s.green,t),n.blue=ek(i.blue,s.blue,t),n.alpha=eV(i.alpha,s.alpha,t),tj.transform(n))},eB=(t,e)=>i=>e(t(i)),eO=(...t)=>t.reduce(eB),eI=new Set(["none","hidden"]);function eU(t,e){return i=>eV(t,e,i)}function e$(t){return"number"==typeof t?eU:"string"==typeof t?ev(t)?eD:tH.test(t)?eF:ez:Array.isArray(t)?eN:"object"==typeof t?tH.test(t)?eF:eW:eD}function eN(t,e){let i=[...t],s=i.length,n=t.map((t,i)=>e$(t)(t,e[i]));return t=>{for(let e=0;e<s;e++)i[e]=n[e](t);return i}}function eW(t,e){let i={...t,...e},s={};for(let n in i)void 0!==t[n]&&void 0!==e[n]&&(s[n]=e$(t[n])(t[n],e[n]));return t=>{for(let e in s)i[e]=s[e](t);return i}}let ez=(t,e)=>{let i=tJ.createTransformer(e),s=tG(t),n=tG(e);return s.indexes.var.length===n.indexes.var.length&&s.indexes.color.length===n.indexes.color.length&&s.indexes.number.length>=n.indexes.number.length?eI.has(t)&&!n.values.length||eI.has(e)&&!s.values.length?function(t,e){return eI.has(t)?i=>i<=0?t:e:i=>i>=1?e:t}(t,e):eO(eN(function(t,e){var i;let s=[],n={color:0,var:0,number:0};for(let r=0;r<e.values.length;r++){let o=e.types[r],a=t.indexes[o][n[o]],l=null!==(i=t.values[a])&&void 0!==i?i:0;s[r]=l,n[o]++}return s}(s,n),n.values),i):(B(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),eD(t,e))};function eH(t,e,i){return"number"==typeof t&&"number"==typeof e&&"number"==typeof i?eV(t,e,i):e$(t)(t,e)}function eY(t,e,i){var s,n;let r=Math.max(e-5,0);return s=i-t(r),(n=e-r)?1e3/n*s:0}let eX={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function eK(t,e){return t*Math.sqrt(1-e*e)}let eq=["duration","bounce"],eG=["stiffness","damping","mass"];function e_(t,e){return e.some(e=>void 0!==t[e])}function eZ(t=eX.visualDuration,e=eX.bounce){let i;let s="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:n,restDelta:r}=s,o=s.keyframes[0],a=s.keyframes[s.keyframes.length-1],l={done:!1,value:o},{stiffness:u,damping:h,mass:d,duration:p,velocity:m,isResolvedFromDuration:f}=function(t){let e={velocity:eX.velocity,stiffness:eX.stiffness,damping:eX.damping,mass:eX.mass,isResolvedFromDuration:!1,...t};if(!e_(t,eG)&&e_(t,eq)){if(t.visualDuration){let i=2*Math.PI/(1.2*t.visualDuration),s=i*i,n=2*tb(.05,1,1-(t.bounce||0))*Math.sqrt(s);e={...e,mass:eX.mass,stiffness:s,damping:n}}else{let i=function({duration:t=eX.duration,bounce:e=eX.bounce,velocity:i=eX.velocity,mass:s=eX.mass}){let n,r;B(t<=j(eX.maxDuration),"Spring duration must be 10 seconds or less");let o=1-e;o=tb(eX.minDamping,eX.maxDamping,o),t=tb(eX.minDuration,eX.maxDuration,F(t)),o<1?(n=e=>{let s=e*o,n=s*t;return .001-(s-i)/eK(e,o)*Math.exp(-n)},r=e=>{let s=e*o*t,r=Math.pow(o,2)*Math.pow(e,2)*t,a=Math.exp(-s),l=eK(Math.pow(e,2),o);return(s*i+i-r)*a*(-n(e)+.001>0?-1:1)/l}):(n=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),r=e=>t*t*(i-e)*Math.exp(-e*t));let a=function(t,e,i){let s=i;for(let i=1;i<12;i++)s-=t(s)/e(s);return s}(n,r,5/t);if(t=j(t),isNaN(a))return{stiffness:eX.stiffness,damping:eX.damping,duration:t};{let e=Math.pow(a,2)*s;return{stiffness:e,damping:2*o*Math.sqrt(s*e),duration:t}}}(t);(e={...e,...i,mass:eX.mass}).isResolvedFromDuration=!0}}return e}({...s,velocity:-F(s.velocity||0)}),v=m||0,g=h/(2*Math.sqrt(u*d)),y=a-o,P=F(Math.sqrt(u/d)),T=5>Math.abs(y);if(n||(n=T?eX.restSpeed.granular:eX.restSpeed.default),r||(r=T?eX.restDelta.granular:eX.restDelta.default),g<1){let t=eK(P,g);i=e=>a-Math.exp(-g*P*e)*((v+g*P*y)/t*Math.sin(t*e)+y*Math.cos(t*e))}else if(1===g)i=t=>a-Math.exp(-P*t)*(y+(v+P*y)*t);else{let t=P*Math.sqrt(g*g-1);i=e=>{let i=Math.exp(-g*P*e),s=Math.min(t*e,300);return a-i*((v+g*P*y)*Math.sinh(s)+t*y*Math.cosh(s))/t}}let w={calculatedDuration:f&&p||null,next:t=>{let e=i(t);if(f)l.done=t>=p;else{let s=0;g<1&&(s=0===t?j(v):eY(i,t,e));let o=Math.abs(s)<=n,u=Math.abs(a-e)<=r;l.done=o&&u}return l.value=l.done?a:e,l},toString:()=>{let t=Math.min(c(w),2e4),e=x(e=>w.next(t*e).value,t,30);return t+"ms "+e}};return w}function eQ({keyframes:t,velocity:e=0,power:i=.8,timeConstant:s=325,bounceDamping:n=10,bounceStiffness:r=500,modifyTarget:o,min:a,max:l,restDelta:u=.5,restSpeed:h}){let d,c;let p=t[0],m={done:!1,value:p},f=t=>void 0!==a&&t<a||void 0!==l&&t>l,v=t=>void 0===a?l:void 0===l?a:Math.abs(a-t)<Math.abs(l-t)?a:l,g=i*e,y=p+g,x=void 0===o?y:o(y);x!==y&&(g=x-p);let P=t=>-g*Math.exp(-t/s),T=t=>x+P(t),w=t=>{let e=P(t),i=T(t);m.done=Math.abs(e)<=u,m.value=m.done?x:i},b=t=>{f(m.value)&&(d=t,c=eZ({keyframes:[m.value,v(m.value)],velocity:eY(T,t,m.value),damping:n,stiffness:r,restDelta:u,restSpeed:h}))};return b(0),{calculatedDuration:null,next:t=>{let e=!1;return(c||void 0!==d||(e=!0,w(t),b(t)),void 0!==d&&t>=d)?c.next(t-d):(e||w(t),m)}}}let eJ=tc(.42,0,1,1),e0=tc(0,0,.58,1),e1=tc(.42,0,.58,1),e5=t=>Array.isArray(t)&&"number"!=typeof t[0],e2={linear:B,easeIn:eJ,easeInOut:e1,easeOut:e0,circIn:tx,circInOut:tT,circOut:tP,backIn:tv,backInOut:tg,backOut:tf,anticipate:ty},e3=t=>{if(f(t)){B(4===t.length,"Cubic bezier arrays must contain four numerical values.");let[e,i,s,n]=t;return tc(e,i,s,n)}return"string"==typeof t?(B(void 0!==e2[t],`Invalid easing type '${t}'`),e2[t]):t};function e9({duration:t=300,keyframes:e,times:i,ease:s="easeInOut"}){let n=e5(s)?s.map(e3):e3(s),r={done:!1,value:e[0]},o=function(t,e,{clamp:i=!0,ease:s,mixer:n}={}){let r=t.length;if(B(r===e.length,"Both input and output ranges must be the same length"),1===r)return()=>e[0];if(2===r&&e[0]===e[1])return()=>e[1];let o=t[0]===t[1];t[0]>t[r-1]&&(t=[...t].reverse(),e=[...e].reverse());let a=function(t,e,i){let s=[],n=i||eH,r=t.length-1;for(let i=0;i<r;i++){let r=n(t[i],t[i+1]);e&&(r=eO(Array.isArray(e)?e[i]||B:e,r)),s.push(r)}return s}(e,s,n),l=a.length,u=i=>{if(o&&i<t[0])return e[0];let s=0;if(l>1)for(;s<t.length-2&&!(i<t[s+1]);s++);let n=y(t[s],t[s+1],i);return a[s](n)};return i?e=>u(tb(t[0],t[r-1],e)):u}((i&&i.length===e.length?i:function(t){let e=[0];return function(t,e){let i=t[t.length-1];for(let s=1;s<=e;s++){let n=y(0,e,s);t.push(eV(i,1,n))}}(e,t.length-1),e}(e)).map(e=>e*t),e,{ease:Array.isArray(n)?n:e.map(()=>n||e1).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(r.value=o(e),r.done=e>=t,r)}}let e4=t=>{let e=({timestamp:e})=>t(e);return{start:()=>K.update(e,!0),stop:()=>q(e),now:()=>G.isProcessing?G.timestamp:Q.now()}},e6={decay:eQ,inertia:eQ,tween:e9,keyframes:e9,spring:eZ},e7=t=>t/100;class e8 extends eE{constructor(t){super(t),this.holdTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.startTime=null,this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.teardown();let{onStop:t}=this.options;t&&t()};let{name:e,motionValue:i,element:s,keyframes:n}=this.options,r=(null==s?void 0:s.KeyframeResolver)||ed;this.resolver=new r(n,(t,e)=>this.onKeyframesResolved(t,e),e,i,s),this.resolver.scheduleResolve()}flatten(){super.flatten(),this._resolved&&Object.assign(this._resolved,this.initPlayback(this._resolved.keyframes))}initPlayback(t){let e,i;let{type:s="keyframes",repeat:n=0,repeatDelay:r=0,repeatType:o,velocity:a=0}=this.options,l=p(s)?s:e6[s]||e9;l!==e9&&"number"!=typeof t[0]&&(e=eO(e7,eH(t[0],t[1])),t=[0,100]);let u=l({...this.options,keyframes:t});"mirror"===o&&(i=l({...this.options,keyframes:[...t].reverse(),velocity:-a})),null===u.calculatedDuration&&(u.calculatedDuration=c(u));let{calculatedDuration:h}=u,d=h+r;return{generator:u,mirroredGenerator:i,mapPercentToKeyframes:e,calculatedDuration:h,resolvedDuration:d,totalDuration:d*(n+1)-r}}onPostResolved(){let{autoplay:t=!0}=this.options;eM.mainThread++,this.play(),"paused"!==this.pendingPlayState&&t?this.state=this.pendingPlayState:this.pause()}tick(t,e=!1){let{resolved:i}=this;if(!i){let{keyframes:t}=this.options;return{done:!0,value:t[t.length-1]}}let{finalKeyframe:s,generator:n,mirroredGenerator:r,mapPercentToKeyframes:o,keyframes:a,calculatedDuration:l,totalDuration:u,resolvedDuration:h}=i;if(null===this.startTime)return n.next(0);let{delay:d,repeat:c,repeatType:p,repeatDelay:m,onUpdate:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-u/this.speed,this.startTime)),e?this.currentTime=t:null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=Math.round(t-this.startTime)*this.speed;let v=this.currentTime-d*(this.speed>=0?1:-1),g=this.speed>=0?v<0:v>u;this.currentTime=Math.max(v,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=u);let y=this.currentTime,x=n;if(c){let t=Math.min(this.currentTime,u)/h,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,c+1))%2&&("reverse"===p?(i=1-i,m&&(i-=m/h)):"mirror"===p&&(x=r)),y=tb(0,1,i)*h}let P=g?{done:!1,value:a[0]}:x.next(y);o&&(P.value=o(P.value));let{done:T}=P;g||null===l||(T=this.speed>=0?this.currentTime>=u:this.currentTime<=0);let w=null===this.holdTime&&("finished"===this.state||"running"===this.state&&T);return w&&void 0!==s&&(P.value=eA(a,this.options,s)),f&&f(P.value),w&&this.finish(),P}get duration(){let{resolved:t}=this;return t?F(t.calculatedDuration):0}get time(){return F(this.currentTime)}set time(t){t=j(t),this.currentTime=t,null!==this.holdTime||0===this.speed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.speed)}get speed(){return this.playbackSpeed}set speed(t){let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=F(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved){this.pendingPlayState="running";return}if(this.isStopped)return;let{driver:t=e4,onPlay:e,startTime:i}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),e&&e();let s=this.driver.now();null!==this.holdTime?this.startTime=s-this.holdTime:this.startTime?"finished"===this.state&&(this.startTime=s):this.startTime=null!=i?i:this.calcStartTime(),"finished"===this.state&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){var t;if(!this._resolved){this.pendingPlayState="paused";return}this.state="paused",this.holdTime=null!==(t=this.currentTime)&&void 0!==t?t:0}complete(){"running"!==this.state&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";let{onComplete:t}=this.options;t&&t()}cancel(){null!==this.cancelTime&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel(),eM.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}}let it=new Set(["opacity","clipPath","filter","transform"]),ie=a(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),ii={anticipate:ty,backInOut:tg,circInOut:tT};class is extends eE{constructor(t){super(t);let{name:e,motionValue:i,element:s,keyframes:n}=this.options;this.resolver=new ew(n,(t,e)=>this.onKeyframesResolved(t,e),e,i,s),this.resolver.scheduleResolve()}initPlayback(t,e){var i;let{duration:s=300,times:n,ease:r,type:o,motionValue:a,name:l,startTime:u}=this.options;if(!a.owner||!a.owner.current)return!1;if("string"==typeof r&&g()&&r in ii&&(r=ii[r]),p((i=this.options).type)||"spring"===i.type||!function t(e){return!!("function"==typeof e&&g()||!e||"string"==typeof e&&(e in T||g())||f(e)||Array.isArray(e)&&e.every(t))}(i.ease)){let{onComplete:e,onUpdate:i,motionValue:a,element:l,...u}=this.options,h=function(t,e){let i=new e8({...e,keyframes:t,repeat:0,delay:0,isGenerator:!0}),s={done:!1,value:t[0]},n=[],r=0;for(;!s.done&&r<2e4;)n.push((s=i.sample(r)).value),r+=10;return{times:void 0,keyframes:n,duration:r-10,ease:"linear"}}(t,u);1===(t=h.keyframes).length&&(t[1]=t[0]),s=h.duration,n=h.times,r=h.ease,o="keyframes"}let h=function(t,e,i,{delay:s=0,duration:n=300,repeat:r=0,repeatType:o="loop",ease:a="easeInOut",times:l}={}){let u={[e]:i};l&&(u.offset=l);let h=function t(e,i){if(e)return"function"==typeof e&&g()?x(e,i):f(e)?P(e):Array.isArray(e)?e.map(e=>t(e,i)||T.easeOut):T[e]}(a,n);Array.isArray(h)&&(u.easing=h),Y.value&&eM.waapi++;let d=t.animate(u,{delay:s,duration:n,easing:Array.isArray(h)?"linear":h,fill:"both",iterations:r+1,direction:"reverse"===o?"alternate":"normal"});return Y.value&&d.finished.finally(()=>{eM.waapi--}),d}(a.owner.current,l,t,{...this.options,duration:s,times:n,ease:r});return h.startTime=null!=u?u:this.calcStartTime(),this.pendingTimeline?(m(h,this.pendingTimeline),this.pendingTimeline=void 0):h.onfinish=()=>{let{onComplete:i}=this.options;a.set(eA(t,this.options,e)),i&&i(),this.cancel(),this.resolveFinishedPromise()},{animation:h,duration:s,times:n,type:o,ease:r,keyframes:t}}get duration(){let{resolved:t}=this;if(!t)return 0;let{duration:e}=t;return F(e)}get time(){let{resolved:t}=this;if(!t)return 0;let{animation:e}=t;return F(e.currentTime||0)}set time(t){let{resolved:e}=this;if(!e)return;let{animation:i}=e;i.currentTime=j(t)}get speed(){let{resolved:t}=this;if(!t)return 1;let{animation:e}=t;return e.playbackRate}set speed(t){let{resolved:e}=this;if(!e)return;let{animation:i}=e;i.playbackRate=t}get state(){let{resolved:t}=this;if(!t)return"idle";let{animation:e}=t;return e.playState}get startTime(){let{resolved:t}=this;if(!t)return null;let{animation:e}=t;return e.startTime}attachTimeline(t){if(this._resolved){let{resolved:e}=this;if(!e)return B;let{animation:i}=e;m(i,t)}else this.pendingTimeline=t;return B}play(){if(this.isStopped)return;let{resolved:t}=this;if(!t)return;let{animation:e}=t;"finished"===e.playState&&this.updateFinishedPromise(),e.play()}pause(){let{resolved:t}=this;if(!t)return;let{animation:e}=t;e.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.resolveFinishedPromise(),this.updateFinishedPromise();let{resolved:t}=this;if(!t)return;let{animation:e,keyframes:i,duration:s,type:n,ease:r,times:o}=t;if("idle"===e.playState||"finished"===e.playState)return;if(this.time){let{motionValue:t,onUpdate:e,onComplete:a,element:l,...u}=this.options,h=new e8({...u,keyframes:i,duration:s,type:n,ease:r,times:o,isGenerator:!0}),d=j(this.time);t.setWithVelocity(h.sample(d-10).value,h.sample(d).value,10)}let{onStop:a}=this.options;a&&a(),this.cancel()}complete(){let{resolved:t}=this;t&&t.animation.finish()}cancel(){let{resolved:t}=this;t&&t.animation.cancel()}static supports(t){let{motionValue:e,name:i,repeatDelay:s,repeatType:n,damping:r,type:o}=t;if(!e||!e.owner||!(e.owner.current instanceof HTMLElement))return!1;let{onUpdate:a,transformTemplate:l}=e.owner.getProps();return ie()&&i&&it.has(i)&&!a&&!l&&!s&&"mirror"!==n&&0!==r&&"inertia"!==o}}let ir={type:"spring",stiffness:500,damping:25,restSpeed:10},io=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),ia={type:"keyframes",duration:.8},il={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},iu=(t,{keyframes:e})=>e.length>2?ia:I.has(t)?t.startsWith("scale")?io(e[1]):ir:il,ih=(t,e,i,s={},n,r)=>o=>{let a=d(s,t)||{},l=a.delay||s.delay||0,{elapsed:u=0}=s;u-=j(l);let c={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-u,onUpdate:t=>{e.set(t),a.onUpdate&&a.onUpdate(t)},onComplete:()=>{o(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:r?void 0:n};!function({when:t,delay:e,delayChildren:i,staggerChildren:s,staggerDirection:n,repeat:r,repeatType:o,repeatDelay:a,from:l,elapsed:u,...h}){return!!Object.keys(h).length}(a)&&(c={...c,...iu(t,c)}),c.duration&&(c.duration=j(c.duration)),c.repeatDelay&&(c.repeatDelay=j(c.repeatDelay)),void 0!==c.from&&(c.keyframes[0]=c.from);let p=!1;if(!1!==c.type&&(0!==c.duration||c.repeatDelay)||(c.duration=0,0!==c.delay||(p=!0)),(th.current||z.skipAnimations)&&(p=!0,c.duration=0,c.delay=0),p&&!r&&void 0!==e.get()){let t=eA(c.keyframes,a);if(void 0!==t)return K.update(()=>{c.onUpdate(t),c.onComplete()}),new h([])}return!r&&is.supports(c)?new is(c):new e8(c)};function id(t,e,{delay:i=0,transitionOverride:s,type:n}={}){var r;let{transition:a=t.getDefaultTransition(),transitionEnd:l,...u}=e;s&&(a=s);let h=[],c=n&&t.animationState&&t.animationState.getState()[n];for(let e in u){let s=t.getValue(e,null!==(r=t.latestValues[e])&&void 0!==r?r:null),n=u[e];if(void 0===n||c&&function({protectedKeys:t,needsAnimating:e},i){let s=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,s}(c,e))continue;let o={delay:i,...d(a||{},e)},l=!1;if(window.MotionHandoffAnimation){let i=t.props[tu];if(i){let t=window.MotionHandoffAnimation(i,e,K);null!==t&&(o.startTime=t,l=!0)}}ta(t,e),s.start(ih(e,s,n,t.shouldReduceMotion&&U.has(e)?{type:!1}:o,t,l));let p=s.animation;p&&h.push(p)}return l&&Promise.all(h).then(()=>{K.update(()=>{l&&function(t,e){let{transitionEnd:i={},transition:s={},...n}=o(t,e)||{};for(let e in n={...n,...i}){let i=W(n[e]);t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,tr(i))}}(t,l)})}),h}function ic(t,e,i={}){var s;let n=o(t,e,"exit"===i.type?null===(s=t.presenceContext)||void 0===s?void 0:s.custom:void 0),{transition:r=t.getDefaultTransition()||{}}=n||{};i.transitionOverride&&(r=i.transitionOverride);let a=n?()=>Promise.all(id(t,n,i)):()=>Promise.resolve(),l=t.variantChildren&&t.variantChildren.size?(s=0)=>{let{delayChildren:n=0,staggerChildren:o,staggerDirection:a}=r;return function(t,e,i=0,s=0,n=1,r){let o=[],a=(t.variantChildren.size-1)*s,l=1===n?(t=0)=>t*s:(t=0)=>a-t*s;return Array.from(t.variantChildren).sort(ip).forEach((t,s)=>{t.notify("AnimationStart",e),o.push(ic(t,e,{...r,delay:i+l(s)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(o)}(t,e,n+s,o,a,i)}:()=>Promise.resolve(),{when:u}=r;if(!u)return Promise.all([a(),l(i.delay)]);{let[t,e]="beforeChildren"===u?[a,l]:[l,a];return t().then(()=>e())}}function ip(t,e){return t.sortNodePosition(e)}function im(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let s=0;s<i;s++)if(e[s]!==t[s])return!1;return!0}function iv(t){return"string"==typeof t||Array.isArray(t)}let ig=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],iy=["initial",...ig],ix=iy.length,iP=[...ig].reverse(),iT=ig.length;function iw(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function ib(){return{animate:iw(!0),whileInView:iw(),whileHover:iw(),whileTap:iw(),whileDrag:iw(),whileFocus:iw(),exit:iw()}}class iS{constructor(t){this.isMounted=!1,this.node=t}update(){}}class iA extends iS{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(function(t,e,i={}){let s;if(t.notify("AnimationStart",e),Array.isArray(e))s=Promise.all(e.map(e=>ic(t,e,i)));else if("string"==typeof e)s=ic(t,e,i);else{let n="function"==typeof e?o(t,e,i.custom):e;s=Promise.all(id(t,n,i))}return s.then(()=>{t.notify("AnimationComplete",e)})})(t,e,i))),i=ib(),s=!0,r=e=>(i,s)=>{var n;let r=o(t,s,"exit"===e?null===(n=t.presenceContext)||void 0===n?void 0:n.custom:void 0);if(r){let{transition:t,transitionEnd:e,...s}=r;i={...i,...s,...e}}return i};function a(a){let{props:l}=t,u=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<ix;t++){let s=iy[t],n=e.props[s];(iv(n)||!1===n)&&(i[s]=n)}return i}(t.parent)||{},h=[],d=new Set,c={},p=1/0;for(let e=0;e<iT;e++){var m;let o=iP[e],f=i[o],v=void 0!==l[o]?l[o]:u[o],g=iv(v),y=o===a?f.isActive:null;!1===y&&(p=e);let x=v===u[o]&&v!==l[o]&&g;if(x&&s&&t.manuallyAnimateOnMount&&(x=!1),f.protectedKeys={...c},!f.isActive&&null===y||!v&&!f.prevProp||n(v)||"boolean"==typeof v)continue;let P=(m=f.prevProp,"string"==typeof v?v!==m:!!Array.isArray(v)&&!im(v,m)),T=P||o===a&&f.isActive&&!x&&g||e>p&&g,w=!1,b=Array.isArray(v)?v:[v],S=b.reduce(r(o),{});!1===y&&(S={});let{prevResolvedValues:A={}}=f,E={...A,...S},M=e=>{T=!0,d.has(e)&&(w=!0,d.delete(e)),f.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in E){let e=S[t],i=A[t];if(!c.hasOwnProperty(t))($(e)&&$(i)?im(e,i):e===i)?void 0!==e&&d.has(t)?M(t):f.protectedKeys[t]=!0:null!=e?M(t):d.add(t)}f.prevProp=v,f.prevResolvedValues=S,f.isActive&&(c={...c,...S}),s&&t.blockInitialAnimation&&(T=!1);let V=!(x&&P)||w;T&&V&&h.push(...b.map(t=>({animation:t,options:{type:o}})))}if(d.size){let e={};if("boolean"!=typeof l.initial){let i=o(t,Array.isArray(l.initial)?l.initial[0]:l.initial);i&&i.transition&&(e.transition=i.transition)}d.forEach(i=>{let s=t.getBaseTarget(i),n=t.getValue(i);n&&(n.liveStyle=!0),e[i]=null!=s?s:null}),h.push({animation:e})}let f=!!h.length;return s&&(!1===l.initial||l.initial===l.animate)&&!t.manuallyAnimateOnMount&&(f=!1),s=!1,f?e(h):Promise.resolve()}return{animateChanges:a,setActive:function(e,s){var n;if(i[e].isActive===s)return Promise.resolve();null===(n=t.variantChildren)||void 0===n||n.forEach(t=>{var i;return null===(i=t.animationState)||void 0===i?void 0:i.setActive(e,s)}),i[e].isActive=s;let r=a(e);for(let t in i)i[t].protectedKeys={};return r},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=ib(),s=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();n(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){var t;this.node.animationState.reset(),null===(t=this.unmountControls)||void 0===t||t.call(this)}}let iE=0;class iM extends iS{constructor(){super(...arguments),this.id=iE++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let s=this.node.animationState.setActive("exit",!t);e&&!t&&s.then(()=>{e(this.id)})}mount(){let{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}function iV(t,e,i,s={passive:!0}){return t.addEventListener(e,i,s),()=>t.removeEventListener(e,i)}function iC(t){return{point:{x:t.pageX,y:t.pageY}}}let iD=t=>e=>M(e)&&t(e,iC(e));function ik(t,e,i,s){return iV(t,e,iD(i),s)}function iR({top:t,left:e,right:i,bottom:s}){return{x:{min:e,max:i},y:{min:t,max:s}}}function iL(t){return t.max-t.min}function ij(t,e,i,s=.5){t.origin=s,t.originPoint=eV(e.min,e.max,t.origin),t.scale=iL(i)/iL(e),t.translate=eV(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function iF(t,e,i,s){ij(t.x,e.x,i.x,s?s.originX:void 0),ij(t.y,e.y,i.y,s?s.originY:void 0)}function iB(t,e,i){t.min=i.min+e.min,t.max=t.min+iL(e)}function iO(t,e,i){t.min=e.min-i.min,t.max=t.min+iL(e)}function iI(t,e,i){iO(t.x,e.x,i.x),iO(t.y,e.y,i.y)}let iU=()=>({translate:0,scale:1,origin:0,originPoint:0}),i$=()=>({x:iU(),y:iU()}),iN=()=>({min:0,max:0}),iW=()=>({x:iN(),y:iN()});function iz(t){return[t("x"),t("y")]}function iH(t){return void 0===t||1===t}function iY({scale:t,scaleX:e,scaleY:i}){return!iH(t)||!iH(e)||!iH(i)}function iX(t){return iY(t)||iK(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function iK(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}function iq(t,e,i,s,n){return void 0!==n&&(t=s+n*(t-s)),s+i*(t-s)+e}function iG(t,e=0,i=1,s,n){t.min=iq(t.min,e,i,s,n),t.max=iq(t.max,e,i,s,n)}function i_(t,{x:e,y:i}){iG(t.x,e.translate,e.scale,e.originPoint),iG(t.y,i.translate,i.scale,i.originPoint)}function iZ(t,e){t.min=t.min+e,t.max=t.max+e}function iQ(t,e,i,s,n=.5){let r=eV(t.min,t.max,n);iG(t,e,i,r,s)}function iJ(t,e){iQ(t.x,e.x,e.scaleX,e.scale,e.originX),iQ(t.y,e.y,e.scaleY,e.scale,e.originY)}function i0(t,e){return iR(function(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),s=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:s.y,right:s.x}}(t.getBoundingClientRect(),e))}function i1(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}let i5=(t,e)=>Math.abs(t-e);class i2{constructor(t,e,{transformPagePoint:i,dragSnapToOrigin:s=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=i4(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,i=function(t,e){return Math.sqrt(i5(t.x,e.x)**2+i5(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=3;if(!e&&!i)return;let{point:s}=t,{timestamp:n}=G;this.history.push({...s,timestamp:n});let{onStart:r,onMove:o}=this.handlers;e||(r&&r(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),o&&o(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{if(t.target instanceof Element&&t.target.hasPointerCapture&&void 0!==t.pointerId)try{if(!t.target.hasPointerCapture(t.pointerId))return}catch(t){}this.lastMoveEvent=t,this.lastMoveEventInfo=i3(e,this.transformPagePoint),K.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{A(t,"release"),this.end();let{onEnd:i,onSessionEnd:s,resumeAnimation:n}=this.handlers;if(this.dragSnapToOrigin&&n&&n(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let r=i4("pointercancel"===t.type||"lostpointercapture"===t.type?this.lastMoveEventInfo:i3(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,r),s&&s(t,r)},!M(t))return;this.dragSnapToOrigin=s,this.handlers=e,this.transformPagePoint=i;let n=i3(iC(t),this.transformPagePoint),{point:r}=n,{timestamp:o}=G;this.history=[{...r,timestamp:o}];let{onSessionStart:a}=e;a&&a(t,i4(n,this.history)),A(t,"set"),this.removeListeners=eO(ik(t.currentTarget,"pointermove",this.handlePointerMove),ik(t.currentTarget,"pointerup",this.handlePointerUp),ik(t.currentTarget,"pointercancel",this.handlePointerUp),ik(t.currentTarget,"lostpointercapture",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),q(this.updatePoint)}}function i3(t,e){return e?{point:e(t.point)}:t}function i9(t,e){return{x:t.x-e.x,y:t.y-e.y}}function i4({point:t},e){return{point:t,delta:i9(t,i6(e)),offset:i9(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,s=null,n=i6(t);for(;i>=0&&(s=t[i],!(n.timestamp-s.timestamp>j(.1)));)i--;if(!s)return{x:0,y:0};let r=F(n.timestamp-s.timestamp);if(0===r)return{x:0,y:0};let o={x:(n.x-s.x)/r,y:(n.y-s.y)/r};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}(e,0)}}function i6(t){return t[t.length-1]}function i7(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function i8(t,e){let i=e.min-t.min,s=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,s]=[s,i]),{min:i,max:s}}function st(t,e,i){return{min:se(t,e),max:se(t,i)}}function se(t,e){return"number"==typeof t?t:t[e]||0}let si=new WeakMap;class ss{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=iW(),this.visualElement=t}start(t,{snapToCursor:e=!1}={}){let{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;let{dragSnapToOrigin:s}=this.getProps();this.panSession=new i2(t,{onSessionStart:t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(iC(t).point)},onStart:(t,e)=>{let{drag:i,dragPropagation:s,onDragStart:n}=this.getProps();if(i&&!s&&(this.openDragLock&&this.openDragLock(),this.openDragLock="x"===i||"y"===i?w[i]?null:(w[i]=!0,()=>{w[i]=!1}):w.x||w.y?null:(w.x=w.y=!0,()=>{w.x=w.y=!1}),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),iz(t=>{let e=this.getAxisMotionValue(t).get()||0;if(tI.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let s=i.layout.layoutBox[t];s&&(e=iL(s)*(parseFloat(e)/100))}}this.originPoint[t]=e}),n&&K.postRender(()=>n(t,e)),ta(this.visualElement,"transform");let{animationState:r}=this.visualElement;r&&r.setActive("whileDrag",!0)},onMove:(t,e)=>{let{dragPropagation:i,dragDirectionLock:s,onDirectionLock:n,onDrag:r}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:o}=e;if(s&&null===this.currentDirection){this.currentDirection=function(t,e=10){let i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(o),null!==this.currentDirection&&n&&n(this.currentDirection);return}this.updateAxis("x",e.point,o),this.updateAxis("y",e.point,o),this.visualElement.render(),r&&r(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>iz(t=>{var e;return"paused"===this.getAnimationState(t)&&(null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.play())})},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:s})}stop(t,e){let i=this.isDragging;if(this.cancel(),!i)return;let{velocity:s}=e;this.startAnimation(s);let{onDragEnd:n}=this.getProps();n&&K.postRender(()=>n(t,e))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:s}=this.getProps();if(!i||!sn(t,s,this.currentDirection))return;let n=this.getAxisMotionValue(t),r=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(r=function(t,{min:e,max:i},s){return void 0!==e&&t<e?t=s?eV(e,t,s.min):Math.max(t,e):void 0!==i&&t>i&&(t=s?eV(i,t,s.max):Math.min(t,i)),t}(r,this.constraints[t],this.elastic[t])),n.set(r)}resolveConstraints(){var t;let{dragConstraints:e,dragElastic:i}=this.getProps(),s=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null===(t=this.visualElement.projection)||void 0===t?void 0:t.layout,n=this.constraints;e&&i1(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&s?this.constraints=function(t,{top:e,left:i,bottom:s,right:n}){return{x:i7(t.x,i,n),y:i7(t.y,e,s)}}(s.layoutBox,e):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:st(t,"left","right"),y:st(t,"top","bottom")}}(i),n!==this.constraints&&s&&this.constraints&&!this.hasMutatedConstraints&&iz(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(s.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!i1(e))return!1;let s=e.current;B(null!==s,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:n}=this.visualElement;if(!n||!n.layout)return!1;let r=function(t,e,i){let s=i0(t,i),{scroll:n}=e;return n&&(iZ(s.x,n.offset.x),iZ(s.y,n.offset.y)),s}(s,n.root,this.visualElement.getTransformPagePoint()),o={x:i8((t=n.layout.layoutBox).x,r.x),y:i8(t.y,r.y)};if(i){let t=i(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=iR(t))}return o}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:s,dragTransition:n,dragSnapToOrigin:r,onDragTransitionEnd:o}=this.getProps(),a=this.constraints||{};return Promise.all(iz(o=>{if(!sn(o,e,this.currentDirection))return;let l=a&&a[o]||{};r&&(l={min:0,max:0});let u={type:"inertia",velocity:i?t[o]:0,bounceStiffness:s?200:1e6,bounceDamping:s?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...n,...l};return this.startAxisValueAnimation(o,u)})).then(o)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return ta(this.visualElement,t),i.start(ih(t,i,0,e,this.visualElement,!1))}stopAnimation(){iz(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){iz(t=>{var e;return null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.pause()})}getAnimationState(t){var e;return null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){iz(e=>{let{drag:i}=this.getProps();if(!sn(e,i,this.currentDirection))return;let{projection:s}=this.visualElement,n=this.getAxisMotionValue(e);if(s&&s.layout){let{min:i,max:r}=s.layout.layoutBox[e];n.set(t[e]-eV(i,r,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!i1(e)||!i||!this.constraints)return;this.stopAnimation();let s={x:0,y:0};iz(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let i=e.get();s[t]=function(t,e){let i=.5,s=iL(t),n=iL(e);return n>s?i=y(e.min,e.max-s,t.min):s>n&&(i=y(t.min,t.max-n,e.min)),tb(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:n}=this.visualElement.getProps();this.visualElement.current.style.transform=n?n({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),iz(e=>{if(!sn(e,t,null))return;let i=this.getAxisMotionValue(e),{min:n,max:r}=this.constraints[e];i.set(eV(n,r,s[e]))})}addListeners(){if(!this.visualElement.current)return;si.set(this.visualElement,this);let t=ik(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();i1(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,s=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),K.read(e);let n=iV(window,"resize",()=>this.scalePositionWithinConstraints()),r=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(iz(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{n(),t(),s(),r&&r()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:s=!1,dragConstraints:n=!1,dragElastic:r=.35,dragMomentum:o=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:s,dragConstraints:n,dragElastic:r,dragMomentum:o}}}function sn(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class sr extends iS{constructor(t){super(t),this.removeGroupControls=B,this.removeListeners=B,this.controls=new ss(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||B}unmount(){this.removeGroupControls(),this.removeListeners()}}let so=t=>(e,i)=>{t&&K.postRender(()=>t(e,i))};class sa extends iS{constructor(){super(...arguments),this.removePointerDownListener=B}onPointerDown(t){this.session=new i2(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint()})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:s}=this.node.getProps();return{onSessionStart:so(t),onStart:so(e),onMove:i,onEnd:(t,e)=>{delete this.session,s&&K.postRender(()=>s(t,e))}}}mount(){this.removePointerDownListener=ik(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var sl,su,sh=i(5155),sd=i(2115),sc=i(5087),sp=i(4710);let sm=(0,sd.createContext)({}),sf={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function sv(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let sg={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t){if(!tU.test(t))return t;t=parseFloat(t)}let i=sv(t,e.target.x),s=sv(t,e.target.y);return`${i}% ${s}%`}},sy={},{schedule:sx,cancel:sP}=X(queueMicrotask,!1);class sT extends sd.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:s}=this.props,{projection:n}=t;!function(t){for(let e in t)sy[e]=t[e],em(e)&&(sy[e].isCSSVariable=!0)}(sb),n&&(e.group&&e.group.add(n),i&&i.register&&s&&i.register(n),n.root.didUpdate(),n.addEventListener("animationComplete",()=>{this.safeToRemove()}),n.setOptions({...n.options,onExitComplete:()=>this.safeToRemove()})),sf.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:s,isPresent:n}=this.props,r=i.projection;return r&&(r.isPresent=n,s||t.layoutDependency!==e||void 0===e?r.willUpdate():this.safeToRemove(),t.isPresent===n||(n?r.promote():r.relegate()||K.postRender(()=>{let t=r.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),sx.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:s}=t;s&&(s.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(s),i&&i.deregister&&i.deregister(s))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function sw(t){let[e,i]=(0,sc.xQ)(),s=(0,sd.useContext)(sp.L);return(0,sh.jsx)(sT,{...t,layoutGroup:s,switchLayoutGroup:(0,sd.useContext)(sm),isPresent:e,safeToRemove:i})}let sb={borderRadius:{...sg,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:sg,borderTopRightRadius:sg,borderBottomLeftRadius:sg,borderBottomRightRadius:sg,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let s=tJ.parse(t);if(s.length>5)return t;let n=tJ.createTransformer(t),r="number"!=typeof s[0]?1:0,o=i.x.scale*e.x,a=i.y.scale*e.y;s[0+r]/=o,s[1+r]/=a;let l=eV(o,a,.5);return"number"==typeof s[2+r]&&(s[2+r]/=l),"number"==typeof s[3+r]&&(s[3+r]/=l),n(s)}}},sS=(t,e)=>t.depth-e.depth;class sA{constructor(){this.children=[],this.isDirty=!1}add(t){J(this.children,t),this.isDirty=!0}remove(t){tt(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(sS),this.isDirty=!1,this.children.forEach(t)}}function sE(t){let e=to(t)?t.get():t;return N(e)?e.toValue():e}let sM=["TopLeft","TopRight","BottomLeft","BottomRight"],sV=sM.length,sC=t=>"string"==typeof t?parseFloat(t):t,sD=t=>"number"==typeof t||tU.test(t);function sk(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let sR=sj(0,.5,tP),sL=sj(.5,.95,B);function sj(t,e,i){return s=>s<t?0:s>e?1:i(y(t,e,s))}function sF(t,e){t.min=e.min,t.max=e.max}function sB(t,e){sF(t.x,e.x),sF(t.y,e.y)}function sO(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function sI(t,e,i,s,n){return t-=e,t=s+1/i*(t-s),void 0!==n&&(t=s+1/n*(t-s)),t}function sU(t,e,[i,s,n],r,o){!function(t,e=0,i=1,s=.5,n,r=t,o=t){if(tI.test(e)&&(e=parseFloat(e),e=eV(o.min,o.max,e/100)-o.min),"number"!=typeof e)return;let a=eV(r.min,r.max,s);t===r&&(a-=e),t.min=sI(t.min,e,i,a,n),t.max=sI(t.max,e,i,a,n)}(t,e[i],e[s],e[n],e.scale,r,o)}let s$=["x","scaleX","originX"],sN=["y","scaleY","originY"];function sW(t,e,i,s){sU(t.x,e,s$,i?i.x:void 0,s?s.x:void 0),sU(t.y,e,sN,i?i.y:void 0,s?s.y:void 0)}function sz(t){return 0===t.translate&&1===t.scale}function sH(t){return sz(t.x)&&sz(t.y)}function sY(t,e){return t.min===e.min&&t.max===e.max}function sX(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function sK(t,e){return sX(t.x,e.x)&&sX(t.y,e.y)}function sq(t){return iL(t.x)/iL(t.y)}function sG(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class s_{constructor(){this.members=[]}add(t){J(this.members,t),t.scheduleRender()}remove(t){if(tt(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e;let i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:s}=t.options;!1===s&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let sZ={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},sQ=["","X","Y","Z"],sJ={visibility:"hidden"},s0=0;function s1(t,e,i,s){let{latestValues:n}=e;n[t]&&(i[t]=n[t],e.setStaticValue(t,0),s&&(s[t]=0))}function s5({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:s,resetTransform:n}){return class{constructor(t={},i=null==e?void 0:e()){this.id=s0++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,Y.value&&(sZ.nodes=sZ.calculatedTargetDeltas=sZ.calculatedProjections=0),this.nodes.forEach(s9),this.nodes.forEach(ni),this.nodes.forEach(ns),this.nodes.forEach(s4),Y.addProjectionMetrics&&Y.addProjectionMetrics(sZ)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new sA)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new te),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e,i=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=e instanceof SVGElement&&"svg"!==e.tagName,this.instance=e;let{layoutId:s,layout:n,visualElement:r}=this.options;if(r&&!r.current&&r.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),i&&(n||s)&&(this.isLayoutDirty=!0),t){let i;let s=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=Q.now(),s=({timestamp:e})=>{let n=e-i;n>=250&&(q(s),t(n-250))};return K.read(s,!0),()=>q(s)}(s,250),sf.hasAnimatedSinceResize&&(sf.hasAnimatedSinceResize=!1,this.nodes.forEach(ne))})}s&&this.root.registerSharedNode(s,this),!1!==this.options.animate&&r&&(s||n)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:i,layout:s})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let n=this.options.transition||r.getDefaultTransition()||nu,{onLayoutAnimationStart:o,onLayoutAnimationComplete:a}=r.getProps(),l=!this.targetLayout||!sK(this.targetLayout,s),u=!e&&i;if(this.options.layoutRoot||this.resumeFrom||u||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(t,u);let e={...d(n,"layout"),onPlay:o,onComplete:a};(r.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e)}else e||ne(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=s})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,q(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(nn),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let s=i.props[tu];if(window.MotionHasOptimisedAnimation(s,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(s,"transform",K,!(t||i))}let{parent:n}=e;n&&!n.hasCheckedOptimisedAppear&&t(n)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let s=this.getTransformTemplate();this.prevTransformTemplateValue=s?s(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(s7);return}this.isUpdating||this.nodes.forEach(s8),this.isUpdating=!1,this.nodes.forEach(nt),this.nodes.forEach(s2),this.nodes.forEach(s3),this.clearAllSnapshots();let t=Q.now();G.delta=tb(0,1e3/60,t-G.timestamp),G.timestamp=t,G.isProcessing=!0,_.update.process(G),_.preRender.process(G),_.render.process(G),G.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,sx.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(s6),this.sharedNodes.forEach(nr)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,K.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){K.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure(),!this.snapshot||iL(this.snapshot.measuredBox.x)||iL(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=iW(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e){let e=s(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!n)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!sH(this.projectionDelta),i=this.getTransformTemplate(),s=i?i(this.latestValues,""):void 0,r=s!==this.prevTransformTemplateValue;t&&(e||iX(this.latestValues)||r)&&(n(this.instance,s),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),s=this.removeElementScroll(i);return t&&(s=this.removeTransform(s)),nc((e=s).x),nc(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:s,latestValues:{},source:this.id}}measurePageBox(){var t;let{visualElement:e}=this.options;if(!e)return iW();let i=e.measureViewportBox();if(!((null===(t=this.scroll)||void 0===t?void 0:t.wasRoot)||this.path.some(nm))){let{scroll:t}=this.root;t&&(iZ(i.x,t.offset.x),iZ(i.y,t.offset.y))}return i}removeElementScroll(t){var e;let i=iW();if(sB(i,t),null===(e=this.scroll)||void 0===e?void 0:e.wasRoot)return i;for(let e=0;e<this.path.length;e++){let s=this.path[e],{scroll:n,options:r}=s;s!==this.root&&n&&r.layoutScroll&&(n.wasRoot&&sB(i,t),iZ(i.x,n.offset.x),iZ(i.y,n.offset.y))}return i}applyTransform(t,e=!1){let i=iW();sB(i,t);for(let t=0;t<this.path.length;t++){let s=this.path[t];!e&&s.options.layoutScroll&&s.scroll&&s!==s.root&&iJ(i,{x:-s.scroll.offset.x,y:-s.scroll.offset.y}),iX(s.latestValues)&&iJ(i,s.latestValues)}return iX(this.latestValues)&&iJ(i,this.latestValues),i}removeTransform(t){let e=iW();sB(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!iX(i.latestValues))continue;iY(i.latestValues)&&i.updateSnapshot();let s=iW();sB(s,i.measurePageBox()),sW(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,s)}return iX(this.latestValues)&&sW(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==G.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){var e,i,s,n;let r=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=r.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=r.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=r.isSharedProjectionDirty);let o=!!this.resumingFrom||this!==r;if(!(t||o&&this.isSharedProjectionDirty||this.isProjectionDirty||(null===(e=this.parent)||void 0===e?void 0:e.isProjectionDirty)||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:a,layoutId:l}=this.options;if(this.layout&&(a||l)){if(this.resolvedRelativeTargetAt=G.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iW(),this.relativeTargetOrigin=iW(),iI(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),sB(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if((this.target||(this.target=iW(),this.targetWithTransforms=iW()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target)?(this.forceRelativeParentToResolveTarget(),i=this.target,s=this.relativeTarget,n=this.relativeParent.target,iB(i.x,s.x,n.x),iB(i.y,s.y,n.y)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):sB(this.target,this.layout.layoutBox),i_(this.target,this.targetDelta)):sB(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iW(),this.relativeTargetOrigin=iW(),iI(this.relativeTargetOrigin,this.target,t.target),sB(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}Y.value&&sZ.calculatedTargetDeltas++}}}getClosestProjectingParent(){return!this.parent||iY(this.parent.latestValues)||iK(this.parent.latestValues)?void 0:this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var t;let e=this.getLead(),i=!!this.resumingFrom||this!==e,s=!0;if((this.isProjectionDirty||(null===(t=this.parent)||void 0===t?void 0:t.isProjectionDirty))&&(s=!1),i&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(s=!1),this.resolvedRelativeTargetAt===G.timestamp&&(s=!1),s)return;let{layout:n,layoutId:r}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(n||r))return;sB(this.layoutCorrected,this.layout.layoutBox);let o=this.treeScale.x,a=this.treeScale.y;!function(t,e,i,s=!1){let n,r;let o=i.length;if(o){e.x=e.y=1;for(let a=0;a<o;a++){r=(n=i[a]).projectionDelta;let{visualElement:o}=n.options;(!o||!o.props.style||"contents"!==o.props.style.display)&&(s&&n.options.layoutScroll&&n.scroll&&n!==n.root&&iJ(t,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),r&&(e.x*=r.x.scale,e.y*=r.y.scale,i_(t,r)),s&&iX(n.latestValues)&&iJ(t,n.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}(this.layoutCorrected,this.treeScale,this.path,i),e.layout&&!e.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(e.target=e.layout.layoutBox,e.targetWithTransforms=iW());let{target:l}=e;if(!l){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(sO(this.prevProjectionDelta.x,this.projectionDelta.x),sO(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),iF(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.treeScale.x===o&&this.treeScale.y===a&&sG(this.projectionDelta.x,this.prevProjectionDelta.x)&&sG(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),Y.value&&sZ.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){var e;if(null===(e=this.options.visualElement)||void 0===e||e.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=i$(),this.projectionDelta=i$(),this.projectionDeltaWithTransform=i$()}setAnimationOrigin(t,e=!1){let i;let s=this.snapshot,n=s?s.latestValues:{},r={...this.latestValues},o=i$();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let a=iW(),l=(s?s.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),h=!u||u.members.length<=1,d=!!(l&&!h&&!0===this.options.crossfade&&!this.path.some(nl));this.animationProgress=0,this.mixTargetDelta=e=>{let s=e/1e3;if(no(o.x,t.x,s),no(o.y,t.y,s),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,c,p,m;iI(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,na(p.x,m.x,a.x,s),na(p.y,m.y,a.y,s),i&&(u=this.relativeTarget,c=i,sY(u.x,c.x)&&sY(u.y,c.y))&&(this.isProjectionDirty=!1),i||(i=iW()),sB(i,this.relativeTarget)}l&&(this.animationValues=r,function(t,e,i,s,n,r){n?(t.opacity=eV(0,void 0!==i.opacity?i.opacity:1,sR(s)),t.opacityExit=eV(void 0!==e.opacity?e.opacity:1,0,sL(s))):r&&(t.opacity=eV(void 0!==e.opacity?e.opacity:1,void 0!==i.opacity?i.opacity:1,s));for(let n=0;n<sV;n++){let r=`border${sM[n]}Radius`,o=sk(e,r),a=sk(i,r);(void 0!==o||void 0!==a)&&(o||(o=0),a||(a=0),0===o||0===a||sD(o)===sD(a)?(t[r]=Math.max(eV(sC(o),sC(a),s),0),(tI.test(a)||tI.test(o))&&(t[r]+="%")):t[r]=a)}(e.rotate||i.rotate)&&(t.rotate=eV(e.rotate||0,i.rotate||0,s))}(r,n,this.latestValues,s,d,h)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=s},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(q(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=K.update(()=>{sf.hasAnimatedSinceResize=!0,eM.layout++,this.currentAnimation=function(t,e,i){let s=to(0)?0:tr(0);return s.start(ih("",s,1e3,i)),s.animation}(0,0,{...t,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{eM.layout--},onComplete:()=>{eM.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:s,latestValues:n}=t;if(e&&i&&s){if(this!==t&&this.layout&&s&&np(this.options.animationType,this.layout.layoutBox,s.layoutBox)){i=this.target||iW();let e=iL(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let s=iL(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+s}sB(e,i),iJ(e,n),iF(this.projectionDeltaWithTransform,this.layoutCorrected,e,n)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new s_),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){var t;let{layoutId:e}=this.options;return e&&(null===(t=this.getStack())||void 0===t?void 0:t.lead)||this}getPrevLead(){var t;let{layoutId:e}=this.options;return e?null===(t=this.getStack())||void 0===t?void 0:t.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let s=this.getStack();s&&s.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let s={};i.z&&s1("z",t,s,this.animationValues);for(let e=0;e<sQ.length;e++)s1(`rotate${sQ[e]}`,t,s,this.animationValues),s1(`skew${sQ[e]}`,t,s,this.animationValues);for(let e in t.render(),s)t.setStaticValue(e,s[e]),this.animationValues&&(this.animationValues[e]=s[e]);t.scheduleRender()}getProjectionStyles(t){var e,i;if(!this.instance||this.isSVG)return;if(!this.isVisible)return sJ;let s={visibility:""},n=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,s.opacity="",s.pointerEvents=sE(null==t?void 0:t.pointerEvents)||"",s.transform=n?n(this.latestValues,""):"none",s;let r=this.getLead();if(!this.projectionDelta||!this.layout||!r.target){let e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=sE(null==t?void 0:t.pointerEvents)||""),this.hasProjected&&!iX(this.latestValues)&&(e.transform=n?n({},""):"none",this.hasProjected=!1),e}let o=r.animationValues||r.latestValues;this.applyTransformsToTarget(),s.transform=function(t,e,i){let s="",n=t.x.translate/e.x,r=t.y.translate/e.y,o=(null==i?void 0:i.z)||0;if((n||r||o)&&(s=`translate3d(${n}px, ${r}px, ${o}px) `),(1!==e.x||1!==e.y)&&(s+=`scale(${1/e.x}, ${1/e.y}) `),i){let{transformPerspective:t,rotate:e,rotateX:n,rotateY:r,skewX:o,skewY:a}=i;t&&(s=`perspective(${t}px) ${s}`),e&&(s+=`rotate(${e}deg) `),n&&(s+=`rotateX(${n}deg) `),r&&(s+=`rotateY(${r}deg) `),o&&(s+=`skewX(${o}deg) `),a&&(s+=`skewY(${a}deg) `)}let a=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==a||1!==l)&&(s+=`scale(${a}, ${l})`),s||"none"}(this.projectionDeltaWithTransform,this.treeScale,o),n&&(s.transform=n(o,s.transform));let{x:a,y:l}=this.projectionDelta;for(let t in s.transformOrigin=`${100*a.origin}% ${100*l.origin}% 0`,r.animationValues?s.opacity=r===this?null!==(i=null!==(e=o.opacity)&&void 0!==e?e:this.latestValues.opacity)&&void 0!==i?i:1:this.preserveOpacity?this.latestValues.opacity:o.opacityExit:s.opacity=r===this?void 0!==o.opacity?o.opacity:"":void 0!==o.opacityExit?o.opacityExit:0,sy){if(void 0===o[t])continue;let{correct:e,applyTo:i,isCSSVariable:n}=sy[t],a="none"===s.transform?o[t]:e(o[t],r);if(i){let t=i.length;for(let e=0;e<t;e++)s[i[e]]=a}else n?this.options.visualElement.renderState.vars[t]=a:s[t]=a}return this.options.layoutId&&(s.pointerEvents=r===this?sE(null==t?void 0:t.pointerEvents)||"":"none"),s}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>{var e;return null===(e=t.currentAnimation)||void 0===e?void 0:e.stop()}),this.root.nodes.forEach(s7),this.root.sharedNodes.clear()}}}function s2(t){t.updateLayout()}function s3(t){var e;let i=(null===(e=t.resumeFrom)||void 0===e?void 0:e.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&i&&t.hasListeners("didUpdate")){let{layoutBox:e,measuredBox:s}=t.layout,{animationType:n}=t.options,r=i.source!==t.layout.source;"size"===n?iz(t=>{let s=r?i.measuredBox[t]:i.layoutBox[t],n=iL(s);s.min=e[t].min,s.max=s.min+n}):np(n,i.layoutBox,e)&&iz(s=>{let n=r?i.measuredBox[s]:i.layoutBox[s],o=iL(e[s]);n.max=n.min+o,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[s].max=t.relativeTarget[s].min+o)});let o=i$();iF(o,e,i.layoutBox);let a=i$();r?iF(a,t.applyTransform(s,!0),i.measuredBox):iF(a,e,i.layoutBox);let l=!sH(o),u=!1;if(!t.resumeFrom){let s=t.getClosestProjectingParent();if(s&&!s.resumeFrom){let{snapshot:n,layout:r}=s;if(n&&r){let o=iW();iI(o,i.layoutBox,n.layoutBox);let a=iW();iI(a,e,r.layoutBox),sK(o,a)||(u=!0),s.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=o,t.relativeParent=s)}}}t.notifyListeners("didUpdate",{layout:e,snapshot:i,delta:a,layoutDelta:o,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function s9(t){Y.value&&sZ.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function s4(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function s6(t){t.clearSnapshot()}function s7(t){t.clearMeasurements()}function s8(t){t.isLayoutDirty=!1}function nt(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function ne(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function ni(t){t.resolveTargetDelta()}function ns(t){t.calcProjection()}function nn(t){t.resetSkewAndRotation()}function nr(t){t.removeLeadSnapshot()}function no(t,e,i){t.translate=eV(e.translate,0,i),t.scale=eV(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function na(t,e,i,s){t.min=eV(e.min,i.min,s),t.max=eV(e.max,i.max,s)}function nl(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let nu={duration:.45,ease:[.4,0,.1,1]},nh=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),nd=nh("applewebkit/")&&!nh("chrome/")?Math.round:B;function nc(t){t.min=nd(t.min),t.max=nd(t.max)}function np(t,e,i){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(sq(e)-sq(i)))}function nm(t){var e;return t!==t.root&&(null===(e=t.scroll)||void 0===e?void 0:e.wasRoot)}let nf=s5({attachResizeListener:(t,e)=>iV(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),nv={current:void 0},ng=s5({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!nv.current){let t=new nf({});t.mount(window),t.setOptions({layoutScroll:!0}),nv.current=t}return nv.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});function ny(t,e,i){let{props:s}=t;t.animationState&&s.whileHover&&t.animationState.setActive("whileHover","Start"===i);let n=s["onHover"+i];n&&K.postRender(()=>n(e,iC(e)))}class nx extends iS{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[s,n,r]=b(t,i),o=t=>{if(!S(t))return;let{target:i}=t,s=e(i,t);if("function"!=typeof s||!i)return;let r=t=>{S(t)&&(s(t),i.removeEventListener("pointerleave",r))};i.addEventListener("pointerleave",r,n)};return s.forEach(t=>{t.addEventListener("pointerenter",o,n)}),r}(t,(t,e)=>(ny(this.node,e,"Start"),t=>ny(this.node,t,"End"))))}unmount(){}}class nP extends iS{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=eO(iV(this.node.current,"focus",()=>this.onFocus()),iV(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function nT(t,e,i){let{props:s}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&s.whileTap&&t.animationState.setActive("whileTap","Start"===i);let n=s["onTap"+("End"===i?"":i)];n&&K.postRender(()=>n(e,iC(e)))}class nw extends iS{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[s,n,r]=b(t,i),o=t=>{let i=t.currentTarget;if(!i||!L(t)||C.has(i))return;C.add(i),A(t,"set");let s=e(i,t),r=(t,e)=>{i.removeEventListener("pointerup",o),i.removeEventListener("pointercancel",a),A(t,"release"),L(t)&&C.has(i)&&(C.delete(i),"function"==typeof s&&s(t,{success:e}))},o=t=>{var e;t.isTrusted&&(e=i instanceof Element?i.getBoundingClientRect():{left:0,top:0,right:window.innerWidth,bottom:window.innerHeight},t.clientX<e.left||t.clientX>e.right||t.clientY<e.top||t.clientY>e.bottom)?r(t,!1):r(t,!(i instanceof Element)||E(i,t.target))},a=t=>{r(t,!1)};i.addEventListener("pointerup",o,n),i.addEventListener("pointercancel",a,n),i.addEventListener("lostpointercapture",a,n)};return s.forEach(t=>{t=i.useGlobalTarget?window:t;let e=!1;if(t instanceof HTMLElement){var s;e=!0,s=t,V.has(s.tagName)||-1!==s.tabIndex||null!==t.getAttribute("tabindex")||(t.tabIndex=0)}t.addEventListener("pointerdown",o,n),e&&t.addEventListener("focus",t=>R(t,n),n)}),r}(t,(t,e)=>(nT(this.node,e,"Start"),(t,{success:e})=>nT(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let nb=new WeakMap,nS=new WeakMap,nA=t=>{let e=nb.get(t.target);e&&e(t)},nE=t=>{t.forEach(nA)},nM={some:0,all:1};class nV extends iS{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:s="some",once:n}=t,r={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof s?s:nM[s]};return function(t,e,i){let s=function({root:t,...e}){let i=t||document;nS.has(i)||nS.set(i,{});let s=nS.get(i),n=JSON.stringify(e);return s[n]||(s[n]=new IntersectionObserver(nE,{root:t,...e})),s[n]}(e);return nb.set(t,i),s.observe(t),()=>{nb.delete(t),s.unobserve(t)}}(this.node.current,r,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,n&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:s}=this.node.getProps(),r=e?i:s;r&&r(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}let nC=(0,sd.createContext)({strict:!1});var nD=i(7249);let nk=(0,sd.createContext)({});function nR(t){return n(t.animate)||iy.some(e=>iv(t[e]))}function nL(t){return!!(nR(t)||t.variants)}function nj(t){return Array.isArray(t)?t.join(" "):t}var nF=i(5687);let nB={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},nO={};for(let t in nB)nO[t]={isEnabled:e=>nB[t].some(t=>!!e[t])};let nI=Symbol.for("motionComponentSymbol");var nU=i(9656),n$=i(5403);function nN(t,{layout:e,layoutId:i}){return I.has(t)||t.startsWith("origin")||(e||void 0!==i)&&(!!sy[t]||"opacity"===t)}let nW=(t,e)=>e&&"number"==typeof t?e.transform(t):t,nz={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},nH=O.length;function nY(t,e,i){let{style:s,vars:n,transformOrigin:r}=t,o=!1,a=!1;for(let t in e){let i=e[t];if(I.has(t)){o=!0;continue}if(em(t)){n[t]=i;continue}{let e=nW(i,t9[t]);t.startsWith("origin")?(a=!0,r[t]=e):s[t]=e}}if(!e.transform&&(o||i?s.transform=function(t,e,i){let s="",n=!0;for(let r=0;r<nH;r++){let o=O[r],a=t[o];if(void 0===a)continue;let l=!0;if(!(l="number"==typeof a?a===(o.startsWith("scale")?1:0):0===parseFloat(a))||i){let t=nW(a,t9[o]);if(!l){n=!1;let e=nz[o]||o;s+=`${e}(${t}) `}i&&(e[o]=t)}}return s=s.trim(),i?s=i(e,n?"":s):n&&(s="none"),s}(e,t.transform,i):s.transform&&(s.transform="none")),a){let{originX:t="50%",originY:e="50%",originZ:i=0}=r;s.transformOrigin=`${t} ${e} ${i}`}}let nX=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function nK(t,e,i){for(let s in e)to(e[s])||nN(s,i)||(t[s]=e[s])}let nq=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function nG(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||nq.has(t)}let n_=t=>!nG(t);try{!function(t){t&&(n_=e=>e.startsWith("on")?!nG(e):t(e))}(require("@emotion/is-prop-valid").default)}catch(t){}let nZ=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function nQ(t){if("string"!=typeof t||t.includes("-"));else if(nZ.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}let nJ={offset:"stroke-dashoffset",array:"stroke-dasharray"},n0={offset:"strokeDashoffset",array:"strokeDasharray"};function n1(t,e,i){return"string"==typeof t?t:tU.transform(e+i*t)}function n5(t,{attrX:e,attrY:i,attrScale:s,originX:n,originY:r,pathLength:o,pathSpacing:a=1,pathOffset:l=0,...u},h,d){if(nY(t,u,d),h){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:c,style:p,dimensions:m}=t;c.transform&&(m&&(p.transform=c.transform),delete c.transform),m&&(void 0!==n||void 0!==r||p.transform)&&(p.transformOrigin=function(t,e,i){let s=n1(e,t.x,t.width),n=n1(i,t.y,t.height);return`${s} ${n}`}(m,void 0!==n?n:.5,void 0!==r?r:.5)),void 0!==e&&(c.x=e),void 0!==i&&(c.y=i),void 0!==s&&(c.scale=s),void 0!==o&&function(t,e,i=1,s=0,n=!0){t.pathLength=1;let r=n?nJ:n0;t[r.offset]=tU.transform(-s);let o=tU.transform(e),a=tU.transform(i);t[r.array]=`${o} ${a}`}(c,o,a,l,!1)}let n2=()=>({...nX(),attrs:{}}),n3=t=>"string"==typeof t&&"svg"===t.toLowerCase();var n9=i(9234);let n4=t=>(e,i)=>{let s=(0,sd.useContext)(nk),o=(0,sd.useContext)(nU.t),a=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e,onUpdate:i},s,o,a){let l={latestValues:function(t,e,i,s){let o={},a=s(t,{});for(let t in a)o[t]=sE(a[t]);let{initial:l,animate:u}=t,h=nR(t),d=nL(t);e&&d&&!h&&!1!==t.inherit&&(void 0===l&&(l=e.initial),void 0===u&&(u=e.animate));let c=!!i&&!1===i.initial,p=(c=c||!1===l)?u:l;if(p&&"boolean"!=typeof p&&!n(p)){let e=Array.isArray(p)?p:[p];for(let i=0;i<e.length;i++){let s=r(t,e[i]);if(s){let{transitionEnd:t,transition:e,...i}=s;for(let t in i){let e=i[t];if(Array.isArray(e)){let t=c?e.length-1:0;e=e[t]}null!==e&&(o[t]=e)}for(let e in t)o[e]=t[e]}}}return o}(s,o,a,t),renderState:e()};return i&&(l.onMount=t=>i({props:s,current:t,...l}),l.onUpdate=t=>i(t)),l})(t,e,s,o);return i?a():(0,n9.M)(a)};function n6(t,e,i){var s;let{style:n}=t,r={};for(let o in n)(to(n[o])||e.style&&to(e.style[o])||nN(o,t)||(null===(s=null==i?void 0:i.getValue(o))||void 0===s?void 0:s.liveStyle)!==void 0)&&(r[o]=n[o]);return r}let n7={useVisualState:n4({scrapeMotionValuesFromProps:n6,createRenderState:nX})};function n8(t,e){try{e.dimensions="function"==typeof t.getBBox?t.getBBox():t.getBoundingClientRect()}catch(t){e.dimensions={x:0,y:0,width:0,height:0}}}function rt(t,{style:e,vars:i},s,n){for(let r in Object.assign(t.style,e,n&&n.getProjectionStyles(s)),i)t.style.setProperty(r,i[r])}let re=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function ri(t,e,i,s){for(let i in rt(t,e,void 0,s),e.attrs)t.setAttribute(re.has(i)?i:tl(i),e.attrs[i])}function rs(t,e,i){let s=n6(t,e,i);for(let i in t)(to(t[i])||to(e[i]))&&(s[-1!==O.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return s}let rn=["x","y","width","height","cx","cy","r"],rr={useVisualState:n4({scrapeMotionValuesFromProps:rs,createRenderState:n2,onUpdate:({props:t,prevProps:e,current:i,renderState:s,latestValues:n})=>{if(!i)return;let r=!!t.drag;if(!r){for(let t in n)if(I.has(t)){r=!0;break}}if(!r)return;let o=!e;if(e)for(let i=0;i<rn.length;i++){let s=rn[i];t[s]!==e[s]&&(o=!0)}o&&K.read(()=>{n8(i,s),K.render(()=>{n5(s,n,n3(i.tagName),t.transformTemplate),ri(i,s)})})}})},ro={current:null},ra={current:!1},rl=[...eP,tH,tJ],ru=t=>rl.find(ex(t)),rh=new WeakMap,rd=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class rc{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:s,blockInitialAnimation:n,visualState:r},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=ed,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=Q.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,K.render(this.render,!1,!0))};let{latestValues:a,renderState:l,onUpdate:u}=r;this.onUpdate=u,this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=s,this.options=o,this.blockInitialAnimation=!!n,this.isControllingVariants=nR(e),this.isVariantNode=nL(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:h,...d}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in d){let e=d[t];void 0!==a[t]&&to(e)&&e.set(a[t],!1)}}mount(t){this.current=t,rh.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),ra.current||function(){if(ra.current=!0,nF.B){if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>ro.current=t.matches;t.addListener(e),e()}else ro.current=!1}}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||ro.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in this.projection&&this.projection.unmount(),q(this.notifyUpdate),q(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let s=I.has(t);s&&this.onBindTransform&&this.onBindTransform();let n=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&K.preRender(this.notifyUpdate),s&&this.projection&&(this.projection.isTransformDirty=!0)}),r=e.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{n(),r(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in nO){let e=nO[t];if(!e)continue;let{isEnabled:i,Feature:s}=e;if(!this.features[t]&&s&&i(this.props)&&(this.features[t]=new s(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):iW()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<rd.length;e++){let i=rd[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let s=t["on"+i];s&&(this.propEventSubscriptions[i]=this.on(i,s))}this.prevMotionValues=function(t,e,i){for(let s in e){let n=e[s],r=i[s];if(to(n))t.addValue(s,n);else if(to(r))t.addValue(s,tr(n,{owner:t}));else if(r!==n){if(t.hasValue(s)){let e=t.getValue(s);!0===e.liveStyle?e.jump(n):e.hasAnimated||e.set(n)}else{let e=t.getStaticValue(s);t.addValue(s,tr(void 0!==e?e:n,{owner:t}))}}}for(let s in i)void 0===e[s]&&t.removeValue(s);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue(),this.onUpdate&&this.onUpdate(this)}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=tr(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){var i;let s=void 0===this.latestValues[t]&&this.current?null!==(i=this.getBaseTargetFromProps(this.props,t))&&void 0!==i?i:this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=s&&("string"==typeof s&&(ec(s)||tw(s))?s=parseFloat(s):!ru(s)&&tJ.test(e)&&(s=t7(t,e)),this.setBaseTarget(t,to(s)?s.get():s)),to(s)?s.get():s}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){var e;let i;let{initial:s}=this.props;if("string"==typeof s||"object"==typeof s){let n=r(this.props,s,null===(e=this.presenceContext)||void 0===e?void 0:e.custom);n&&(i=n[t])}if(s&&void 0!==i)return i;let n=this.getBaseTargetFromProps(this.props,t);return void 0===n||to(n)?void 0!==this.initialValues[t]&&void 0===i?void 0:this.baseTarget[t]:n}on(t,e){return this.events[t]||(this.events[t]=new te),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class rp extends rc{constructor(){super(...arguments),this.KeyframeResolver=ew}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;to(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}class rm extends rp{constructor(){super(...arguments),this.type="html",this.renderInstance=rt}readValueFromInstance(t,e){if(I.has(e)){let t=t6(e);return t&&t.default||0}{let i=window.getComputedStyle(t),s=(em(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof s?s.trim():s}}measureInstanceViewportBox(t,{transformPagePoint:e}){return i0(t,e)}build(t,e,i){nY(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return n6(t,e,i)}}class rf extends rp{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=iW,this.updateDimensions=()=>{this.current&&!this.renderState.dimensions&&n8(this.current,this.renderState)}}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(I.has(e)){let t=t6(e);return t&&t.default||0}return e=re.has(e)?e:tl(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return rs(t,e,i)}onBindTransform(){this.current&&!this.renderState.dimensions&&K.postRender(this.updateDimensions)}build(t,e,i){n5(t,e,this.isSVGTag,i.transformTemplate)}renderInstance(t,e,i,s){ri(t,e,i,s)}mount(t){this.isSVGTag=n3(t.tagName),super.mount(t)}}let rv=function(t){if("undefined"==typeof Proxy)return t;let e=new Map;return new Proxy((...e)=>t(...e),{get:(i,s)=>"create"===s?t:(e.has(s)||e.set(s,t(s)),e.get(s))})}((sl={animation:{Feature:iA},exit:{Feature:iM},inView:{Feature:nV},tap:{Feature:nw},focus:{Feature:nP},hover:{Feature:nx},pan:{Feature:sa},drag:{Feature:sr,ProjectionNode:ng,MeasureLayout:sw},layout:{ProjectionNode:ng,MeasureLayout:sw}},su=(t,e)=>nQ(t)?new rf(e):new rm(e,{allowProjection:t!==sd.Fragment}),function(t,{forwardMotionProps:e}={forwardMotionProps:!1}){return function(t){var e,i;let{preloadedFeatures:s,createVisualElement:n,useRender:r,useVisualState:o,Component:a}=t;function l(t,e){var i;let s;let l={...(0,sd.useContext)(nD.Q),...t,layoutId:function(t){let{layoutId:e}=t,i=(0,sd.useContext)(sp.L).id;return i&&void 0!==e?i+"-"+e:e}(t)},{isStatic:u}=l,h=function(t){let{initial:e,animate:i}=function(t,e){if(nR(t)){let{initial:e,animate:i}=t;return{initial:!1===e||iv(e)?e:void 0,animate:iv(i)?i:void 0}}return!1!==t.inherit?e:{}}(t,(0,sd.useContext)(nk));return(0,sd.useMemo)(()=>({initial:e,animate:i}),[nj(e),nj(i)])}(t),d=o(t,u);if(!u&&nF.B){(0,sd.useContext)(nC).strict;let t=function(t){let{drag:e,layout:i}=nO;if(!e&&!i)return{};let s={...e,...i};return{MeasureLayout:(null==e?void 0:e.isEnabled(t))||(null==i?void 0:i.isEnabled(t))?s.MeasureLayout:void 0,ProjectionNode:s.ProjectionNode}}(l);s=t.MeasureLayout,h.visualElement=function(t,e,i,s,n){var r,o;let{visualElement:a}=(0,sd.useContext)(nk),l=(0,sd.useContext)(nC),u=(0,sd.useContext)(nU.t),h=(0,sd.useContext)(nD.Q).reducedMotion,d=(0,sd.useRef)(null);s=s||l.renderer,!d.current&&s&&(d.current=s(t,{visualState:e,parent:a,props:i,presenceContext:u,blockInitialAnimation:!!u&&!1===u.initial,reducedMotionConfig:h}));let c=d.current,p=(0,sd.useContext)(sm);c&&!c.projection&&n&&("html"===c.type||"svg"===c.type)&&function(t,e,i,s){let{layoutId:n,layout:r,drag:o,dragConstraints:a,layoutScroll:l,layoutRoot:u}=e;t.projection=new i(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:n,layout:r,alwaysMeasureLayout:!!o||a&&i1(a),visualElement:t,animationType:"string"==typeof r?r:"both",initialPromotionConfig:s,layoutScroll:l,layoutRoot:u})}(d.current,i,n,p);let m=(0,sd.useRef)(!1);(0,sd.useInsertionEffect)(()=>{c&&m.current&&c.update(i,u)});let f=i[tu],v=(0,sd.useRef)(!!f&&!(null===(r=window.MotionHandoffIsComplete)||void 0===r?void 0:r.call(window,f))&&(null===(o=window.MotionHasOptimisedAnimation)||void 0===o?void 0:o.call(window,f)));return(0,n$.E)(()=>{c&&(m.current=!0,window.MotionIsMounted=!0,c.updateFeatures(),sx.render(c.render),v.current&&c.animationState&&c.animationState.animateChanges())}),(0,sd.useEffect)(()=>{c&&(!v.current&&c.animationState&&c.animationState.animateChanges(),v.current&&(queueMicrotask(()=>{var t;null===(t=window.MotionHandoffMarkAsComplete)||void 0===t||t.call(window,f)}),v.current=!1))}),c}(a,d,l,n,t.ProjectionNode)}return(0,sh.jsxs)(nk.Provider,{value:h,children:[s&&h.visualElement?(0,sh.jsx)(s,{visualElement:h.visualElement,...l}):null,r(a,t,(i=h.visualElement,(0,sd.useCallback)(t=>{t&&d.onMount&&d.onMount(t),i&&(t?i.mount(t):i.unmount()),e&&("function"==typeof e?e(t):i1(e)&&(e.current=t))},[i])),d,u,h.visualElement)]})}s&&function(t){for(let e in t)nO[e]={...nO[e],...t[e]}}(s),l.displayName="motion.".concat("string"==typeof a?a:"create(".concat(null!==(i=null!==(e=a.displayName)&&void 0!==e?e:a.name)&&void 0!==i?i:"",")"));let u=(0,sd.forwardRef)(l);return u[nI]=a,u}({...nQ(t)?rr:n7,preloadedFeatures:sl,useRender:function(t=!1){return(e,i,s,{latestValues:n},r)=>{let o=(nQ(e)?function(t,e,i,s){let n=(0,sd.useMemo)(()=>{let i=n2();return n5(i,e,n3(s),t.transformTemplate),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};nK(e,t.style,t),n.style={...e,...n.style}}return n}:function(t,e){let i={},s=function(t,e){let i=t.style||{},s={};return nK(s,i,t),Object.assign(s,function({transformTemplate:t},e){return(0,sd.useMemo)(()=>{let i=nX();return nY(i,e,t),Object.assign({},i.vars,i.style)},[e])}(t,e)),s}(t,e);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,s.userSelect=s.WebkitUserSelect=s.WebkitTouchCallout="none",s.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=s,i})(i,n,r,e),a=function(t,e,i){let s={};for(let n in t)("values"!==n||"object"!=typeof t.values)&&(n_(n)||!0===i&&nG(n)||!e&&!nG(n)||t.draggable&&n.startsWith("onDrag"))&&(s[n]=t[n]);return s}(i,"string"==typeof e,t),l=e!==sd.Fragment?{...a,...o,ref:s}:{},{children:u}=i,h=(0,sd.useMemo)(()=>to(u)?u.get():u,[u]);return(0,sd.createElement)(e,{...l,children:h})}}(e),createVisualElement:su,Component:t})}))},5687:(t,e,i)=>{i.d(e,{B:()=>s});let s="undefined"!=typeof window},9234:(t,e,i)=>{i.d(e,{M:()=>n});var s=i(2115);function n(t){let e=(0,s.useRef)(null);return null===e.current&&(e.current=t()),e.current}},5403:(t,e,i)=>{i.d(e,{E:()=>n});var s=i(2115);let n=i(5687).B?s.useLayoutEffect:s.useEffect}}]);