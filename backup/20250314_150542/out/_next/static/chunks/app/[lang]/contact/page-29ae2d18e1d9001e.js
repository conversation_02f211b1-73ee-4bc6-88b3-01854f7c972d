(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[680],{2204:(e,t,r)=>{Promise.resolve().then(r.bind(r,8897))},8897:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});var s=r(5155),a=r(2115),l=r(4889),o=r(7468);function i(e){var t,r,i,n;let{t:d,lang:c}=e,[m,u]=(0,a.useState)("general"),[h,x]=(0,a.useState)(!1),[p,g]=(0,a.useState)(null),[f,v]=(0,a.useState)({name:"",email:"",subject:"",message:"",privacy:!1}),b=e=>{let{name:t,value:r}=e.target;v({...f,[t]:r})},w=async e=>{if(e.preventDefault(),!f.email||!f.message){g({success:!1,message:"Per favore, compila tutti i campi obbligatori."});return}if(!f.privacy){g({success:!1,message:"Devi accettare la privacy policy per procedere."});return}x(!0),g(null);try{let e=await fetch("/api/send-email.php",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:f.name,email:f.email,subject:f.subject||"Richiesta informazioni",message:f.message,isEmergency:!1})}),t=await e.json();e.ok?(g({success:!0,message:"Grazie! Il tuo messaggio \xe8 stato inviato con successo. Ti risponderemo al pi\xf9 presto."}),v({name:"",email:"",subject:"",message:"",privacy:!1})):g({success:!1,message:t.error||"Si \xe8 verificato un errore durante l'invio del messaggio. Riprova pi\xf9 tardi."})}catch(e){console.error("Errore nell'invio del form:",e),g({success:!1,message:"Si \xe8 verificato un errore durante l'invio del messaggio. Riprova pi\xf9 tardi."})}finally{x(!1)}};return(0,s.jsxs)("main",{children:[(0,s.jsxs)("section",{className:"relative h-[75vh] min-h-[550px] overflow-hidden bg-gradient-to-br from-blue-900 via-purple-900 to-red-900 pt-16",children:[(0,s.jsxs)("div",{className:"absolute inset-0 z-0",children:[(0,s.jsx)("div",{className:"absolute w-full h-full opacity-20",style:{backgroundImage:'url("/images/noise.png")',backgroundRepeat:"repeat"}}),(0,s.jsx)("div",{className:"absolute top-0 left-0 right-0 h-1/3 bg-gradient-to-b from-blue-500/10 to-transparent"}),(0,s.jsx)("div",{className:"absolute inset-0",style:{backgroundImage:"radial-gradient(circle at 30% 50%, rgba(255, 255, 255, 0.1) 0%, transparent 25%), radial-gradient(circle at 70% 30%, rgba(255, 255, 255, 0.08) 0%, transparent 25%)"}}),(0,s.jsx)("div",{className:"absolute -top-24 -right-24 w-96 h-96 bg-red-500 rounded-full mix-blend-soft-light filter blur-3xl opacity-30 animate-blob animation-delay-2000"}),(0,s.jsx)("div",{className:"absolute top-1/3 -left-20 w-72 h-72 bg-blue-500 rounded-full mix-blend-soft-light filter blur-3xl opacity-30 animate-blob animation-delay-4000"}),(0,s.jsx)("div",{className:"absolute -bottom-32 left-1/3 w-80 h-80 bg-purple-500 rounded-full mix-blend-soft-light filter blur-3xl opacity-30 animate-blob"}),(0,s.jsx)("div",{className:"absolute top-1/2 right-1/4 w-64 h-64 bg-pink-500 rounded-full mix-blend-soft-light filter blur-3xl opacity-30 animate-blob animation-delay-3000"}),(0,s.jsxs)("div",{className:"absolute inset-0 overflow-hidden opacity-20",children:[(0,s.jsx)("div",{className:"absolute top-[10%] left-0 right-0 h-[1px] bg-gradient-to-r from-transparent via-blue-400 to-transparent animate-pulse-slow"}),(0,s.jsx)("div",{className:"absolute top-[30%] left-0 right-0 h-[1px] bg-gradient-to-r from-transparent via-red-400 to-transparent animate-pulse-slow animation-delay-2000"}),(0,s.jsx)("div",{className:"absolute top-[70%] left-0 right-0 h-[1px] bg-gradient-to-r from-transparent via-purple-400 to-transparent animate-pulse-slow animation-delay-3000"}),(0,s.jsx)("div",{className:"absolute bottom-[20%] left-0 right-0 h-[1px] bg-gradient-to-r from-transparent via-indigo-400 to-transparent animate-pulse-slow animation-delay-4000"})]})]}),(0,s.jsx)("div",{className:"absolute bottom-0 left-0 right-0 z-10",children:(0,s.jsxs)("div",{className:"relative h-16",children:[(0,s.jsx)("div",{className:"absolute top-0 inset-x-0 h-8 bg-gradient-to-b from-transparent to-white/10"}),(0,s.jsx)("div",{className:"absolute top-8 inset-x-0 h-[1px] bg-white/20"}),(0,s.jsx)("div",{className:"absolute bottom-0 inset-x-0 h-8 bg-white",style:{clipPath:"polygon(0 100%, 100% 100%, 100% 0)"}}),(0,s.jsx)("div",{className:"absolute bottom-0 inset-x-0 h-8 bg-white",style:{clipPath:"polygon(0 100%, 0 0, 100% 100%)"}})]})}),(0,s.jsx)("div",{className:"container max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10 h-full flex flex-col justify-center",children:(0,s.jsxs)("div",{className:"text-center max-w-3xl mx-auto",children:[(0,s.jsx)(l.A,{delay:.2,children:(0,s.jsxs)("div",{className:"inline-flex items-center px-4 py-1.5 rounded-full bg-gradient-to-r from-red-500 to-purple-600 text-white text-sm font-medium mb-5 shadow-lg border border-white/10 backdrop-blur-sm relative overflow-hidden group",children:[(0,s.jsx)("span",{className:"absolute inset-0 bg-gradient-to-r from-blue-500/20 to-purple-500/20 group-hover:opacity-80 opacity-0 transition-opacity duration-300"}),(0,s.jsx)("span",{className:"absolute top-0 left-0 w-full h-full bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full animate-shimmer"}),(0,s.jsxs)("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:[(0,s.jsx)("path",{d:"M2 5a2 2 0 012-2h7a2 2 0 012 2v4a2 2 0 01-2 2H9l-3 3v-3H4a2 2 0 01-2-2V5z"}),(0,s.jsx)("path",{d:"M15 7v2a4 4 0 01-4 4H9.828l-1.766 1.767c.28.149.599.233.938.233h2l3 3v-3h2a2 2 0 002-2V9a2 2 0 00-2-2h-1z"})]}),(0,s.jsx)("span",{className:"relative z-10",children:"Get in touch"})]})}),(0,s.jsx)(l.A,{delay:.4,children:(0,s.jsx)("h1",{className:"text-5xl md:text-7xl font-manrope font-bold tracking-tighter leading-tight mb-6 text-white text-shadow-glow",children:(0,s.jsxs)("span",{className:"relative",children:[(0,s.jsx)("span",{className:"relative z-10 inline-block",children:d.contact.title}),(0,s.jsx)("span",{className:"absolute -inset-0.5 bg-gradient-to-r from-red-500 to-purple-600 blur-2xl opacity-20 rounded-full z-0"})]})})}),(0,s.jsx)(l.A,{delay:.6,children:(0,s.jsxs)("div",{className:"relative h-[3px] w-40 mx-auto my-8 overflow-hidden rounded-full",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-red-500 via-purple-600 to-blue-500"}),(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-500 via-red-500 to-purple-500 animate-gradient-x"}),(0,s.jsx)("div",{className:"absolute top-0 left-0 w-full h-full bg-gradient-to-r from-transparent via-white to-transparent -translate-x-full animate-shimmer"})]})}),(0,s.jsx)(l.A,{delay:.8,children:(0,s.jsx)("p",{className:"text-base md:text-xl font-sans leading-relaxed text-white/90 max-w-2xl mx-auto mb-10",children:d.contact.description})}),(0,s.jsx)(l.A,{delay:1,children:(0,s.jsxs)("div",{className:"flex flex-wrap justify-center gap-4 mt-8",children:[(0,s.jsxs)("button",{className:"px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white rounded-full shadow-lg shadow-purple-500/30 transition-all duration-300 mr-3 flex items-center group",children:[(0,s.jsx)("span",{className:"mr-2",children:d.contact.generalInquiry}),(0,s.jsx)("svg",{className:"w-5 h-5 transform group-hover:translate-x-1 transition-transform",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M13 7l5 5m0 0l-5 5m5-5H6"})})]}),(0,s.jsxs)("button",{className:"px-6 py-3 bg-gradient-to-r from-red-500 to-purple-600 hover:from-red-600 hover:to-purple-700 text-white rounded-full shadow-lg shadow-purple-500/30 transition-all duration-300 flex items-center group",children:[(0,s.jsx)("svg",{className:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,s.jsx)("span",{children:d.common.emergency})]})]})})]})})]}),(0,s.jsxs)("section",{className:"bg-white py-20 relative overflow-hidden",children:[(0,s.jsxs)("div",{className:"absolute inset-0 -z-10 opacity-60",children:[(0,s.jsx)("div",{className:"absolute -top-20 -left-20 w-64 h-64 bg-blue-50 rounded-full filter blur-3xl opacity-40"}),(0,s.jsx)("div",{className:"absolute -bottom-20 -right-20 w-64 h-64 bg-red-50 rounded-full filter blur-3xl opacity-40"}),(0,s.jsx)("div",{className:"absolute top-1/2 left-1/4 w-48 h-48 bg-purple-50 rounded-full filter blur-3xl opacity-30"}),(0,s.jsx)("div",{className:"absolute w-full h-full opacity-10",style:{backgroundImage:'url("/images/noise.png")',backgroundRepeat:"repeat"}})]}),(0,s.jsxs)("div",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsx)("div",{className:"max-w-3xl mx-auto",children:(0,s.jsx)("div",{className:"mb-14 relative z-10",children:(0,s.jsxs)("div",{className:"bg-white p-2 sm:p-2.5 rounded-2xl shadow-2xl border border-gray-100 flex flex-col sm:flex-row justify-center max-w-lg mx-auto overflow-hidden backdrop-blur-sm gap-2",children:[(0,s.jsxs)("button",{onClick:()=>u("general"),className:"\n                    flex-1 rounded-xl px-5 py-4 text-base font-medium transition-all duration-300 flex items-center justify-center gap-3\n                    ".concat("general"===m?"bg-gradient-to-r from-red-600 to-purple-700 text-white shadow-lg transform scale-[1.02]":"text-gray-700 hover:bg-gray-50 border border-gray-100 hover:border-gray-200","\n                  "),"aria-pressed":"general"===m,"aria-label":"Seleziona richiesta generale",children:[(0,s.jsx)("div",{className:"rounded-full p-1.5 bg-white/10 flex items-center justify-center",children:(0,s.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M8 12h.01M12 12h.01M16 12h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,s.jsx)("span",{className:"font-medium",children:d.contact.generalInquiry})]}),(0,s.jsxs)("button",{onClick:()=>u("emergency"),className:"\n                    flex-1 rounded-xl px-5 py-4 text-base font-medium transition-all duration-300 flex items-center justify-center gap-3\n                    ".concat("emergency"===m?"bg-gradient-to-r from-red-600 to-purple-700 text-white shadow-lg transform scale-[1.02]":"text-gray-700 hover:bg-gray-50 border border-gray-100 hover:border-gray-200","\n                  "),"aria-pressed":"emergency"===m,"aria-label":"Seleziona richiesta emergenza",children:[(0,s.jsx)("div",{className:"rounded-full p-1.5 bg-white/10 flex items-center justify-center",children:(0,s.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,s.jsx)("span",{className:"font-medium",children:d.contact.emergency})]})]})})}),(0,s.jsxs)("div",{className:"mt-12 relative",children:[(0,s.jsx)("div",{className:"absolute -top-6 -right-6 w-32 h-32 bg-red-50 rounded-full mix-blend-multiply filter blur-2xl opacity-70"}),(0,s.jsx)("div",{className:"absolute -bottom-6 -left-6 w-32 h-32 bg-blue-50 rounded-full mix-blend-multiply filter blur-2xl opacity-70"}),(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-red-50 via-white to-blue-50 opacity-40 -z-10 rounded-3xl transform -rotate-1 scale-105"}),(0,s.jsxs)("div",{className:"overflow-hidden rounded-3xl bg-white shadow-xl border border-gray-100 backdrop-blur-sm",children:[(0,s.jsx)("div",{className:"w-full h-2 bg-gradient-to-r from-red-500 to-red-600"}),(0,s.jsx)("div",{className:"px-6 py-10 sm:p-12",children:"emergency"===m?(0,s.jsx)(o.I,{t:d,lang:c}):(0,s.jsxs)("form",{className:"space-y-8",onSubmit:e=>e.preventDefault(),children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{className:"col-span-1 group",children:[(0,s.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-2 transition-colors group-focus-within:text-red-600",children:d.contact.form.name}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3.5 flex items-center pointer-events-none transition-colors group-focus-within:text-red-500",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-gray-400 group-focus-within:text-red-500 transition-colors",viewBox:"0 0 20 20",fill:"currentColor",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z",clipRule:"evenodd"})})}),(0,s.jsx)("input",{type:"text",name:"name",id:"name",className:"pl-10 block w-full rounded-xl border-gray-200 shadow-sm focus:border-red-500 focus:ring-red-500 sm:text-sm transition-all duration-300 py-3 bg-gray-50 focus:bg-white text-gray-800",placeholder:(null===(t=d.contact.form.placeholders)||void 0===t?void 0:t.name)||"",value:f.name,onChange:b})]})]}),(0,s.jsxs)("div",{className:"col-span-1 group",children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2 transition-colors group-focus-within:text-red-600",children:d.contact.form.email}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3.5 flex items-center pointer-events-none",children:(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-gray-400 group-focus-within:text-red-500 transition-colors",viewBox:"0 0 20 20",fill:"currentColor",children:[(0,s.jsx)("path",{d:"M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"}),(0,s.jsx)("path",{d:"M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"})]})}),(0,s.jsx)("input",{type:"email",name:"email",id:"email",className:"pl-10 block w-full rounded-xl border-gray-200 shadow-sm focus:border-red-500 focus:ring-red-500 sm:text-sm transition-all duration-300 py-3 bg-gray-50 focus:bg-white text-gray-800",placeholder:(null===(r=d.contact.form.placeholders)||void 0===r?void 0:r.email)||"",value:f.email,onChange:b,required:!0})]})]})]}),(0,s.jsxs)("div",{className:"group",children:[(0,s.jsx)("label",{htmlFor:"subject",className:"block text-sm font-medium text-gray-700 mb-2 transition-colors group-focus-within:text-red-600",children:d.contact.form.subject}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3.5 flex items-center pointer-events-none",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-gray-400 group-focus-within:text-red-500 transition-colors",viewBox:"0 0 20 20",fill:"currentColor",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z",clipRule:"evenodd"})})}),(0,s.jsx)("input",{type:"text",name:"subject",id:"subject",className:"pl-10 block w-full rounded-xl border-gray-200 shadow-sm focus:border-red-500 focus:ring-red-500 sm:text-sm transition-all duration-300 py-3 bg-gray-50 focus:bg-white text-gray-800",placeholder:(null===(i=d.contact.form.placeholders)||void 0===i?void 0:i.subject)||"",value:f.subject,onChange:b})]})]}),(0,s.jsxs)("div",{className:"group",children:[(0,s.jsx)("label",{htmlFor:"message",className:"block text-sm font-medium text-gray-700 mb-2 transition-colors group-focus-within:text-red-600",children:d.contact.form.message}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute top-3 left-3.5 flex items-start pointer-events-none",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-gray-400 group-focus-within:text-red-500 transition-colors",viewBox:"0 0 20 20",fill:"currentColor",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M18 5v8a2 2 0 01-2 2h-5l-5 4v-4H4a2 2 0 01-2-2V5a2 2 0 012-2h12a2 2 0 012 2zM7 8H5v2h2V8zm2 0h2v2H9V8zm6 0h-2v2h2V8z",clipRule:"evenodd"})})}),(0,s.jsx)("textarea",{id:"message",name:"message",rows:5,className:"pl-10 block w-full rounded-xl border-gray-200 shadow-sm focus:border-red-500 focus:ring-red-500 sm:text-sm transition-all duration-300 bg-gray-50 focus:bg-white text-gray-800",placeholder:(null===(n=d.contact.form.placeholders)||void 0===n?void 0:n.message)||"",value:f.message,onChange:b,required:!0})]})]}),(0,s.jsxs)("div",{className:"flex items-start p-4 bg-gray-50 rounded-xl border border-gray-100",children:[(0,s.jsx)("div",{className:"flex-shrink-0 mt-0.5",children:(0,s.jsxs)("div",{className:"relative flex items-center justify-center h-6 w-6",children:[(0,s.jsx)("input",{id:"privacy",name:"privacy",type:"checkbox",className:"h-5 w-5 text-red-600 focus:ring-red-500 focus:ring-offset-0 border-gray-300 rounded cursor-pointer",checked:f.privacy,onChange:e=>{let{name:t,checked:r}=e.target;v({...f,[t]:r})},required:!0}),(0,s.jsx)("svg",{className:"absolute h-6 w-6 text-red-500 opacity-0 peer-checked:opacity-100 transform scale-90 transition-all duration-200",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)("circle",{cx:"12",cy:"12",r:"12",fill:"currentColor",fillOpacity:"0.2"})})]})}),(0,s.jsxs)("div",{className:"ml-3",children:[(0,s.jsx)("label",{htmlFor:"privacy",className:"text-sm font-medium text-gray-700 cursor-pointer",children:d.contact.form.privacy}),(0,s.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:["it"===c?"I tuoi dati saranno trattati secondo la nostra ":"fr"===c?"Vos donn\xe9es seront trait\xe9es conform\xe9ment \xe0 notre ":"Ihre Daten werden gem\xe4\xdf unserer ",(0,s.jsx)("a",{href:"/".concat(c,"/privacy-policy"),className:"text-red-600 hover:text-red-800 underline transition-colors",children:"it"===c?"Informativa sulla Privacy":"fr"===c?"Politique de Confidentialit\xe9":"Datenschutzerkl\xe4rung"})]})]})]}),(0,s.jsx)("div",{className:"pt-4",children:(0,s.jsx)("button",{type:"submit",className:"w-full flex justify-center items-center rounded-xl bg-gradient-to-r from-red-500 to-purple-600 px-6 py-4 text-base font-semibold text-white shadow-lg hover:from-red-600 hover:to-purple-700 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-purple-600 transition-all duration-300 transform hover:-translate-y-1 hover:shadow-xl disabled:opacity-70 disabled:cursor-not-allowed",disabled:h,onClick:w,children:h?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,s.jsx)("span",{children:"Invio in corso..."})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("span",{children:d.contact.form.submit}),(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"ml-2 h-5 w-5 animate-pulse",viewBox:"0 0 20 20",fill:"currentColor",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z",clipRule:"evenodd"})})]})})}),p&&(0,s.jsx)("div",{className:"mt-4 p-4 rounded-lg ".concat(p.success?"bg-green-50 text-green-800":"bg-red-50 text-red-800"),children:(0,s.jsxs)("div",{className:"flex",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:p.success?(0,s.jsx)("svg",{className:"h-5 w-5 text-green-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}):(0,s.jsx)("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0z",clipRule:"evenodd"})})}),(0,s.jsx)("div",{className:"ml-3",children:(0,s.jsx)("p",{className:"text-sm font-medium",children:p.message})})]})})]})})]})]})]})]}),(0,s.jsxs)("section",{className:"relative overflow-hidden bg-gradient-to-br from-gray-50 to-white py-20 mt-10",children:[(0,s.jsx)("div",{className:"absolute -top-10 -right-10 w-40 h-40 bg-red-50 rounded-full filter blur-3xl opacity-60"}),(0,s.jsx)("div",{className:"absolute -bottom-10 -left-10 w-40 h-40 bg-blue-50 rounded-full filter blur-3xl opacity-60"}),(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsxs)("div",{className:"text-center mb-14",children:[(0,s.jsx)("h2",{className:"text-3xl font-bold text-gray-900 sm:text-4xl mb-4",children:"Come raggiungerci"}),(0,s.jsx)("div",{className:"h-1 w-16 mx-auto bg-gradient-to-r from-red-600 to-purple-700 rounded-full my-4"}),(0,s.jsx)("p",{className:"text-lg text-gray-600 max-w-2xl mx-auto",children:"Siamo sempre disponibili per aiutarti. Scegli il metodo che preferisci per contattarci."})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8 xl:gap-12",children:[(0,s.jsxs)("div",{className:"group bg-white p-8 rounded-2xl shadow-lg border border-gray-100 flex flex-col items-center text-center transform transition-all duration-500 hover:shadow-xl hover:-translate-y-2 relative overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute top-0 inset-x-0 h-2 bg-gradient-to-r from-red-600 to-purple-700 transform transition-transform duration-300 group-hover:scale-x-100"}),(0,s.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-red-600 to-purple-700 rounded-2xl flex items-center justify-center mb-6 transform rotate-3 group-hover:rotate-6 transition-all duration-300 shadow-md",children:(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-8 w-8 text-white",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:[(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})]})}),(0,s.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-3",children:"La nostra sede"}),(0,s.jsxs)("p",{className:"text-gray-600 mb-4",children:["Inparo Gmbh",(0,s.jsx)("br",{}),"Gubelstrasse 15",(0,s.jsx)("br",{}),"6300 Zug, Svizzera"]}),(0,s.jsxs)("a",{href:"https://maps.google.com",target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center text-sm font-medium text-red-600 hover:text-purple-700 transition-colors",children:[(0,s.jsx)("span",{children:"Vedi su Google Maps"}),(0,s.jsx)("svg",{className:"ml-1 w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z",clipRule:"evenodd"})})]})]}),(0,s.jsxs)("div",{className:"group bg-white p-8 rounded-2xl shadow-lg border border-gray-100 flex flex-col items-center text-center transform transition-all duration-500 hover:shadow-xl hover:-translate-y-2 relative overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute top-0 inset-x-0 h-2 bg-gradient-to-r from-red-600 to-purple-700 transform transition-transform duration-300 group-hover:scale-x-100"}),(0,s.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-red-600 to-purple-700 rounded-2xl flex items-center justify-center mb-6 transform -rotate-3 group-hover:rotate-0 transition-all duration-300 shadow-md",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-8 w-8 text-white",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2v-1.586a1 1 0 01.293-.707l7.586-7.586a3 3 0 000-4.243a1 1 0 00-.242-1.79l-2.93-2.93a21.994 21.994 0 01-6.379 1.123 2 2 0 01-2.07-.689A1 1 0 0013 7h-1a1 1 0 00-2 0v1a1 1 0 00-1 1v2.586a1 1 0 01-.293.707z"})})}),(0,s.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-3",children:"Email"}),(0,s.jsx)("p",{className:"text-gray-600 mb-4",children:"<EMAIL>"}),(0,s.jsxs)("a",{href:"mailto:<EMAIL>",className:"inline-flex items-center text-sm font-medium text-red-600 hover:text-purple-700 transition-colors",children:[(0,s.jsx)("span",{children:"Invia un'email"}),(0,s.jsx)("svg",{className:"ml-1 w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z",clipRule:"evenodd"})})]})]}),(0,s.jsxs)("div",{className:"group bg-white p-8 rounded-2xl shadow-lg border border-gray-100 flex flex-col items-center text-center transform transition-all duration-500 hover:shadow-xl hover:-translate-y-2 relative overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute top-0 inset-x-0 h-2 bg-gradient-to-r from-red-600 to-purple-700 transform transition-transform duration-300 group-hover:scale-x-100"}),(0,s.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-red-600 to-purple-700 rounded-2xl flex items-center justify-center mb-6 transform rotate-3 group-hover:-rotate-6 transition-all duration-300 shadow-md",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-8 w-8 text-white",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"})})}),(0,s.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-3",children:"Telefono"}),(0,s.jsxs)("p",{className:"text-gray-600 mb-4",children:["+41 76 466 2122",(0,s.jsx)("br",{}),"Lun-Ven: 9:00-18:00"]}),(0,s.jsxs)("a",{href:"tel:+41764662122",className:"inline-flex items-center text-sm font-medium text-red-600 hover:text-purple-700 transition-colors",children:[(0,s.jsx)("span",{children:"Chiama ora"}),(0,s.jsx)("svg",{className:"ml-1 w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z",clipRule:"evenodd"})})]}),(0,s.jsxs)("a",{href:"https://wa.me/41764662122",target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center text-sm font-medium text-green-600 hover:text-green-700 transition-colors",children:[(0,s.jsx)("span",{children:"WhatsApp"}),(0,s.jsx)("svg",{className:"ml-1 w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z",clipRule:"evenodd"})})]})]})]})]}),(0,s.jsx)("div",{className:"absolute left-0 right-0 bottom-0 h-8 overflow-hidden",children:(0,s.jsx)("svg",{className:"w-full h-full",viewBox:"0 0 1200 120",preserveAspectRatio:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)("path",{d:"M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V120H0V95.72C57.71,118.68,121.41,111.23,165.53,101.1Z",fill:"#ffffff"})})})]})]})}},4889:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var s=r(5155),a=r(3478);function l(e){let{children:t,className:r="",delay:l=0}=e;return(0,s.jsx)(a.P.div,{className:r,initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:l},children:t})}},5683:(e,t,r)=>{"use strict";r.d(t,{N:()=>f});var s=r(5155),a=r(2115),l=r(4710),o=r(9234),i=r(9656),n=r(7249);class d extends a.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,r=e instanceof HTMLElement&&e.offsetWidth||0,s=this.props.sizeRef.current;s.height=t.offsetHeight||0,s.width=t.offsetWidth||0,s.top=t.offsetTop,s.left=t.offsetLeft,s.right=r-s.width-s.left}return null}componentDidUpdate(){}render(){return this.props.children}}function c(e){let{children:t,isPresent:r,anchorX:l}=e,o=(0,a.useId)(),i=(0,a.useRef)(null),c=(0,a.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:m}=(0,a.useContext)(n.Q);return(0,a.useInsertionEffect)(()=>{let{width:e,height:t,top:s,left:a,right:n}=c.current;if(r||!i.current||!e||!t)return;i.current.dataset.motionPopId=o;let d=document.createElement("style");return m&&(d.nonce=m),document.head.appendChild(d),d.sheet&&d.sheet.insertRule('\n          [data-motion-pop-id="'.concat(o,'"] {\n            position: absolute !important;\n            width: ').concat(e,"px !important;\n            height: ").concat(t,"px !important;\n            ").concat("left"===l?"left: ".concat(a):"right: ".concat(n),"px !important;\n            top: ").concat(s,"px !important;\n          }\n        ")),()=>{document.head.removeChild(d)}},[r]),(0,s.jsx)(d,{isPresent:r,childRef:i,sizeRef:c,children:a.cloneElement(t,{ref:i})})}let m=e=>{let{children:t,initial:r,isPresent:l,onExitComplete:n,custom:d,presenceAffectsLayout:m,mode:h,anchorX:x}=e,p=(0,o.M)(u),g=(0,a.useId)(),f=(0,a.useCallback)(e=>{for(let t of(p.set(e,!0),p.values()))if(!t)return;n&&n()},[p,n]),v=(0,a.useMemo)(()=>({id:g,initial:r,isPresent:l,custom:d,onExitComplete:f,register:e=>(p.set(e,!1),()=>p.delete(e))}),m?[Math.random(),f]:[l,f]);return(0,a.useMemo)(()=>{p.forEach((e,t)=>p.set(t,!1))},[l]),a.useEffect(()=>{l||p.size||!n||n()},[l]),"popLayout"===h&&(t=(0,s.jsx)(c,{isPresent:l,anchorX:x,children:t})),(0,s.jsx)(i.t.Provider,{value:v,children:t})};function u(){return new Map}var h=r(5087);let x=e=>e.key||"";function p(e){let t=[];return a.Children.forEach(e,e=>{(0,a.isValidElement)(e)&&t.push(e)}),t}var g=r(5403);let f=e=>{let{children:t,custom:r,initial:i=!0,onExitComplete:n,presenceAffectsLayout:d=!0,mode:c="sync",propagate:u=!1,anchorX:f="left"}=e,[v,b]=(0,h.xQ)(u),w=(0,a.useMemo)(()=>p(t),[t]),j=u&&!v?[]:w.map(x),y=(0,a.useRef)(!0),N=(0,a.useRef)(w),k=(0,o.M)(()=>new Map),[z,C]=(0,a.useState)(w),[M,R]=(0,a.useState)(w);(0,g.E)(()=>{y.current=!1,N.current=w;for(let e=0;e<M.length;e++){let t=x(M[e]);j.includes(t)?k.delete(t):!0!==k.get(t)&&k.set(t,!1)}},[M,j.length,j.join("-")]);let L=[];if(w!==z){let e=[...w];for(let t=0;t<M.length;t++){let r=M[t],s=x(r);j.includes(s)||(e.splice(t,0,r),L.push(r))}return"wait"===c&&L.length&&(e=L),R(p(e)),C(w),null}let{forceRender:B}=(0,a.useContext)(l.L);return(0,s.jsx)(s.Fragment,{children:M.map(e=>{let t=x(e),a=(!u||!!v)&&(w===M||j.includes(t));return(0,s.jsx)(m,{isPresent:a,initial:(!y.current||!!i)&&void 0,custom:r,presenceAffectsLayout:d,mode:c,onExitComplete:a?void 0:()=>{if(!k.has(t))return;k.set(t,!0);let e=!0;k.forEach(t=>{t||(e=!1)}),e&&(null==B||B(),R(N.current),u&&(null==b||b()),n&&n())},anchorX:f,children:e},t)})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[478,468,441,517,358],()=>t(2204)),_N_E=e.O()}]);