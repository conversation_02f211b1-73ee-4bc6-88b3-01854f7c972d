import { NextConfig } from 'next'

/**
 * Configurazione di Next.js con ottimizzazione delle immagini
 * Limitiamo la dimensione massima delle immagini a 1000px di larghezza
 * e ottimizziamo automaticamente per migliorare le performance
 */
const config: NextConfig = {
  images: {
    domains: ['localhost'],
    deviceSizes: [640, 750, 828, 1000],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384, 512, 768, 1000],
    remotePatterns: [
      {
        protocol: 'http',
        hostname: 'localhost',
        port: '',
        pathname: '/**',
      },
    ],
    unoptimized: true  // Consente di utilizzare direttamente le immagini ottimizzate manualmente
  },
  // Configurazione per l'esportazione statica
  output: 'export',
  // Disabilita il server runtime di Next.js per generazione completamente statica
  trailingSlash: true, // Aggiunge slash finale agli URL per compatibilità con hosting PHP
  
  // Disabilitare ESLint durante la build
  eslint: {
    ignoreDuringBuilds: true,
  },
  
  // Disabilitare TypeScript checking durante la build
  typescript: {
    ignoreBuildErrors: true,
  },
}

export default config
