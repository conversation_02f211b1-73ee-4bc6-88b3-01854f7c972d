# Abilita il modulo rewrite
RewriteEngine On

# Regole di sicurezza di base
# Impedisce l'elencazione delle directory
Options -Indexes

# Protegge i file .htaccess e .htpasswd
<Files ~ "^\.ht">
Order deny,allow
Deny from all
</Files>

# Imposta il charset predefinito
AddDefaultCharset UTF-8

# Forza HTTPS (decommenta se disponibile HTTPS sul hosting)
# RewriteCond %{HTTPS} !=on
# RewriteRule ^ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Imposta la compressione per i file più comuni
<IfModule mod_deflate.c>
AddOutputFilterByType DEFLATE text/html text/plain text/xml text/css application/javascript application/json image/svg+xml
</IfModule>

# Imposta la cache per i file statici
<IfModule mod_expires.c>
ExpiresActive On
ExpiresByType image/jpg "access plus 1 year"
ExpiresByType image/jpeg "access plus 1 year"
ExpiresByType image/gif "access plus 1 year"
ExpiresByType image/png "access plus 1 year"
ExpiresByType image/svg+xml "access plus 1 month"
ExpiresByType text/css "access plus 1 month"
ExpiresByType application/javascript "access plus 1 month"
ExpiresByType application/x-javascript "access plus 1 month"
</IfModule>

# Rewrite per API - consenti l'accesso diretto ai file PHP nella cartella /api/
RewriteRule ^api/ - [L]

# Per le risorse statiche, passa direttamente ai file
RewriteCond %{REQUEST_URI} \.(jpg|jpeg|gif|png|ico|css|js|svg)$ [NC]
RewriteRule .* - [L]

# Per tutto il resto, indirizza a index.php
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [L,QSA]
