export interface ServiceData {
  hero: {
    title: string;
    description: string;
    subtitle: string;
  };
  categories: Array<{
    id: string;
    title: string;
    description: string;
    services: Array<{
      id: string;
      title: string;
      description: string;
      features: string[];
    }>;
  }>;
  cta: {
    title: string;
    description: string;
    button: string;
    url?: string;
  };
}

export interface Translations {
  metadata: {
    title: string;
    description: string;
    ogImageAlt: string;
  };
  common: {
    contact: string;
    emergency: string;
    callNow: string;
    getQuote: string;
    learnMore: string;
    viewProjects?: string;
  };

  nav: {
    home: string;
    services: string;
    emergency: string;
    contact: string;
    renovation?: string;
  };
  navigation: {
    home: string;
    services: string;
    renovation: string;
    emergency: string;
    contact: string;
  };
  home: {
    hero: {
      title: string;
      titleHighlight: string;
      subtitle: string;
    };
    whyChooseUs: {
      title: string;
      subtitle: string;
      experience: {
        title: string;
        description: string;
      };
      quality: {
        title: string;
        description: string;
      };
      support: {
        title: string;
        description: string;
      };
    };
    services: {
      title: string;
      subtitle: string;
      seeAll: string;
      renovation: {
        title: string;
        description: string;
      };
      emergency?: {
        title: string;
        description: string;
      };
      technical?: {
        title: string;
        description: string;
      };
      cleaning?: {
        title: string;
        description: string;
      };
      consulting: {
        title: string;
        description: string;
      };
    };
  };
  services: {
    hero: {
      title: string;
      subtitle: string;
    };
    main: {
      title: string;
      subtitle: string;
      description: string;
    };
    features?: string | string[];
    featuresTitle?: string;
    infoTitle?: string;
    types: {
      renovation: {
        title: string;
        description: string;
        features: string[] | { title: string; list: string[]; };
      };
      emergency?: {
        title: string;
        description: string;
        features: string[];
      };
      restoration: {
        title: string;
        description: string;
        features: string[];
      };
      consulting: {
        title: string;
        description: string;
        features: string[];
      };
      maintenance: {
        title: string;
        description: string;
        features: string[];
      };
    };
  };
  contact: {
    title: string;
    description: string;
    generalInquiry: string;
    emergency: string;
    form: {
      name: string;
      phone: string;
      email: string;
      address: string;
      message: string;
      emergency_type: string;
      privacy: string;
      submit: string;
      success: string;
      error: string;
      subject: string;
      placeholders?: {
        name: string;
        email: string;
        phone: string;
        address: string;
        message: string;
        subject: string;
      };
    };
  };
  footer: {
    copyright: string;
    emergencyDescription: string;
    terms?: string;
  };
  emergency?: {
    hero?: {
      title: string;
      subtitle: string;
      description: string;
    };
    services?: {
      title: string;
      items?: Array<{
        id: number;
        title: string;
        description: string;
        response_time: string;
        icon: string;
      }>
    };
    process?: Array<{
      step: number;
      title: string;
      description: string;
    }>;
    cta?: {
      emergency_number: string;
      whatsapp_number: string;
    };
    contact_form?: {
      title: string;
      description: string;
      fields: {
        name: string;
        phone: string;
        address: string;
        emergency_type: string;
        message: string;
        privacy: string;
      };
      submit: string;
      success: string;
      error: string;
      emergency_types: string[];
    };
    equipment: {
      title: string;
      subtitle: string;
      pumps: {
        title: string;
        description: string;
        example: string;
        features: string[];
      };
      dryers: {
        title: string;
        description: string;
        example: string;
        details: string;
      };
      dyes: {
        title: string;
        description: string;
        advantages: string;
        features: string[];
      };
      strategy: {
        title: string;
        description: string;
      };
    };
  };
}
