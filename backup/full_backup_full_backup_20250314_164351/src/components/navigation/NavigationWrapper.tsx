import { getTranslation } from '@/i18n/server';
import { Translations } from '@/types/translations';
import Navigation from './Navigation';

interface NavigationWrapperProps {
  lang: string;
}

export default async function NavigationWrapper({ lang }: NavigationWrapperProps) {
  const translations = await getTranslation(lang) as Translations;
  return <Navigation lang={lang} translations={translations} />;
}
