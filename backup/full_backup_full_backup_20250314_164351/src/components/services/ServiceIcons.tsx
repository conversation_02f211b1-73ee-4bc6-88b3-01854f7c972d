'use client';

import React from 'react';
import { FaTools, FaPaintRoller, FaHome, FaBroom, FaLeaf, FaShower, FaWrench } from 'react-icons/fa';

// Componente generico per l'icona del servizio
export const ServiceIcon = ({ iconName, size = 24 }: { iconName: string; size?: number }) => {
  switch (iconName) {
    case 'FaTools':
      return <FaTools size={size} />;
    case 'FaPaintRoller':
      return <FaPaintRoller size={size} />;
    case 'FaHome':
      return <FaHome size={size} />;
    case 'FaBroom':
      return <FaBroom size={size} />;
    case 'FaLeaf':
      return <FaLeaf size={size} />;
    case 'FaShower':
      return <FaShower size={size} />;
    default:
      return <FaWrench size={size} />;
  }
};

// Tipi per i nomi delle icone supportate
export type ServiceIconName = 'FaTools' | 'FaPaintRoller' | 'FaHome' | 'FaBroom' | 'FaLeaf' | 'FaShower' | 'default';
