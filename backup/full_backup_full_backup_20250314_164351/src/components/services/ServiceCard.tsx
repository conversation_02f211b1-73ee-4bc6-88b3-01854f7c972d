'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { FaCheck, FaArrowRight, FaClock, FaShieldAlt } from 'react-icons/fa';
import { ServiceIcon, type ServiceIconName } from './ServiceIcons';

// Funzione per tradurre i nomi dei servizi in italiano
const translateServiceName = (id: string, lang: string): string => {
  // Se la lingua non è italiana o tedesca, ritorna il nome originale formattato
  if (lang !== 'it' && lang !== 'de' && lang !== 'fr') {
    return id.split('-').join(' ');
  }
  
  // Mappatura dei nomi dei servizi
  const serviceNameMap: Record<string, Record<string, string>> = {
    it: {
      'complete-renovation': 'Ristrutturazione Completa',
      'interior-design': 'Interior Design',
      'bathroom': 'Bagno',
      'bathroom-renovation': 'Ristrutturazione Bagno',
      'kitchen': 'Ristrutturazione Cucina',
      'roofing': 'Rifacimento Tetti',
      'facade': 'Energia Solare',
      'electrical': 'Impianti Elettrici',
      'plumbing': 'Impianti Idraulici',
      'flooring': 'Pavimenti',
      'painting': 'Pitture e Decorazioni',
      'interior-renovation': 'Ristrutturazione Interni',
      'house-painter': 'Tinteggiatura',
      'tiler': 'Piastrellista',
      'wooden-doors': 'Porte in Legno',
      'carpentry': 'Infissi su misura',
      'wall': 'Muratura',
      'roof': 'Tetti',
      'deep-cleaning': 'Pulizia Profonda',
      'regular-cleaning': 'Pulizia Regolare',
      'garden-maintenance': 'Manutenzione Giardino',
      'specialized-services': 'Servizi Specializzati'
    },
    de: {
      'complete-renovation': 'Komplettsanierung',
      'interior-design': 'Innenarchitektur',
      'bathroom': 'Badezimmer',
      'bathroom-renovation': 'Badrenovierung',
      'kitchen': 'Küchenrenovierung',
      'roofing': 'Dachsanierung',
      'facade': 'Solarenergie',
      'electrical': 'Elektroanlagen',
      'plumbing': 'Sanitäranlagen',
      'flooring': 'Bodenbeläge',
      'painting': 'Gipser & Malerarbeiten',
      'interior-renovation': 'Innenrenovierung',
      'house-painter': 'Anstricharbeiten',
      'tiler': 'Fliesenleger',
      'wooden-doors': 'Holztüren',
      'carpentry': 'Schreinerarbeiten',
      'wall': 'Maurerarbeiten',
      'roof': 'Dach',
      'deep-cleaning': 'Grundreinigung',
      'regular-cleaning': 'Regelmäßige Reinigung',
      'garden-maintenance': 'Gartenpflege',
      'specialized-services': 'Spezialdienste'
    },
    fr: {
      'complete-renovation': 'Rénovation Complète',
      'interior-design': 'Design d\'Intérieur',
      'bathroom': 'Salle de Bain',
      'bathroom-renovation': 'Rénovation de Salle de Bain',
      'kitchen': 'Rénovation de Cuisine',
      'roofing': 'Réfection de Toiture',
      'facade': 'Énergie Solaire',
      'electrical': 'Installations Électriques',
      'plumbing': 'Installations Sanitaires',
      'flooring': 'Revêtements de Sol',
      'painting': 'Peinture et Décoration',
      'interior-renovation': 'Rénovation Intérieure',
      'house-painter': 'Peinture',
      'tiler': 'Carreleur',
      'wooden-doors': 'Portes en Bois',
      'carpentry': 'Menuiserie sur Mesure',
      'wall': 'Maçonnerie',
      'roof': 'Toiture',
      'deep-cleaning': 'Nettoyage en Profondeur',
      'regular-cleaning': 'Nettoyage Régulier',
      'garden-maintenance': 'Entretien de Jardin',
      'specialized-services': 'Services Spécialisés'
    }
  };
  
  // Restituisce il nome tradotto o il nome originale formattato se non presente nella mappa
  return serviceNameMap[lang][id] || id.split('-').join(' ');
};

// Funzione per ottenere l'immagine corrispondente al servizio
const getServiceImage = (id: string): string => {
  // Mappa completa di tutti gli ID dei servizi alle rispettive immagini
  const serviceImages: Record<string, string> = {
    // Immagini JPG specifiche
    'bathroom-renovation': '/images/services/bathroom.jpg',
    'interior-renovation': '/images/services/renovation.jpg',
    'complete-renovation': '/images/services/renovation.jpg',
    'house-painter': '/images/services/house-painter.jpg',
    'tiler': '/images/services/tiler.jpg',
    'wooden-doors': '/images/services/wooden-doors.jpg',
    'electrical': '/images/services/electrical.jpg',
    'wall': '/images/services/wall.jpg',
    'roof': '/images/services/roof.jpg',
    'roofing': '/images/services/roof.jpg',             // Confermo immagine per rifacimento tetti
    'interior-design': '/images/services/interior-design.jpg',
    
    // Correzioni specifiche per la terza e quarta card
    'bathroom': '/images/services/bathroom.jpg',          // Terza card corretta
    'kitchen': '/images/services/interior-design.jpg',    // Quarta card - rinominata correttamente
    'painting': '/images/services/house-painter.jpg',     // Card a sinistra - usa l'immagine dell'imbianchino
    'carpentry': '/images/services/wooden-doors.jpg',      // Card a destra - usa l'immagine delle porte in legno
    
    // Mappature per servizi senza immagini dedicate
    'facade': '/images/services/pannelli_resized.jpg',                // Impianti fotovoltaici
    'plumbing': '/images/services/bathroom.jpg',          // Usiamo bathroom per idraulica
    'flooring': '/images/services/tiler.jpg',             // Usiamo tiler per pavimenti
    
    // Immagini SVG per servizi di pulizia e manutenzione
    'deep-cleaning': '/images/services/deep-cleaning.svg',
    'regular-cleaning': '/images/services/regular-cleaning.svg',
    'garden-maintenance': '/images/services/garden-maintenance.svg',
    'specialized-services': '/images/services/specialized-services.svg',
    'interior': '/images/services/interior-renovation.svg',
    'complete': '/images/services/complete-renovation.svg'
  };
  
  // Cerca di restituire l'immagine basata sull'ID esatto
  if (serviceImages[id]) {
    return serviceImages[id];
  }
  
  // Se non trova un'immagine diretta, cerca per parole chiave nell'ID
  for (const key in serviceImages) {
    if (id.includes(key) || key.includes(id)) {
      return serviceImages[key];
    }
  }
  
  // Categorie generiche in base alla tipologia di servizio
  if (id.includes('clean') || id.includes('pulizia')) {
    return '/images/services/deep-cleaning.svg';
  } else if (id.includes('garden') || id.includes('giardino')) {
    return '/images/services/garden-maintenance.svg';
  } else if (id.includes('bath') || id.includes('bagno')) {
    return '/images/services/bathroom.jpg';
  } else if (id.includes('paint') || id.includes('pittura')) {
    return '/images/services/house-painter.jpg';
  } else if (id.includes('floor') || id.includes('paviment')) {
    return '/images/services/tiler.jpg';
  } else if (id.includes('electric') || id.includes('elettric')) {
    return '/images/services/electrical.jpg';
  } else if (id.includes('roof') || id.includes('tetto')) {
    return '/images/services/roof.jpg';
  } else if (id.includes('renovat') || id.includes('ristruttura')) {
    return '/images/services/renovation.jpg';
  }
  
  // Restituisce l'immagine di ristrutturazione come default per un aspetto più professionale
  return '/images/services/renovation.jpg';
}

// Funzione per tradurre il testo dei pulsanti in base alla lingua
const translateButtonText = (lang: string): string => {
  const buttonTextMap: Record<string, string> = {
    'it': 'Richiedi preventivo',
    'de': 'Angebot anfordern',
    'en': 'Request a quote',
    'fr': 'Demander un devis'
  };
  
  return buttonTextMap[lang] || 'Richiedi preventivo';
};

interface ServiceCardProps {
  id: string;
  title: string;
  description: string;
  features: string[];
  icon: ServiceIconName;
  image?: string;
  duration?: string;
  warranty?: string;
  lang: string;
  delay?: number;
}

export default function ServiceCard({
  id,
  title,
  description,
  features,
  icon,
  image,
  duration,
  warranty,
  lang,
  delay = 0
}: ServiceCardProps) {
  // L'icona sarà renderizzata direttamente dal componente ServiceIcon
  // Standardizziamo a 4 feature per ogni card per uniformità grafica
  const standardFeatureCount = 4;
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay }}
      className="group relative overflow-hidden rounded-lg bg-gradient-to-br from-gray-50 to-white border border-gray-100 shadow-sm transition-all duration-300 hover:shadow-md hover:-translate-y-1 hover:border-inparoblue-100 h-[580px] flex flex-col"
    >
      
      {/* Header con immagine di sfondo - ingrandita e senza icona centrale */}
      <div className="relative w-full h-96 overflow-hidden">
        {/* Immagine di sfondo ottimizzata */}
        <div className="absolute inset-0">
          <Image 
            src={getServiceImage(id)}
            alt={title}
            fill
            className="object-cover"
            sizes="(max-width: 768px) 100vw, 33vw"
            priority
          />
          {/* Overlay graduale in basso per migliorare leggibilità del titolo */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent"></div>
        </div>
        
        {/* Tag/Badge nell'angolo tradotto in italiano */}
        <div className="absolute top-6 right-6 bg-white px-3 py-1.5 rounded-full shadow-md text-sm font-medium text-inparoblue-600 border border-inparoblue-100 z-10 transition-all group-hover:shadow-lg group-hover:border-inparoblue-200">
          {translateServiceName(id, lang)}
        </div>
      </div>
      
      <div className="flex flex-1 flex-col p-5 h-full justify-between">
        {/* Separatore decorativo */}
        <div className="mb-3">
          <div className="h-1 w-16 bg-gradient-to-r from-inparoblue-500 to-inparoblue-700 rounded-full"></div>
        </div>
        
        {/* Description */}
        <p className="text-sm text-gray-600 mb-5">{description}</p>
        
        {/* Features - con numero standardizzato */}
        <div className="space-y-3 min-h-[140px]">
          {/* Mostriamo solo il numero standardizzato di feature o aggiungiamo placeholder */}
          {Array.from({ length: standardFeatureCount }).map((_, index) => (
            <div key={index} className="flex items-start">
              <div className="flex-shrink-0 h-5 w-5 rounded-full bg-gradient-to-r from-inparoblue-100 to-inparoblue-200 flex items-center justify-center">
                <FaCheck className="h-2.5 w-2.5 text-inparoblue-600" />
              </div>
              <span className="ml-2 text-sm text-gray-700">
                {index < features.length ? features[index] : ""} 
              </span>
            </div>
          ))}
        </div>

        {/* Call to Action */}
        <div className="pt-5">
          <Link
            href={`/${lang}/contact?service=${id}`}
            className="w-full flex items-center justify-center rounded-md bg-gradient-to-r from-inparoblue-600 to-inparoblue-700 px-4 py-2.5 text-sm font-medium text-white shadow-sm hover:from-inparoblue-500 hover:to-inparoblue-600 transition-colors"
          >
            {translateButtonText(lang)}
          </Link>
        </div>
      </div>
    </motion.div>
  );
}
