'use client';

import { useLanguage } from '@/app/providers';
import { usePathname } from 'next/navigation';
import { motion } from 'framer-motion';

const languages = [
  { code: 'it', name: 'Italiano', flag: '🇮🇹' },
  { code: 'de', name: '<PERSON><PERSON><PERSON>', flag: '🇨🇭' },
  { code: 'fr', name: 'Français', flag: '🇫🇷' },
] as const;

interface LanguageSelectorProps {
  currentLang: string;
}

export default function LanguageSelector({ currentLang }: LanguageSelectorProps) {
  const pathname = usePathname();
  const { setLocale } = useLanguage();
  const segments = pathname?.split('/') || [];
  const newPathname = segments.length > 2 ? segments.slice(2).join('/') : '';

  // Varianti di animazione per i componenti
  const buttonVariants = {
    active: { scale: 1.05 },
    inactive: { scale: 1 }
  };

  const textVariants = {
    active: { fontWeight: 600 },
    inactive: { fontWeight: 400 }
  };

  return (
    <div className="flex items-center space-x-2 bg-white/80 backdrop-blur-sm rounded-full p-1 shadow-lg">
      {languages.map((lang) => {
        const isActive = currentLang === lang.code;
        
        return (
          <motion.button
            key={lang.code}
            onClick={() => setLocale(lang.code)}
            className="relative px-4 py-2 text-sm font-medium rounded-full"
            variants={buttonVariants}
            animate={isActive ? 'active' : 'inactive'}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            transition={{ type: "spring", stiffness: 500, damping: 30 }}
          >
            <motion.span 
              className="flex items-center space-x-2"
              variants={textVariants}
              animate={isActive ? 'active' : 'inactive'}
              transition={{ duration: 0.3 }}
            >
              <span className="text-base">{lang.flag}</span>
              <motion.span
                animate={{ color: isActive ? '#2563eb' : '#4b5563' }}
                transition={{ duration: 0.3 }}
              >
                {lang.name}
              </motion.span>
            </motion.span>
            
            {/* Background animato */}
            <motion.div
              layoutId="activeLocale"
              className="absolute inset-0 rounded-full -z-10"
              style={{ backgroundColor: isActive ? 'rgba(219, 234, 254, 0.8)' : 'transparent' }}
              initial={false}
              animate={{ backgroundColor: isActive ? 'rgba(219, 234, 254, 0.8)' : 'transparent' }}
              transition={{ 
                type: "spring", 
                stiffness: 500, 
                damping: 30, 
                mass: 1 
              }}
            />
          </motion.button>
        );
      })}
    </div>
  );
}
