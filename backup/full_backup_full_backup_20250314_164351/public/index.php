<?php
// Configurazioni PHP
ini_set('display_errors', 0); // Disabilita la visualizzazione degli errori in produzione

// Funzione per trovare il percorso corretto per i file statici
function getStaticFilePath($uri) {
    // Rimuove query string
    $uri = strtok($uri, '?');
    
    // Gestisce la radice
    if ($uri === '/' || $uri === '') {
        return './index.html';
    }
    
    // Rimuove la / iniziale se presente
    $uri = ltrim($uri, '/');
    
    // Verifica se il percorso termina con una barra
    if (substr($uri, -1) === '/') {
        // Cerca index.html nella directory
        $path = $uri . 'index.html';
    } else {
        // Controlla se è una directory senza barra finale
        if (is_dir($uri)) {
            // Reindirizza con barra finale
            header('Location: /' . $uri . '/');
            exit;
        }
        $path = $uri;
    }
    
    // Se il file esiste, lo restituisce
    if (file_exists($path)) {
        return $path;
    }
    
    // Se non esiste, prova ad aggiungere .html
    if (file_exists($path . '.html')) {
        return $path . '.html';
    }
    
    // Gestione 404
    header('HTTP/1.0 404 Not Found');
    if (file_exists('404.html')) {
        return '404.html';
    }
    
    // Fallback se non esiste 404.html
    echo '<h1>404 - Pagina non trovata</h1>';
    exit;
}

// Ottieni l'URI richiesto
$requestUri = $_SERVER['REQUEST_URI'];

// Se è una richiesta API, lascia che il router PHP gestisca
if (strpos($requestUri, '/api/') === 0) {
    // Le richieste API sono già gestite dai file PHP nella cartella /api/
    return false;
}

// Ottieni il percorso del file statico
$filePath = getStaticFilePath($requestUri);

// Imposta il content type in base all'estensione del file
$extension = pathinfo($filePath, PATHINFO_EXTENSION);
switch ($extension) {
    case 'html':
        header('Content-Type: text/html; charset=utf-8');
        break;
    case 'css':
        header('Content-Type: text/css; charset=utf-8');
        break;
    case 'js':
        header('Content-Type: application/javascript; charset=utf-8');
        break;
    case 'json':
        header('Content-Type: application/json; charset=utf-8');
        break;
    case 'svg':
        header('Content-Type: image/svg+xml');
        break;
    case 'png':
        header('Content-Type: image/png');
        break;
    case 'jpg':
    case 'jpeg':
        header('Content-Type: image/jpeg');
        break;
    case 'gif':
        header('Content-Type: image/gif');
        break;
    // Aggiungi altri tipi MIME se necessario
}

// Imposta la cache per i file statici (1 giorno)
if (in_array($extension, ['css', 'js', 'png', 'jpg', 'jpeg', 'gif', 'svg'])) {
    header('Cache-Control: public, max-age=86400');
}

// Servi il file
readfile($filePath);
