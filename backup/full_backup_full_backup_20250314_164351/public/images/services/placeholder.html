<!DOCTYPE html>
<html>
<head>
  <title>Placeholder Generator</title>
  <style>
    body {
      font-family: 'Arial', sans-serif;
      margin: 0;
      padding: 20px;
      text-align: center;
    }
    canvas {
      margin: 20px;
      box-shadow: 0 0 10px rgba(0,0,0,0.1);
    }
    .controls {
      margin-bottom: 20px;
    }
    button {
      background-color: #e63946;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      cursor: pointer;
      margin: 5px;
    }
    select, input {
      padding: 10px;
      margin: 5px;
      border-radius: 4px;
      border: 1px solid #ddd;
    }
  </style>
</head>
<body>
  <h1>Generatore di immagini placeholder per servizi</h1>
  
  <div class="controls">
    <select id="serviceSelector">
      <option value="complete-renovation">Ristrutturazione Completa</option>
      <option value="interior-renovation">Rinnovo Interni</option>
      <option value="specialized-services">Servizi Specializzati</option>
      <option value="regular-cleaning">P<PERSON><PERSON></option>
      <option value="deep-cleaning">P<PERSON>zie Approfondite</option>
      <option value="garden-maintenance">Manutenzione Giardini</option>
      <option value="bathroom-renovation">Ristrutturazione Bagno</option>
      <option value="default">Default</option>
    </select>
    <button id="generateBtn">Genera Immagine</button>
    <button id="downloadBtn">Scarica JPG</button>
  </div>
  
  <canvas id="canvas" width="800" height="600"></canvas>
  
  <script>
    const canvas = document.getElementById('canvas');
    const ctx = canvas.getContext('2d');
    const serviceSelector = document.getElementById('serviceSelector');
    const generateBtn = document.getElementById('generateBtn');
    const downloadBtn = document.getElementById('downloadBtn');
    
    // Funzione per generare un'immagine placeholder
    function generatePlaceholder(serviceName) {
      // Crea sfondo con gradiente
      const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
      gradient.addColorStop(0, '#ffcccb'); // rosa chiaro
      gradient.addColorStop(1, '#ffffff'); // bianco
      
      ctx.fillStyle = gradient;
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      
      // Aggiungi elemento decorativo
      ctx.beginPath();
      ctx.arc(canvas.width / 2, canvas.height, 300, 0, Math.PI, true);
      ctx.fillStyle = 'rgba(230, 57, 70, 0.1)';
      ctx.fill();
      
      // Aggiungi testo del servizio
      ctx.font = 'bold 36px Arial';
      ctx.fillStyle = '#333';
      ctx.textAlign = 'center';
      ctx.fillText(serviceName, canvas.width / 2, canvas.height / 2 - 40);
      
      // Aggiungi scritta "placeholder"
      ctx.font = '24px Arial';
      ctx.fillStyle = '#666';
      ctx.fillText('Placeholder Image', canvas.width / 2, canvas.height / 2);
      
      // Aggiungi bordo
      ctx.strokeStyle = 'rgba(230, 57, 70, 0.5)';
      ctx.lineWidth = 10;
      ctx.strokeRect(0, 0, canvas.width, canvas.height);
    }
    
    // Funzione per scaricare l'immagine
    function downloadImage(filename) {
      const link = document.createElement('a');
      link.download = filename + '.jpg';
      link.href = canvas.toDataURL('image/jpeg', 0.8);
      link.click();
    }
    
    // Event listeners
    generateBtn.addEventListener('click', () => {
      const serviceName = serviceSelector.options[serviceSelector.selectedIndex].text;
      generatePlaceholder(serviceName);
    });
    
    downloadBtn.addEventListener('click', () => {
      const serviceId = serviceSelector.value;
      downloadImage(serviceId);
    });
    
    // Genera immagine predefinita all'avvio
    window.onload = () => {
      const serviceName = serviceSelector.options[serviceSelector.selectedIndex].text;
      generatePlaceholder(serviceName);
    };
  </script>
</body>
</html>
