<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Gestione delle richieste OPTIONS per CORS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Verifica che sia una richiesta POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405); // Method Not Allowed
    echo json_encode(['error' => 'Metodo non consentito']);
    exit();
}

// Ottieni il contenuto JSON dalla richiesta
$json = file_get_contents('php://input');
$data = json_decode($json, true);

// Verifica che i dati necessari siano presenti
if (!isset($data['email']) || !isset($data['message'])) {
    http_response_code(400); // Bad Request
    echo json_encode(['error' => 'Email e messaggio sono obbligatori']);
    exit();
}

// Estrai i dati dal JSON
$name = isset($data['name']) ? $data['name'] : 'Nome non fornito';
$email = $data['email'];
$subject = isset($data['subject']) ? $data['subject'] : 'Nessun oggetto';
$message = $data['message'];
$isEmergency = isset($data['isEmergency']) ? $data['isEmergency'] : false;
$phone = isset($data['phone']) ? $data['phone'] : 'Non fornito';
$address = isset($data['address']) ? $data['address'] : 'Non fornito';
$emergency_type = isset($data['emergency_type']) ? $data['emergency_type'] : 'Non specificato';

// Prepara l'oggetto dell'email
$emailSubject = $isEmergency 
    ? "🚨 EMERGENZA: $emergency_type" 
    : "Richiesta di contatto: $subject";

// Costruisci il corpo dell'email
$emailContent = "<h2>Nuova richiesta da $name</h2>\n<p><strong>Email:</strong> $email</p>\n";

// Aggiungi dettagli specifici per emergenza
if ($isEmergency) {
    $emailContent .= "<p><strong>Telefono:</strong> $phone</p>\n";
    $emailContent .= "<p><strong>Indirizzo:</strong> $address</p>\n";
    $emailContent .= "<p><strong>Tipo di emergenza:</strong> $emergency_type</p>\n";
}

// Aggiungi il messaggio
$emailContent .= "<p><strong>Messaggio:</strong></p>\n<p>$message</p>\n";

// Configurazione per l'invio di email
$to = '<EMAIL>';
$fromEmail = '<EMAIL>'; // Cambia con l'email appropriata

// Header per email HTML
$headers = "MIME-Version: 1.0\r\n";
$headers .= "Content-Type: text/html; charset=UTF-8\r\n";
$headers .= "From: Sito Inparo <$fromEmail>\r\n";
$headers .= "Reply-To: $email\r\n";

try {
    // Invia l'email
    $mailSent = mail($to, $emailSubject, $emailContent, $headers);
    
    if ($mailSent) {
        http_response_code(200);
        echo json_encode(['success' => true, 'message' => 'Email inviata con successo']);
    } else {
        http_response_code(500);
        echo json_encode(['error' => "Si è verificato un errore durante l'invio dell'email"]);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => "Si è verificato un errore durante l'invio dell'email: {$e->getMessage()}"]);
}
?>
