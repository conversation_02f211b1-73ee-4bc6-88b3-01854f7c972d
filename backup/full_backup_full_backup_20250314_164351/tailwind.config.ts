import type { Config } from "tailwindcss";
import plugin from 'tailwindcss/plugin';

export default {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        background: "var(--background)",
        foreground: "var(--foreground)",
        primary: {
          500: "#0078d4",
          600: "#0063b1",
          700: "#004e8c"
        },
        secondary: {
          100: "#f3f4f6",
          200: "#e5e7eb",
          300: "#d1d5db"
        },
        inparoblue: {
          100: "#e6f2ff",
          200: "#bfdfff",
          300: "#80baff",
          400: "#4d9fff",
          500: "#0078d4",
          600: "#0063b1",
          700: "#004e8c",
          800: "#003a69",
          900: "#002548"
        }
      },
      animation: {
        blob: "blob 7s infinite",
        "blob-slow": "blob 10s infinite",
        "blob-fast": "blob 5s infinite",
        "pulse-slow": "pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite",
        shimmer: "shimmer 2s infinite",
        "gradient-x": "gradient-x 3s ease infinite"
      },
      keyframes: {
        blob: {
          "0%": {
            transform: "translate(0px, 0px) scale(1)"
          },
          "33%": {
            transform: "translate(30px, -50px) scale(1.1)"
          },
          "66%": {
            transform: "translate(-20px, 20px) scale(0.9)"
          },
          "100%": {
            transform: "translate(0px, 0px) scale(1)"
          }
        },
        shimmer: {
          "100%": {
            transform: "translateX(100%)",
          },
        },
        "gradient-x": {
          "0%, 100%": {
            "background-position": "200% 0"
          },
          "50%": {
            "background-position": "0% 0"
          }
        }
      },
      boxShadow: {
        'contact': '0 4px 20px rgba(0, 0, 0, 0.05)',
        'glow': '0 0 15px rgba(255, 255, 255, 0.5)',
      },
      textShadow: {
        'glow': '0 0 10px rgba(255, 255, 255, 0.5)',
      },
    },
  },
  plugins: [
    plugin(function({ matchUtilities, theme }) {
      matchUtilities(
        {
          'text-shadow': (value) => ({
            textShadow: value,
          }),
        },
        { values: theme('textShadow') }
      )
    }),
    function ({ addUtilities }: { addUtilities: any }) {
      const newUtilities = {
        '.animation-delay-2000': {
          'animation-delay': '2s',
        },
        '.animation-delay-3000': {
          'animation-delay': '3s',
        },
        '.animation-delay-4000': {
          'animation-delay': '4s',
        },
      }
      addUtilities(newUtilities)
    },
  ],
} satisfies Config;
