# Changelog

Tutte le modifiche notevoli al progetto saranno documentate in questo file.

Il formato è basato su [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
e questo progetto aderisce al [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Non Rilasciato]

### Aggiunto
- Sezione `metadata` nelle traduzioni per migliorare SEO
- Componente `LanguageSelector` condiviso in `src/components/shared`
- Navigazione basata su Link per il cambio lingua
- Supporto per mantenere il percorso durante il cambio lingua
- Menu di navigazione responsive con supporto multilingua
- Componente di animazione Fade per transizioni fluide
- Traduzioni complete per il menu di navigazione in IT, DE, FR
- Effetto di cambio stile del menu allo scroll
- Pulsante di emergenza sempre visibile nel menu
- Form di contatto per emergenze con validazione
- Pagina dedicata ai servizi di emergenza
- Supporto per animazioni con Framer Motion

### Modificato
- Rimosso il componente `LanguageSelector` duplicato da `navigation`
- Aggiornato il tipo `Translations` per riflettere la struttura reale del JSON
- Sostituito `maintenance` con `cleaning` nelle traduzioni della home
- Migliorata la gestione delle traduzioni nel layout principale
- Ottimizzati i metadati per SEO, OpenGraph e Twitter cards
- Migliorato il sistema di routing multilingua
- Ottimizzata la struttura dei componenti
- Aggiornato il layout principale con il nuovo menu
- Migliorata la gestione delle traduzioni
- Semplificato il componente di animazione
- Aggiornata la documentazione del progetto

### Rimosso
- Rimosso l'uso del contesto `useLanguage` dal selettore di lingua
- Eliminata la duplicazione dei componenti di internazionalizzazione

### Risolto
- Problema con l'importazione dei componenti
- Errori nelle traduzioni mancanti
- Bug nel sistema di navigazione
- Problemi di rendering dei componenti animati
- Conflitti di stile nel menu mobile

### Da Testare
- Funzionamento della navigazione tra le lingue
- Mantenimento del percorso durante il cambio lingua
- Animazioni con framer-motion
- Rendering dei metadata
- Build completa del progetto

### Problemi Noti
- Potrebbero emergere errori TypeScript dopo le recenti modifiche
- Necessario verificare la completezza delle traduzioni
- Da testare la responsiveness del layout

## [0.1.0] - 2025-02-23

### Aggiunto
- Setup iniziale del progetto con Next.js 13+
- Configurazione di TailwindCSS
- Sistema base di localizzazione
- Struttura delle route multilingua
- Mock data per lo sviluppo
- Componenti base dell'interfaccia
- File di configurazione essenziali
