'use client';

import { motion } from 'framer-motion';
import type { ReactNode } from 'react';

export type FadeInViewProps = {
  children: ReactNode;
  className?: string;
  delay?: number;
};

export default function FadeInView({ children, className = '', delay = 0 }: FadeInViewProps) {
  return (
    <motion.div
      className={className}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: delay }}
    >
      {children}
    </motion.div>
  );
}
