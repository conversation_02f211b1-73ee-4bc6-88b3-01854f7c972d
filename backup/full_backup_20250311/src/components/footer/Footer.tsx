import Link from 'next/link';
import { FaPhone, FaEnvelope, FaMapMarkerAlt } from 'react-icons/fa';
import { Translations } from '@/types/translations';

interface FooterProps {
  lang: string;
  translations: Translations;
}

export function Footer({ lang, translations }: FooterProps) {
  return (
    <footer className="bg-gray-900 text-white py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* Contatti */}
          <div>
            <h3 className="text-lg font-semibold mb-4">{translations.common.contact}</h3>
            <ul className="space-y-3">
              <li className="flex items-center">
                <FaPhone className="mr-2" />
                <a href="tel:+41764662122" className="hover:text-gray-300">+41 76 466 2122</a>
              </li>
              <li className="flex items-center">
                <FaEnvelope className="mr-2" />
                <a href="mailto:<EMAIL>" className="hover:text-gray-300"><EMAIL></a>
              </li>
              <li className="flex items-start">
                <FaMapMarkerAlt className="mr-2 mt-1" />
                <span>Inparo Gmbh<br />Gubelstrasse 15<br />6300 Zug<br />Svizzera</span>
              </li>
            </ul>
          </div>

          {/* Servizi */}
          <div>
            <h3 className="text-lg font-semibold mb-4">{translations.nav.services}</h3>
            <ul className="space-y-2">
              <li>
                <Link href={`/${lang}/services`} className="hover:text-gray-300">
                  {translations.nav.services}
                </Link>
              </li>
              <li>
                <Link href={`/${lang}/renovation`} className="hover:text-gray-300">
                  {translations.nav.renovation}
                </Link>
              </li>
              <li>
                <Link href={`/${lang}/emergency`} className="hover:text-gray-300">
                  {translations.nav.emergency}
                </Link>
              </li>
              <li>
                <Link href={`/${lang}/contact`} className="hover:text-gray-300">
                  {translations.nav.contact}
                </Link>
              </li>
            </ul>
          </div>

          {/* Emergenza 24/7 */}
          <div>
            <h3 className="text-lg font-semibold mb-4">{translations.common.emergency}</h3>
            <p className="mb-4">{translations.footer?.emergencyDescription}</p>
            <Link
              href={`/${lang}/emergency`}
              className="inline-flex items-center bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 transition-colors"
            >
              <FaPhone className="mr-2" />
              {translations.common.callNow}
            </Link>
          </div>
        </div>

        {/* Privacy e Legal */}
        <div className="mt-12 pt-8 border-t border-gray-800 flex flex-col md:flex-row justify-between items-center">
          <div className="mb-4 md:mb-0 text-sm text-gray-400">
            <p>{translations.footer?.copyright}</p>
          </div>
          <div className="flex space-x-4 text-sm text-gray-400">
            <Link href={`/${lang}/privacy-policy`} className="hover:text-gray-300">
              Privacy Policy
            </Link>
            <Link href={`/${lang}/terms`} className="hover:text-gray-300">
              {translations.footer?.terms || 'Termini e Condizioni'}
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
}
