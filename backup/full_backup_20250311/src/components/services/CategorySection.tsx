'use client';

import { motion } from 'framer-motion';
import { FaArrowRight } from 'react-icons/fa';
import ServiceCard from './ServiceCard';
import { ServiceIconName } from './ServiceIcons';

interface Service {
  id: string;
  title: string;
  description: string;
  features: string[];
  icon?: ServiceIconName;
  image?: string;
  duration?: string;
  warranty?: string;
}

interface CategorySectionProps {
  category: {
    id: string;
    title: string;
    description: string;
    services: Service[];
  };
  isAlternate?: boolean;
  lang: string;
}

export default function CategorySection({
  category,
  isAlternate = false,
  lang
}: CategorySectionProps) {
  // Alternare tra stili diversi per le categorie
  const style = isAlternate 
    ? { bg: 'bg-gray-50', accent: 'from-blue-500 to-purple-500', patternOpacity: 'opacity-5' }
    : { bg: 'bg-white', accent: 'from-red-500 to-amber-500', patternOpacity: 'opacity-5' };
  
  const textColor = 'text-gray-900';
  const subtextColor = 'text-gray-600';
  
  // Estraiamo le proprietà dalla categoria
  const { title, description, services } = category;
  
  // Funzione per tradurre il testo dei pulsanti in base alla lingua
  const translateButtonText = (lang: string): string => {
    const buttonTextMap: Record<string, string> = {
      'it': 'Richiedi preventivo',
      'de': 'Angebot anfordern',
      'en': 'Request a quote',
      'fr': 'Demander un devis'
    };
    
    return buttonTextMap[lang] || 'Richiedi preventivo';
  };

  // Funzione per tradurre il testo introduttivo in base alla lingua
  const translateIntroText = (lang: string): string => {
    const introTextMap: Record<string, string> = {
      'it': 'I nostri esperti sono pronti a rispondere alle tue domande e fornirti un preventivo personalizzato.',
      'de': 'Unsere Experten stehen bereit, Ihre Fragen zu beantworten und Ihnen ein maßgeschneidertes Angebot zu unterbreiten.',
      'en': 'Our experts are ready to answer your questions and provide you with a personalized quote.',
      'fr': 'Nos experts sont prêts à répondre à vos questions et à vous fournir un devis personnalisé.'
    };
    
    return introTextMap[lang] || introTextMap['it'];
  };

  return (
    <div className={`relative py-20 ${style.bg} overflow-hidden`}>
      {/* Decorative elements */}
      <div className={`absolute -top-24 -right-24 w-96 h-96 rounded-full bg-gradient-to-br ${style.accent} ${style.patternOpacity} blur-3xl opacity-30`}></div>
      <div className={`absolute top-1/2 -left-24 w-64 h-64 rounded-full bg-gradient-to-tr ${style.accent} ${style.patternOpacity} blur-2xl opacity-20`}></div>
      
      {/* Subtle pattern */}
      <div className="absolute inset-0 -z-10 opacity-5">
        <svg className="absolute inset-0 h-full w-full stroke-current" aria-hidden="true">
          <defs>
            <pattern id={`grid-pattern-${category.id}`} width="60" height="60" patternUnits="userSpaceOnUse">
              <path d="M.5 0V60M60 .5H0" fill="none" />
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill={`url(#grid-pattern-${category.id})`} />
        </svg>
      </div>
      
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, margin: '-100px' }}
          transition={{ duration: 0.6, ease: [0.22, 1, 0.36, 1] }}
          className="mx-auto max-w-3xl"
        >
          <div className="flex flex-col sm:flex-row sm:items-end sm:justify-between mb-12">
            <div className="max-w-2xl">
              <div className="flex items-center mb-3">
                <div className={`h-1.5 w-10 bg-gradient-to-r ${style.accent} rounded-full mr-3`}></div>
                <span className="text-sm uppercase tracking-wider font-semibold text-gray-500">Categoria</span>
              </div>
              <h2 className={`text-3xl sm:text-4xl font-bold ${textColor}`}>
                {title}
              </h2>
              <p className={`mt-3 text-base leading-7 ${subtextColor} max-w-xl`}>
                {description}
              </p>
            </div>
            {/* Link 'Tutti i servizi' rimosso */}
          </div>
        </motion.div>

        <div className="mx-auto grid max-w-7xl grid-cols-1 gap-x-8 gap-y-12 sm:grid-cols-2 lg:grid-cols-3">
          {services.map((service, serviceIndex) => (
            <motion.div
              key={service.id}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true, margin: '-50px' }}
              transition={{ 
                duration: 0.5, 
                delay: serviceIndex * 0.1,
                ease: [0.22, 1, 0.36, 1]
              }}
            >
              <ServiceCard
                {...service}
                icon={service.icon || 'FaTools'}
                lang={lang}
                delay={0}
              />
            </motion.div>
          ))}
        </div>
        
        {/* Separatore e CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="mt-16 flex flex-col sm:flex-row items-center justify-between border-t border-gray-200 pt-10"
        >
          <p className="text-sm text-gray-500 max-w-md">{translateIntroText(lang)}</p>
          <a
            href={`/${lang}/contact`}
            className="mt-4 sm:mt-0 inline-flex items-center px-5 py-2.5 border border-transparent font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 shadow-sm transition-colors"
          >
            {translateButtonText(lang)}
          </a>
        </motion.div>
      </div>
    </div>
  );
}
