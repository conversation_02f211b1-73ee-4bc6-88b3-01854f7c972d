'use client';

import { createContext, useContext, useState, useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';

type LanguageContextType = {
  locale: string;
  setLocale: (locale: string) => void;
};

const LanguageContext = createContext<LanguageContextType>({
  locale: 'it',
  setLocale: () => {},
});

export function LanguageProvider({ 
  children,
  initialLocale = 'it'
}: { 
  children: React.ReactNode;
  initialLocale?: string;
}) {
  const router = useRouter();
  const pathname = usePathname();
  // Stato per tracciare se il componente è montato
  const [mounted, setMounted] = useState(false);
  // Usa initialLocale come valore predefinito, ma aggiornato solo lato client
  const [locale, setLocale] = useState(initialLocale);

  // Imposta mounted a true solo dopo il montaggio del componente
  useEffect(() => {
    setMounted(true);
  }, []);

  // Stato per controllare l'animazione di transizione
  const [isChangingLocale, setIsChangingLocale] = useState(false);

  const handleSetLocale = (newLocale: string) => {
    if (newLocale === locale || !mounted) return;
    
    // Avvia l'animazione di uscita
    setIsChangingLocale(true);
    
    // Aggiorna il locale e naviga dopo un piccolo ritardo
    setTimeout(() => {
      setLocale(newLocale);
      const newPath = pathname?.replace(/^\/[^\/]+/, `/${newLocale}`) || `/${newLocale}`;
      router.push(newPath);
    }, 300); // Ritardo corrispondente alla durata dell'animazione di uscita
  };

  // Quando il componente non è ancora montato, usiamo un placeholder per evitare discrepanze di hydration
  if (!mounted) {
    return (
      <LanguageContext.Provider value={{ locale: initialLocale, setLocale: () => {} }}>
        {children}
      </LanguageContext.Provider>
    );
  }

  // Rendering lato client con animazioni
  return (
    <LanguageContext.Provider value={{ locale, setLocale: handleSetLocale }}>
      {mounted ? (
        <AnimatePresence mode="wait">
          <motion.div
            key={locale}
            initial={{ opacity: isChangingLocale ? 0 : 1 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3, ease: "easeInOut" }}
            onAnimationComplete={() => setIsChangingLocale(false)}
            className="w-full"
          >
            {children}
          </motion.div>
        </AnimatePresence>
      ) : (
        <div className="w-full">
          {children}
        </div>
      )}
    </LanguageContext.Provider>
  );
}

export function useLanguage() {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
}
