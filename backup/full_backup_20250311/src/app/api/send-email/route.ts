import { NextRequest, NextResponse } from 'next/server';
import nodemailer from 'nodemailer';

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { name, email, subject, message, isEmergency, phone, address, emergency_type } = body;

    // Validazione basilare
    if (!email || !message) {
      return NextResponse.json(
        { error: 'Email e messaggio sono obbligatori' },
        { status: 400 }
      );
    }

    // Configura il trasporto email (in produzione, usare credenziali reali)
    const transporter = nodemailer.createTransport({
      host: process.env.EMAIL_HOST || 'smtp.example.com',
      port: parseInt(process.env.EMAIL_PORT || '587'),
      secure: process.env.EMAIL_SECURE === 'true',
      auth: {
        user: process.env.EMAIL_USER || '<EMAIL>',
        pass: process.env.EMAIL_PASS || 'password',
      },
    });

    // Prepara il contenuto dell'email in base al tipo di form
    const emailSubject = isEmergency 
      ? `🚨 EMERGENZA: ${emergency_type || 'Non specificata'}` 
      : `Richiesta di contatto: ${subject || 'Nessun oggetto'}`;

    // Costruisci il corpo dell'email
    let emailContent = `
      <h2>Nuova richiesta da ${name || 'Nome non fornito'}</h2>
      <p><strong>Email:</strong> ${email}</p>
    `;

    // Aggiungi dettagli specifici per emergenza
    if (isEmergency) {
      emailContent += `
        <p><strong>Telefono:</strong> ${phone || 'Non fornito'}</p>
        <p><strong>Indirizzo:</strong> ${address || 'Non fornito'}</p>
        <p><strong>Tipo di emergenza:</strong> ${emergency_type || 'Non specificato'}</p>
      `;
    }

    // Aggiungi messaggio
    emailContent += `
      <p><strong>Messaggio:</strong></p>
      <p>${message}</p>
    `;

    // Configurazione email
    const mailOptions = {
      from: `"Sito Inparo" <${process.env.EMAIL_FROM || '<EMAIL>'}>`,
      to: '<EMAIL>',
      subject: emailSubject,
      html: emailContent,
      replyTo: email,
    };

    // Simula l'invio in ambiente di sviluppo
    if (process.env.NODE_ENV === 'development') {
      console.log('Email simulata in ambiente di sviluppo:');
      console.log(mailOptions);
      
      // Aggiungi un ritardo per simulare l'invio
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      return NextResponse.json({ success: true, message: 'Email simulata con successo' });
    }

    // Invia l'email in produzione
    await transporter.sendMail(mailOptions);

    return NextResponse.json({
      success: true,
      message: 'Email inviata con successo'
    });
  } catch (error) {
    console.error('Errore nell\'invio dell\'email:', error);
    return NextResponse.json(
      { error: 'Si è verificato un errore durante l\'invio dell\'email' },
      { status: 500 }
    );
  }
}
