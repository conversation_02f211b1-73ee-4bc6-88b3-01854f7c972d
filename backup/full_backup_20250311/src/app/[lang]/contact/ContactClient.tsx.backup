'use client';

import { useState } from 'react';
import Image from 'next/image';
import FadeInView from "@/components/animations/FadeInView";
import { EmergencyContactForm } from "@/components/forms/EmergencyContactForm";
import { Translations } from "@/types/translations";

interface ContactClientProps {
  t: Translations;
  lang: string;
}

export default function ContactClient({ t, lang }: ContactClientProps) {
  const [formType, setFormType] = useState<'emergency' | 'general'>('general');

  return (
    <main>
      {/* Hero Section - Design moderno e accattivante */}
      <section className="relative h-[60vh] min-h-[450px] overflow-hidden bg-gradient-to-br from-gray-50 via-white to-red-50 pt-16">
        {/* Abstract shapes background */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-24 -right-24 w-96 h-96 bg-red-100 rounded-full mix-blend-multiply filter blur-3xl opacity-70 animate-blob"></div>
          <div className="absolute top-96 -left-20 w-72 h-72 bg-blue-100 rounded-full mix-blend-multiply filter blur-3xl opacity-70 animate-blob animation-delay-2000"></div>
          <div className="absolute -bottom-32 right-12 w-80 h-80 bg-pink-100 rounded-full mix-blend-multiply filter blur-3xl opacity-70 animate-blob animation-delay-4000"></div>
        </div>
        
        {/* Diagonal divider */}
        <div className="absolute bottom-0 left-0 right-0 h-16 bg-white transform -skew-y-1"></div>
        
        <div className="container max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10 h-full flex flex-col justify-center">
          <div className="text-center max-w-3xl mx-auto">
            <FadeInView>
              <span className="inline-flex items-center px-4 py-1.5 rounded-full bg-gradient-to-r from-red-600 to-red-500 text-white text-sm font-medium mb-5 shadow-md">
                <svg className="w-4 h-4 mr-1.5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                  <path d="M2 5a2 2 0 012-2h7a2 2 0 012 2v4a2 2 0 01-2 2H9l-3 3v-3H4a2 2 0 01-2-2V5z"></path>
                  <path d="M15 7v2a4 4 0 01-4 4H9.828l-1.766 1.767c.28.149.599.233.938.233h2l3 3v-3h2a2 2 0 002-2V9a2 2 0 00-2-2h-1z"></path>
                </svg>
                {"Get in touch"}
              </span>
              <h1 className="text-5xl md:text-7xl font-manrope font-bold tracking-tighter text-gray-900 leading-tight mb-6 bg-clip-text text-transparent bg-gradient-to-r from-gray-900 via-red-700 to-gray-800">
                {t.contact.title}
              </h1>
              <div className="h-1.5 w-24 mx-auto bg-gradient-to-r from-red-500 to-red-600 rounded-full my-7"></div>
              <p className="text-base md:text-xl font-sans leading-relaxed text-gray-700 max-w-2xl mx-auto mb-8">
                {t.contact.description}
              </p>
            </FadeInView>
          </div>
        </div>
      </section>

      {/* Form Section */}
      <div className="mx-auto max-w-7xl px-6 py-12 sm:py-20 lg:px-8">
        <div className="mx-auto max-w-3xl">
          {/* Form Section Curved Header */}
          <div className="text-center mb-16 relative">
            <div className="absolute inset-0 -z-10">
              <div className="absolute -top-10 -left-10 w-32 h-32 bg-blue-50 rounded-full filter blur-3xl opacity-60"></div>
              <div className="absolute -bottom-10 -right-10 w-32 h-32 bg-red-50 rounded-full filter blur-3xl opacity-60"></div>
            </div>
            <span className="inline-block px-3 py-1 text-xs font-semibold uppercase tracking-wider text-red-700 bg-red-50 rounded-md mb-3">
              {"Get in touch"}
            </span>
            <h2 className="text-3xl font-bold text-white sm:text-4xl mb-4">
              {t.contact.title}
            </h2>
            <p className="text-lg text-white text-opacity-90 max-w-2xl mx-auto">
              {t.contact.description}
            </p>
          </div>
          
          {/* Form Type Selector - Modern Design */}
          <div className="mb-10 relative z-10">
            <div className="bg-white p-1.5 rounded-2xl shadow-lg border border-gray-100 flex flex-col sm:flex-row justify-center max-w-md mx-auto overflow-hidden">
              <button
                onClick={() => setFormType('general')}
                className={`
                  flex-1 rounded-xl px-5 py-3.5 text-base font-medium transition-all duration-300 flex items-center justify-center gap-2 
                  ${formType === 'general' 
                    ? 'bg-gradient-to-r from-red-600 to-red-500 text-white shadow-md transform scale-[1.03]' 
                    : 'text-gray-700 hover:bg-gray-50'}
                `}
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                {t.contact.generalInquiry}
              </button>
              <button
                onClick={() => setFormType('emergency')}
                className={`
                  flex-1 rounded-xl px-5 py-3.5 text-base font-medium transition-all duration-300 flex items-center justify-center gap-2 
                  ${formType === 'emergency' 
                    ? 'bg-gradient-to-r from-red-600 to-red-500 text-white shadow-md transform scale-[1.03]' 
                    : 'text-gray-700 hover:bg-gray-50'}
                `}
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                {t.contact.emergency}
              </button>
            </div>
          </div>

          {/* Form */}
          <div className="mt-12 relative">
            {/* Forma decorativa */}
            <div className="absolute -top-6 -right-6 w-32 h-32 bg-red-50 rounded-full mix-blend-multiply filter blur-2xl opacity-70"></div>
            <div className="absolute -bottom-6 -left-6 w-32 h-32 bg-blue-50 rounded-full mix-blend-multiply filter blur-2xl opacity-70"></div>
            
            <div className="absolute inset-0 bg-gradient-to-r from-red-50 via-white to-blue-50 opacity-40 -z-10 rounded-3xl transform -rotate-1 scale-105"></div>
            <div className="overflow-hidden rounded-3xl bg-white shadow-xl border border-gray-100 backdrop-blur-sm">
              {/* Badge dell'intestazione del form */}
              <div className="w-full h-2 bg-gradient-to-r from-red-500 to-red-600"></div>
              
              <div className="px-6 py-10 sm:p-12">
                {formType === 'emergency' ? (
                  <EmergencyContactForm t={t} lang={lang} />
                ) : (
                  <div className="space-y-8">
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                      {/* Name field - redesigned */}
                      <div className="col-span-1 group">
                        <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2 transition-colors group-focus-within:text-red-600">
                          {t.contact.form.name}
                        </label>
                        <div className="relative">
                          <div className="absolute inset-y-0 left-0 pl-3.5 flex items-center pointer-events-none transition-colors group-focus-within:text-red-500">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400 group-focus-within:text-red-500 transition-colors" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                            </svg>
                          </div>
                          <input
                            type="text"
                            name="name"
                            id="name"
                            className="pl-10 block w-full rounded-xl border-gray-200 shadow-sm focus:border-red-500 focus:ring-red-500 sm:text-sm transition-all duration-300 py-3 bg-gray-50 focus:bg-white text-gray-800"
                            placeholder="Il tuo nome completo"
                          />
                        </div>
                      </div>
                      
                      {/* Email field - redesigned */}
                      <div className="col-span-1 group">
                        <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2 transition-colors group-focus-within:text-red-600">
                          {t.contact.form.email}
                        </label>
                        <div className="relative">
                          <div className="absolute inset-y-0 left-0 pl-3.5 flex items-center pointer-events-none">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400 group-focus-within:text-red-500 transition-colors" viewBox="0 0 20 20" fill="currentColor">
                              <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                              <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                            </svg>
                          </div>
                          <input
                            type="email"
                            name="email"
                            id="email"
                            className="pl-10 block w-full rounded-xl border-gray-200 shadow-sm focus:border-red-500 focus:ring-red-500 sm:text-sm transition-all duration-300 py-3 bg-gray-50 focus:bg-white text-gray-800"
                            placeholder="La tua email"
                          />
                        </div>
                      </div>
                    </div>
                    
                    {/* Subject field - modernized */}
                    <div className="group">
                      <label htmlFor="subject" className="block text-sm font-medium text-gray-700 mb-2 transition-colors group-focus-within:text-red-600">
                        {"Oggetto"}
                      </label>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3.5 flex items-center pointer-events-none">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400 group-focus-within:text-red-500 transition-colors" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
                          </svg>
                        </div>
                        <input
                          type="text"
                          name="subject"
                          id="subject"
                          className="pl-10 block w-full rounded-xl border-gray-200 shadow-sm focus:border-red-500 focus:ring-red-500 sm:text-sm transition-all duration-300 py-3 bg-gray-50 focus:bg-white text-gray-800"
                          placeholder="Oggetto della richiesta"
                        />
                      </div>
                    </div>
                    
                    {/* Message field - modernized */}
                    <div className="group">
                      <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2 transition-colors group-focus-within:text-red-600">
                        {t.contact.form.message}
                      </label>
                      <div className="relative">
                        <div className="absolute top-3 left-3.5 flex items-start pointer-events-none">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400 group-focus-within:text-red-500 transition-colors" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M18 5v8a2 2 0 01-2 2h-5l-5 4v-4H4a2 2 0 01-2-2V5a2 2 0 012-2h12a2 2 0 012 2zM7 8H5v2h2V8zm2 0h2v2H9V8zm6 0h-2v2h2V8z" clipRule="evenodd" />
                          </svg>
                        </div>
                        <textarea
                          id="message"
                          name="message"
                          rows={5}
                          className="pl-10 block w-full rounded-xl border-gray-200 shadow-sm focus:border-red-500 focus:ring-red-500 sm:text-sm transition-all duration-300 bg-gray-50 focus:bg-white text-gray-800"
                          placeholder="Scrivi il tuo messaggio qui..."
                        />
                      </div>
                    </div>
                    
                    {/* Privacy checkbox - modernized */}
                    <div className="flex items-start p-4 bg-gray-50 rounded-xl border border-gray-100">
                      <div className="flex-shrink-0 mt-0.5">
                        <div className="relative flex items-center justify-center h-6 w-6">
                          <input
                            id="privacy"
                            name="privacy"
                            type="checkbox"
                            className="h-5 w-5 text-red-600 focus:ring-red-500 focus:ring-offset-0 border-gray-300 rounded cursor-pointer"
                          />
                          <svg className="absolute h-6 w-6 text-red-500 opacity-0 peer-checked:opacity-100 transform scale-90 transition-all duration-200" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="12" cy="12" r="12" fill="currentColor" fillOpacity="0.2"/>
                          </svg>
                        </div>
                      </div>
                      <div className="ml-3">
                        <label htmlFor="privacy" className="text-sm font-medium text-gray-700 cursor-pointer">
                          {t.contact.form.privacy}
                        </label>
                        <p className="text-xs text-gray-500 mt-1">I tuoi dati saranno trattati secondo la nostra <a href="#" className="text-red-600 hover:text-red-800 underline transition-colors">politica sulla privacy</a>.</p>
                      </div>
                    </div>
                    
                    {/* Submit button - modernized */}
                    <div className="pt-4">
                      <button
                        type="submit"
                        className="w-full flex justify-center items-center rounded-xl bg-gradient-to-r from-red-600 to-red-500 px-6 py-4 text-base font-semibold text-white shadow-lg hover:from-red-500 hover:to-red-400 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-red-600 transition-all duration-300 transform hover:-translate-y-1 hover:shadow-xl"
                      >
                        <span>{t.contact.form.submit}</span>
                        <svg xmlns="http://www.w3.org/2000/svg" className="ml-2 h-5 w-5 animate-pulse" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
                        </svg>
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Sezione informativa di contatto - redesigned */}
      <section className="relative overflow-hidden bg-gradient-to-br from-gray-50 to-white py-20 mt-10">
        {/* Background decorations */}
        <div className="absolute -top-10 -right-10 w-40 h-40 bg-red-50 rounded-full filter blur-3xl opacity-60"></div>
        <div className="absolute -bottom-10 -left-10 w-40 h-40 bg-blue-50 rounded-full filter blur-3xl opacity-60"></div>
        
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Heading */}
          <div className="text-center mb-14">
            <h2 className="text-3xl font-bold text-gray-900 sm:text-4xl mb-4">
              Come raggiungerci
            </h2>
            <div className="h-1 w-16 mx-auto bg-gradient-to-r from-red-500 to-red-600 rounded-full my-4"></div>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Siamo sempre disponibili per aiutarti. Scegli il metodo che preferisci per contattarci.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 xl:gap-12">
            {/* Indirizzo - redesigned */}
            <div className="group bg-white p-8 rounded-2xl shadow-lg border border-gray-100 flex flex-col items-center text-center transform transition-all duration-500 hover:shadow-xl hover:-translate-y-2 relative overflow-hidden">
              <div className="absolute top-0 inset-x-0 h-2 bg-gradient-to-r from-red-500 to-red-600 transform transition-transform duration-300 group-hover:scale-x-100"></div>
              <div className="w-16 h-16 bg-gradient-to-br from-red-500 to-red-600 rounded-2xl flex items-center justify-center mb-6 transform rotate-3 group-hover:rotate-6 transition-all duration-300 shadow-md">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">La nostra sede</h3>
              <p className="text-gray-600 mb-4">Via Roma 123<br/>00100 Roma, Italia</p>
              <a href="https://maps.google.com" target="_blank" rel="noopener noreferrer" className="inline-flex items-center text-sm font-medium text-red-600 hover:text-red-800 transition-colors">
                <span>Vedi su Google Maps</span>
                <svg className="ml-1 w-4 h-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd"></path></svg>
              </a>
            </div>
            
            {/* Email - redesigned */}
            <div className="group bg-white p-8 rounded-2xl shadow-lg border border-gray-100 flex flex-col items-center text-center transform transition-all duration-500 hover:shadow-xl hover:-translate-y-2 relative overflow-hidden">
              <div className="absolute top-0 inset-x-0 h-2 bg-gradient-to-r from-red-500 to-red-600 transform transition-transform duration-300 group-hover:scale-x-100"></div>
              <div className="w-16 h-16 bg-gradient-to-br from-red-500 to-red-600 rounded-2xl flex items-center justify-center mb-6 transform -rotate-3 group-hover:rotate-0 transition-all duration-300 shadow-md">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">Email</h3>
              <p className="text-gray-600 mb-4"><EMAIL><br/><EMAIL></p>
              <a href="mailto:<EMAIL>" className="inline-flex items-center text-sm font-medium text-red-600 hover:text-red-800 transition-colors">
                <span>Invia un'email</span>
                <svg className="ml-1 w-4 h-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd"></path></svg>
              </a>
            </div>
            
            {/* Telefono - redesigned */}
            <div className="group bg-white p-8 rounded-2xl shadow-lg border border-gray-100 flex flex-col items-center text-center transform transition-all duration-500 hover:shadow-xl hover:-translate-y-2 relative overflow-hidden">
              <div className="absolute top-0 inset-x-0 h-2 bg-gradient-to-r from-red-500 to-red-600 transform transition-transform duration-300 group-hover:scale-x-100"></div>
              <div className="w-16 h-16 bg-gradient-to-br from-red-500 to-red-600 rounded-2xl flex items-center justify-center mb-6 transform rotate-3 group-hover:-rotate-6 transition-all duration-300 shadow-md">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">Telefono</h3>
              <p className="text-gray-600 mb-4">+39 06 1234567<br/>Lun-Ven: 9:00-18:00</p>
              <a href="tel:+390612345678" className="inline-flex items-center text-sm font-medium text-red-600 hover:text-red-800 transition-colors">
                <span>Chiama ora</span>
                <svg className="ml-1 w-4 h-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd"></path></svg>
              </a>
            </div>
          </div>
        </div>
        
        {/* Decorative wave pattern */}
        <div className="absolute left-0 right-0 bottom-0 h-8 overflow-hidden">
          <svg className="w-full h-full" viewBox="0 0 1200 120" preserveAspectRatio="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V120H0V95.72C57.71,118.68,121.41,111.23,165.53,101.1Z" fill="#ffffff"></path>
          </svg>
        </div>
      </section>
    </main>
  );
}
