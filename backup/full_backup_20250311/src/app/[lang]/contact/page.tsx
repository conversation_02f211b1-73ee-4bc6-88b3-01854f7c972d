import ContactClient from './ContactClient';
import { getTranslation } from '@/i18n/server';

interface ContactPageProps {
  params: Promise<{ lang: string }>;
}

export default async function ContactPage({ params }: ContactPageProps) {
  // Attendiamo che i parametri siano disponibili
  const resolvedParams = await params;
  const t = await getTranslation(resolvedParams.lang);

  return <ContactClient t={t} lang={resolvedParams.lang} />;
}
