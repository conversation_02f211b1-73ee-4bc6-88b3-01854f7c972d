import '../globals.css';
import { Inter } from 'next/font/google';
import NavigationWrapper from '../../components/navigation/NavigationWrapper';
import { Footer } from '../../components/footer/Footer';
import { getTranslation } from '../../i18n/server';
import { locales, Locale, defaultLocale } from '../../i18n/settings';
import { Metadata } from 'next';
import { Translations } from '../../types/translations';
import { ReactNode } from 'react';
import { LanguageProvider } from '@/app/providers';

const inter = Inter({ subsets: ['latin'] });

type LayoutProps = {
  children: ReactNode;
  params: { lang: string };
}

export async function generateMetadata({ params }: LayoutProps): Promise<Metadata> {
  // Attendi che i parametri siano disponibili e poi ottieni la lingua
  const resolvedParams = await params;
  const lang = (resolvedParams?.lang && locales.includes(resolvedParams.lang as Locale)) 
    ? (resolvedParams.lang as Locale) 
    : defaultLocale;
  const translations = await getTranslation(lang) as Translations;

  return {
    metadataBase: new URL('https://inparo.ch'),
    title: translations.metadata.title,
    description: translations.metadata.description,
    icons: {
      icon: [
        { url: '/favicon.ico' },
        { url: '/icon.png', type: 'image/png', sizes: '32x32' },
      ],
      apple: '/apple-icon.png',
    },
    manifest: '/manifest.json',
    alternates: {
      canonical: `/${lang}`,
      languages: Object.fromEntries(
        locales.map(locale => [locale, `/${locale}`])
      ),
    },
    openGraph: {
      title: translations.metadata.title,
      description: translations.metadata.description,
      url: `/${lang}`,
      siteName: 'Inparo',
      locale: lang,
      type: 'website',
      images: [
        {
          url: '/og-image.jpg',
          width: 1200,
          height: 630,
          alt: translations.metadata.ogImageAlt,
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: translations.metadata.title,
      description: translations.metadata.description,
      images: ['/og-image.jpg'],
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
  };
}

export async function generateStaticParams() {
  return locales.map((locale) => ({
    lang: locale,
  }));
}

export const viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
};

export default async function RootLayout({ children, params }: LayoutProps) {
  // Attendi che i parametri siano disponibili e poi ottieni la lingua
  const resolvedParams = await params;
  const lang = (resolvedParams?.lang && locales.includes(resolvedParams.lang as Locale)) 
    ? (resolvedParams.lang as Locale) 
    : defaultLocale;
  const translations = await getTranslation(lang) as Translations;

  return (
    <html lang={lang} suppressHydrationWarning>
      <body className={inter.className}>
        <LanguageProvider initialLocale={lang}>
          <NavigationWrapper lang={lang} />
          {children}
          <Footer lang={lang} translations={translations} />
        </LanguageProvider>
      </body>
    </html>
  );
}
