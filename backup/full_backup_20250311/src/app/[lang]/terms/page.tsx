import { Metadata } from 'next';
import { getTranslation } from '@/i18n/server';
import TermsClient from './TermsClient';

interface TermsPageProps {
  params: { lang: string };
}

export async function generateMetadata({ params }: TermsPageProps): Promise<Metadata> {
  return {
    title: `Termini e Condizioni | Inparo`,
    description: 'Termini e condizioni di utilizzo dei servizi Inparo GmbH.'
  };
}

export default async function TermsPage({ params }: TermsPageProps) {
  const t = await getTranslation(params.lang);
  return <TermsClient t={t} lang={params.lang} />;
}
