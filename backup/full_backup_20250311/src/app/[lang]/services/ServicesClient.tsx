'use client';

import React from 'react';
import { ServiceData, Translations } from "@/types/translations";
import Image from "next/image";
import Link from "next/link";
import { motion } from 'framer-motion';
import { FaArrowRight } from "react-icons/fa";
import ServiceCard from "@/components/services/ServiceCard";
import { ServiceIconName } from "@/components/services/ServiceIcons";

interface ServicesClientProps {
  data: ServiceData;
  t: Translations;
  lang: string;
}

// Definizione corretta dell'interfaccia del servizio
interface ServiceItem {
  id: string;
  title: string;
  description: string;
  features: string[];
  icon?: ServiceIconName; 
  image?: string;
  duration?: string;
  warranty?: string;
}

const fadeIn = {
  hidden: { opacity: 0, y: 20 },
  visible: (i: number) => ({
    opacity: 1,
    y: 0,
    transition: {
      delay: i * 0.1,
      duration: 0.5,
    }
  })
};

export default function ServicesClient({ data, lang }: ServicesClientProps) {
  // Controllo per il client-side rendering per prevenire errori di idratazione
  const [isClient, setIsClient] = React.useState(false);

  React.useEffect(() => {
    setIsClient(true);
  }, []);

  // Se i dati non sono disponibili, mostriamo un messaggio o un contenitore vuoto
  if (!data || !data.categories) {
    console.error('Dati dei servizi non disponibili:', data);
    return <main className="bg-white"><div className="container mx-auto py-12 px-4 text-center">Caricamento dei servizi in corso...</div></main>;
  }

  // Preparo i dati dei servizi per la visualizzazione in sezioni
  // Limito il numero dei servizi a esattamente 3 per categoria
  const serviceCategories = data.categories.map(category => ({
    ...category,
    services: category.services.slice(0, 3).map(service => ({
      ...service as ServiceItem,
      icon: (service as ServiceItem).icon || 'FaTools' as ServiceIconName,
      image: (service as ServiceItem).image || undefined,
      duration: (service as ServiceItem).duration || undefined,
      warranty: (service as ServiceItem).warranty || undefined
    }))
  }));

  // Se non siamo sul client, mostra un contenitore vuoto per evitare errori di idratazione
  if (!isClient) {
    return <main className="bg-white"></main>;
  }

  // Funzione per tradurre il testo del pulsante CTA
  const translateCtaText = (lang: string): string => {
    const ctaTextMap: Record<string, string> = {
      'it': 'Richiedi preventivo',
      'de': 'Angebot anfordern',
      'en': 'Request a quote',
      'fr': 'Demander un devis'
    };
    
    return ctaTextMap[lang] || 'Richiedi preventivo';
  };

  return (
    <main className="bg-white">
      {/* Hero Section - Design con immagine a pieno schermo */}
      <section className="relative h-[80vh] min-h-[500px] overflow-hidden">
        <div className="absolute inset-0 w-full h-full">
          <Image
            src="/images/services/renovation.jpg"
            alt="Servizi di ristrutturazione"
            fill
            className="object-cover brightness-[0.65]"
            priority
          />
        </div>
        
        <div className="absolute inset-0 bg-black bg-opacity-40"></div>
        
        <div className="container max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10 h-full flex flex-col justify-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center max-w-3xl mx-auto"
          >
            <h1 className="text-4xl md:text-6xl font-manrope font-medium tracking-tight text-white leading-tight mb-6">
              {data.hero.title}
            </h1>
            <div className="h-px w-24 mx-auto bg-gradient-to-r from-red-500 to-purple-600 my-6"></div>
            <p className="text-base md:text-xl font-sans leading-relaxed text-white text-opacity-90 max-w-2xl mx-auto mb-8">
              {data.hero.description}
            </p>
            <Link 
              href={`/${lang}/contact/`} 
              className="inline-flex items-center px-8 py-4 text-base font-medium text-white bg-transparent hover:bg-gradient-to-r hover:from-red-600 hover:to-purple-700 border-2 border-white hover:border-purple-700 transition-colors duration-300 ease-in-out"
            >
              {translateCtaText(lang)}
              <FaArrowRight className="ml-3 h-4 w-4" />
            </Link>
          </motion.div>
        </div>
      </section>
      
      {/* Services Sections - Layout moderno e minimale */}
      <div className="py-16 md:py-20">
        <div className="container max-w-6xl mx-auto">
          {serviceCategories.map((category, categoryIndex) => (
            <section key={category.id} className={`py-12 md:py-16 px-4 sm:px-6 lg:px-8 ${categoryIndex % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}>
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5 }}
                className="mb-12"
              >
                <h2 className="text-2xl md:text-3xl font-manrope font-light text-gray-900">
                  <span className="font-medium">{category.title}</span>
                </h2>
                <div className="h-px w-10 bg-red-500 my-4"></div>
                <p className="text-base font-sans text-gray-600 max-w-2xl">
                  {category.description}
                </p>
              </motion.div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
                {category.services.map((service: ServiceItem, index: number) => (
                  <motion.div
                    key={service.id}
                    custom={index}
                    initial="hidden"
                    whileInView="visible"
                    viewport={{ once: true, margin: '-50px' }}
                    variants={fadeIn}
                  >
                    <ServiceCard
                      id={service.id}
                      title={service.title}
                      description={service.description}
                      features={service.features || []}
                      icon={service.icon || 'FaTools'}
                      image={service.image}
                      duration={service.duration}
                      warranty={service.warranty}
                      lang={lang}
                      delay={index * 0.1}
                    />
                  </motion.div>
                ))}
              </div>
            </section>
          ))}
        </div>
      </div>
      
      {/* CTA Section - Design ultra-minimalista */}
      <section className="py-16 md:py-24 bg-gray-900 text-white">
        <div className="container max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 items-center gap-8">
            <div>
              <h2 className="text-2xl md:text-3xl font-manrope font-light">
                {data.cta?.title || 'Hai un progetto in mente?'}
              </h2>
              <div className="h-px w-10 bg-red-500 my-4"></div>
              <p className="mt-3 text-base font-sans text-gray-300">
                {data.cta?.description || 'Contattaci per una consulenza gratuita e un preventivo personalizzato'}
              </p>
            </div>
            <div className="text-right">
              <Link 
                href={data.cta?.url || `/${lang}/contact/`} 
                className="inline-flex items-center px-6 py-3 text-base font-medium text-white bg-transparent hover:bg-gradient-to-r hover:from-red-600 hover:to-purple-700 border border-white hover:border-purple-700 transition-colors duration-200 ease-in-out rounded-sm"
              >
                {data.cta?.button || 'Richiedi Preventivo'}
                <FaArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
}
