'use client';

import { useLanguage } from '@/app/providers';
import it from './locales/it.json';
import de from './locales/de.json';
import fr from './locales/fr.json';

const translations = {
  it,
  de,
  fr,
};

export type Locale = keyof typeof translations;
export type TranslationKey = string;

export function useTranslation() {
  const { locale, setLocale } = useLanguage();

  function t(key: TranslationKey): string {
    const keys = key.split('.');
    let value: any = translations[locale as Locale];

    for (const k of keys) {
      if (value === undefined) return key;
      value = value[k];
    }

    if (typeof value !== 'string') return key;
    return value;
  }

  return {
    t,
    locale,
    setLocale,
  };
}
