import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

const locales = ['it', 'de', 'fr'] as const;
type Locale = typeof locales[number];

function isLocale(value: string): value is Locale {
  return locales.includes(value as Locale);
}

function getLocale(request: NextRequest): Locale {
  const pathname = request.nextUrl.pathname;
  const pathnameLocale = pathname.split('/')[1];

  if (isLocale(pathnameLocale)) {
    return pathnameLocale;
  }

  const acceptLanguage = request.headers.get('accept-language')
  if (acceptLanguage) {
    const preferredLocale = acceptLanguage
      .split(',')
      .map(lang => lang.split(';')[0])
      .find(lang => isLocale(lang.substring(0, 2)))

    if (preferredLocale) {
      return preferredLocale.substring(0, 2) as Locale;
    }
  }
  return 'it'
}

export function middleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname;

  // Skip if the pathname starts with ignored paths
  if (pathname.startsWith('/_next') || 
      pathname.startsWith('/api') || 
      pathname.startsWith('/static') ||
      pathname === '/favicon.ico') {
    return NextResponse.next();
  }

  // Check if the pathname is missing a locale
  const pathnameHasLocale = locales.some(
    locale => pathname.startsWith(`/${locale}/`) || pathname === `/${locale}`
  );

  if (!pathnameHasLocale) {
    const locale = getLocale(request);
    return NextResponse.redirect(
      new URL(
        `/${locale}${pathname}${request.nextUrl.search}`,
        request.url
      )
    );
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    // Skip all internal paths (_next, api, etc)
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
}
