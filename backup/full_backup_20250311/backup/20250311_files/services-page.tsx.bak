import { getTranslation } from "@/i18n/server";
import ServicesClient from "./ServicesClient";
// Utilizziamo il nuovo file dei servizi per evitare problemi di cache
import { services_new as services } from "@/mocks/services_new";

interface ServicesPageProps {
  params: Promise<{ lang: string }>;
}

export default async function ServicesPage({ params }: ServicesPageProps) {
  // Attendiamo che i parametri siano disponibili
  const resolvedParams = await params;
  const t = await getTranslation(resolvedParams.lang);
  const data = services[resolvedParams.lang as keyof typeof services];

  return <ServicesClient data={data} t={t} lang={resolvedParams.lang} />;
}
