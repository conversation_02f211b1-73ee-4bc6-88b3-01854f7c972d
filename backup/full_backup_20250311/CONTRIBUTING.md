# Contribuire al Progetto

Grazie per il tuo interesse a contribuire a Inparo Web! Questo documento fornisce le linee guida per contribuire al progetto.

## 🌟 Come Contribuire

1. Fork del repository
2. Crea un branch per la tua feature (`git checkout -b feature/AmazingFeature`)
3. Commit delle modifiche (`git commit -m 'Add some AmazingFeature'`)
4. Push al branch (`git push origin feature/AmazingFeature`)
5. Apri una Pull Request

## 📝 Linee Guida per il Codice

### Stile del Codice
- Usa TypeScript per tutto il codice
- Segui le convenzioni di ESLint e Prettier configurate
- Mantieni i componenti piccoli e focalizzati
- Usa nomi descrittivi per variabili e funzioni
- Documenta il codice quando necessario

### Componenti React
- Usa componenti funzionali e hooks
- Implementa la gestione degli errori
- Aggiungi prop types o TypeScript interfaces
- Segui il principio di responsabilità singola
- Implementa test per nuove funzionalità

### CSS/Tailwind
- Usa le classi utility di Tailwind
- Segui il mobile-first approach
- Mantieni la coerenza con il design system
- Evita CSS custom quando possibile
- Usa variabili per colori e dimensioni comuni

### Localizzazione
- Aggiungi sempre le traduzioni in tutte le lingue supportate
- Usa chiavi descrittive per le traduzioni
- Mantieni una struttura coerente nei file di traduzione
- Testa le traduzioni in tutte le lingue

## 🔍 Review Process

1. **Self Review**
   - Il codice compila senza errori
   - Tutti i test passano
   - Il linter non mostra errori
   - Le traduzioni sono complete
   - Il design è responsive

2. **Code Review**
   - Il codice segue le linee guida
   - La documentazione è aggiornata
   - Non ci sono regressioni
   - Le performance sono mantenute
   - Il codice è testabile

## 📋 Pull Request Checklist

- [ ] Ho testato le modifiche localmente
- [ ] Ho aggiunto/aggiornato i test
- [ ] Ho aggiornato la documentazione
- [ ] Ho aggiunto le traduzioni necessarie
- [ ] Ho verificato il design responsive
- [ ] Ho aggiornato il changelog
- [ ] Ho seguito le linee guida del codice

## 🐛 Segnalazione Bug

Usa il sistema di issue di GitHub e includi:
- Descrizione dettagliata del problema
- Steps per riprodurre il bug
- Comportamento atteso vs attuale
- Screenshot se pertinenti
- Ambiente di sviluppo/produzione
- Browser/dispositivo usato

## 💡 Proposta Feature

Per proporre nuove feature:
1. Verifica che non esista già una issue simile
2. Crea una nuova issue con:
   - Descrizione dettagliata della feature
   - Casi d'uso
   - Benefici attesi
   - Possibili drawbacks
   - Mock/wireframe se pertinenti

## 📜 Licenza

Contribuendo al progetto, accetti che il tuo codice sarà rilasciato sotto la stessa licenza del progetto (MIT).
