# Inparo Web - Documentazione Completa

Questo repository contiene il sito web aziendale multilingua per Inparo, specializzato in servizi di ristrutturazione e pronto intervento in Svizzera. Di seguito troverai una documentazione dettagliata che ti permetterà di apportare modifiche, gestire traduzioni e deployare il sito in modo efficiente.

## Indice

- [Panoramica](#panoramica)
- [Caratteristiche](#caratteristiche)
- [Tecnologie](#tecnologie)
- [Requisiti di Sistema](#requisiti-di-sistema)
- [Installazione e Setup](#installazione-e-setup)
- [Struttura del Progetto](#struttura-del-progetto)
- [Guida alle Modifiche](#guida-alle-modifiche)
  - [Modificare Contenuti](#modificare-contenuti)
  - [Aggiungere Nuove Pagine](#aggiungere-nuove-pagine)
  - [Gestire le Traduzioni](#gestire-le-traduzioni)
  - [Modificare Stili e UI](#modificare-stili-e-ui)
  - [Modificare Sezione Emergenze](#modificare-sezione-emergenze)
- [Componenti Principali](#componenti-principali)
- [Deployment](#deployment)
  - [Build di Produzione](#build-di-produzione)
  - [Deployment FTP](#deployment-ftp)
  - [Deployment su Piattaforme Cloud](#deployment-su-piattaforme-cloud)
- [Troubleshooting](#troubleshooting)
- [Manutenzione](#manutenzione)
- [Contatti e Supporto](#contatti-e-supporto)

## Panoramica

Inparo Web è un sito aziendale multilingua sviluppato con Next.js e React, ottimizzato per prestazioni e SEO. Il sito supporta italiano, tedesco e francese, con un design responsive e moderno che si adatta a tutti i dispositivi. L'architettura modulare permette facili aggiornamenti e manutenzione.

## Caratteristiche

- 🌍 **Supporto multilingua completo**: IT, DE, FR con gestione automatica dei percorsi URL
- 🎨 **Design responsive e moderno**: basato su Tailwind CSS con layout fluido
- ⚡ **Performance ottimizzate**: caricamento rapido grazie a Next.js e ottimizzazioni integrate
- 📱 **Mobile-first**: esperienza utente perfetta su tutti i dispositivi
- 🔍 **SEO avanzato**: metadata dinamici, struttura semantica e URL localizzati
- 🎭 **Animazioni fluide**: implementate con Framer Motion per una migliore UX
- 📨 **Form di contatto funzionante**: con validazione e invio email tramite API route
- 🚨 **Sezione emergenze**: con showcase prodotti Trotec, multilingua e responsive
- 🧩 **Componenti riutilizzabili**: architettura modulare per facilità di manutenzione

## Tecnologie

- **[Next.js 15](https://nextjs.org/)**: Framework React con server-side rendering e routing avanzato
- **[TypeScript](https://www.typescriptlang.org/)**: Tipizzazione statica per codice più sicuro
- **[Tailwind CSS](https://tailwindcss.com/)**: Framework CSS utility-first per styling rapido
- **[Framer Motion](https://www.framer.com/motion/)**: Libreria per animazioni fluide e reattive
- **[React Icons](https://react-icons.github.io/react-icons/)**: Set completo di icone per l'interfaccia
- **[Next-Intl](https://next-intl-docs.vercel.app/)**: Gestione delle traduzioni e internazionalizzazione
- **[Nodemailer](https://nodemailer.com/)**: Invio di email dal form di contatto

## Requisiti di Sistema

- Node.js 18.0.0 o superiore
- NPM 9.0.0 o superiore
- Git

## Installazione e Setup

```bash
# Clona il repository
git clone https://github.com/yourusername/inparo-web.git

# Entra nella directory del progetto
cd inparo-web

# Installa le dipendenze
npm install

# Crea un file .env.local per le variabili d'ambiente
touch .env.local

# Avvia il server di sviluppo
npm run dev
```

Dopo l'avvio, il sito sarà disponibile all'indirizzo: http://localhost:3000

## Struttura del Progetto

```
/
├── public/                # File statici accessibili pubblicamente
│   ├── images/           # Immagini del sito
│   ├── fonts/           # Font personalizzati
│   └── favicon.ico      # Favicon del sito
│
├── src/                  # Codice sorgente dell'applicazione
│   ├── app/              # Struttura del router Next.js
│   │   ├── [lang]/       # Route dinamiche per le diverse lingue
│   │   │   ├── contact/      # Pagina di contatto
│   │   │   ├── emergency/    # Pagina emergenze
│   │   │   ├── privacy-policy/ # Pagina privacy
│   │   │   ├── services/     # Pagina servizi
│   │   │   └── terms/        # Pagina termini e condizioni
│   │   ├── api/          # API routes per funzioni server-side
│   │   │   └── send-email/  # Endpoint per l'invio di email
│   │   ├── layout.tsx    # Layout principale dell'app
│   │   └── page.tsx      # Pagina principale (redirect alla lingua default)
│   │
│   ├── components/        # Componenti React riutilizzabili
│   │   ├── animations/   # Componenti di animazione (es. Fade)
│   │   ├── emergency/    # Componenti per la pagina emergenze
│   │   ├── footer/       # Componenti del footer
│   │   ├── forms/        # Form di contatto e altri form
│   │   ├── layout/       # Componenti di layout
│   │   ├── navigation/   # Menu e navigazione
│   │   ├── services/     # Componenti per la pagina servizi
│   │   └── shared/       # Componenti condivisi (bottoni, cards, etc.)
│   │
│   ├── contexts/         # Context provider React
│   ├── hooks/            # Hook personalizzati
│   ├── i18n/             # Configurazione internazionalizzazione
│   │   └── locales/      # File JSON con traduzioni (it.json, de.json, fr.json)
│   │
│   ├── mocks/            # Dati mock per lo sviluppo
│   ├── styles/           # Stili CSS globali
│   ├── types/            # Definizione dei tipi TypeScript
│   ├── utils/            # Funzioni di utilità
│   └── backup/           # Directory per backup temporanei
│
├── .eslintrc.json        # Configurazione ESLint
├── .gitignore            # File ignorati da git
├── next.config.js        # Configurazione Next.js
├── package.json          # Dipendenze e script npm
├── postcss.config.js     # Configurazione PostCSS per Tailwind
├── tailwind.config.js    # Configurazione Tailwind CSS
└── tsconfig.json         # Configurazione TypeScript
```

## Guida alle Modifiche

### Modificare Contenuti

#### Testi e Traduzioni

I testi del sito sono gestiti tramite file di traduzione JSON nella cartella `src/i18n/locales/`. Per modificare un testo:

1. Apri i file di traduzione corrispondenti (`it.json`, `de.json`, `fr.json`)
2. Individua la chiave del testo da modificare
3. Modifica il valore mantenendo la struttura

Esempio di modifica in `it.json`:

```json
{
  "common": {
    "navigation": {
      "home": "Homepage",  # Modifica qui il testo
      "services": "Servizi"
    }
  }
}
```

#### Immagini

Per sostituire immagini esistenti:

1. Prepara la nuova immagine (preferibilmente con lo stesso nome e dimensioni dell'originale)
2. Sostituisci il file nella cartella `public/images/`

Per aggiungere nuove immagini:

1. Carica l'immagine nella cartella `public/images/`
2. Riferisciti all'immagine nei componenti usando `<Image src="/images/nome-immagine.jpg" ... />`

### Aggiungere Nuove Pagine

1. Crea una nuova cartella nella directory `src/app/[lang]/` con il nome della pagina (es. `about/`)
2. Crea un file `page.tsx` all'interno della cartella:

```tsx
import { Metadata } from 'next';
import { getTranslations } from '@/i18n';

export async function generateMetadata({ params }: { params: { lang: string } }): Promise<Metadata> {
  const { lang } = params;
  const t = await getTranslations(lang);
  
  return {
    title: t.about.meta.title,
    description: t.about.meta.description,
  };
}

export default async function AboutPage({ params }: { params: { lang: string } }) {
  const { lang } = params;
  const t = await getTranslations(lang);
  
  return (
    <main>
      <h1>{t.about.title}</h1>
      {/* Contenuto della pagina */}
    </main>
  );
}
```

3. Aggiorna i file di traduzione in `src/i18n/locales/` con le nuove chiavi
4. Aggiungi il link nel componente di navigazione (`src/components/navigation/MainNav.tsx`)

### Gestire le Traduzioni

#### Aggiungere Nuove Chiavi di Traduzione

1. Apri i file `src/i18n/locales/it.json`, `de.json` e `fr.json`
2. Aggiungi la nuova chiave in tutti e tre i file mantenendo la stessa struttura:

```json
// it.json
{
  "sezione": {
    "nuova_chiave": "Testo in italiano"
  }
}

// de.json
{
  "sezione": {
    "nuova_chiave": "Testo in tedesco"
  }
}

// fr.json
{
  "sezione": {
    "nuova_chiave": "Testo in francese"
  }
}
```

#### Utilizzare le Traduzioni nei Componenti

In componenti server-side:

```tsx
import { getTranslations } from '@/i18n';

export default async function MioComponente({ params }: { params: { lang: string } }) {
  const { lang } = params;
  const t = await getTranslations(lang);
  
  return <div>{t.sezione.nuova_chiave}</div>;
}
```

In componenti client-side:

```tsx
'use client';

export default function MioComponenteClient({ t, lang }: { t: any, lang: string }) {
  return <div>{t.sezione.nuova_chiave}</div>;
}
```

### Modificare Stili e UI

#### Modificare Stili con Tailwind

Gli stili principali sono gestiti tramite Tailwind CSS. Per modificare lo stile di un componente:

1. Individua il componente in `src/components/`
2. Modifica le classi Tailwind direttamente nell'elemento JSX

Esempio:
```tsx
// Prima
<button className="bg-blue-600 text-white px-4 py-2 rounded-md">
  Pulsante
</button>

// Dopo la modifica
<button className="bg-green-600 text-white px-6 py-3 rounded-xl shadow-lg">
  Pulsante
</button>
```

#### Modificare Colori Globali

Per cambiare i colori del tema globale:

1. Apri `tailwind.config.js`
2. Modifica l'oggetto `theme.extend.colors`

```js
module.exports = {
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#f0f9ff',
          100: '#e0f2fe',
          // ...
          900: '#0c4a6e',
        },
        // altri colori...
      },
    },
  },
};
```

#### Modificare Animazioni

Le animazioni sono gestite principalmente con Framer Motion. Per modificarle:

1. Trova il componente in `src/components/animations/`
2. Modifica i parametri dell'animazione

Esempio:
```tsx
// Prima
<motion.div
  initial={{ opacity: 0, y: 20 }}
  animate={{ opacity: 1, y: 0 }}
  transition={{ duration: 0.5 }}
>

// Dopo la modifica
<motion.div
  initial={{ opacity: 0, y: 50 }}
  animate={{ opacity: 1, y: 0 }}
  transition={{ duration: 0.7, ease: "easeOut" }}
>
```

### Modificare Sezione Emergenze

La sezione emergenze è gestita principalmente dal componente `EmergencyClient.tsx` e dai dati mock in `src/mocks/emergency.ts`.

#### Modificare Colori della Sezione Emergenze

1. Apri `src/components/emergency/EmergencyClient.tsx`
2. Cerca le classi CSS che contengono colori (es. `bg-blue-600`, `text-blue-700`, ecc.)
3. Modifica i colori secondo le tue preferenze (es. da blu a verde: `bg-blue-600` → `bg-green-600`)

#### Modificare Prodotti Trotec

1. Apri `src/mocks/emergency.ts`
2. Trova l'oggetto `trotec` all'interno dell'oggetto principale per ogni lingua
3. Modifica il titolo, la descrizione o i prodotti

```typescript
export const emergencyServices = {
  it: {
    trotec: {
      title: "Attrezzature professionali Trotec",
      description: "Utilizziamo tecnologie avanzate...",
      products: [
        {
          id: 1,
          title: "Pompe sommerse per acqua sporca",
          description: "Le pompe sommerse Trotec...",
          link: "https://it.trotec.com"
        },
        // altri prodotti...
      ]
    }
  },
  // altre lingue...
};
```

## Componenti Principali

### Layout e Navigazione

- `src/app/layout.tsx`: Layout principale con head, meta tags e struttura di base
- `src/app/[lang]/layout.tsx`: Layout specifico per la lingua con navigazione
- `src/components/navigation/MainNav.tsx`: Barra di navigazione principale

### Pagine Principali

- `src/app/[lang]/page.tsx`: Homepage
- `src/app/[lang]/services/page.tsx`: Pagina servizi
- `src/app/[lang]/emergency/page.tsx`: Pagina emergenze
- `src/app/[lang]/contact/page.tsx`: Pagina contatti

### Componenti Chiave

- `src/components/emergency/EmergencyClient.tsx`: Componente principale per la pagina emergenze
- `src/components/forms/ContactForm.tsx`: Form di contatto
- `src/components/animations/Fade.tsx`: Componente per animazioni di fade-in

## Deployment

### Build di Produzione

Per creare una build di produzione:

```bash
npm run build
```

Per testare la build di produzione localmente:

```bash
npm run start
```

### Deployment FTP

Per deployare il sito su un hosting tradizionale via FTP:

1. Crea la build di produzione: `npm run build`
2. Se il tuo hosting supporta Node.js:
   - Carica i seguenti file e cartelle:
     - `.next/` (cartella completa)
     - `public/` (cartella completa)
     - `package.json`
     - `next.config.js`
   - Installa le dipendenze sul server: `npm install --production`
   - Configura il server per eseguire `npm start`

3. Se il tuo hosting non supporta Node.js (solo file statici):
   - Aggiungi lo script `"export": "next build && next export"` al `package.json`
   - Esegui `npm run export` per generare la versione statica
   - Carica l'intero contenuto della cartella `out/` via FTP

### Deployment su Piattaforme Cloud

#### Vercel (consigliato per Next.js)

```bash
npm install -g vercel
vercel login
vercel
```

#### Netlify

```bash
npm install -g netlify-cli
netlify login
netlify deploy
```

## Troubleshooting

### Problemi comuni e soluzioni

#### Le traduzioni non funzionano

- Verifica che la chiave sia presente in tutti i file di traduzione
- Controlla che la struttura delle chiavi sia identica in tutti i file
- Assicurati di passare correttamente il parametro `lang` ai componenti

#### Errori di build

- **Errore di tipo TypeScript**: Controlla i tipi e le interfacce
- **Errore di importazione**: Verifica i percorsi di importazione (case-sensitive)

#### Problemi di responsive design

- Utilizza gli strumenti di sviluppo del browser per testare diversi viewport
- Verifica le classi Tailwind responsive (es. `md:flex`, `lg:grid-cols-2`)

## Manutenzione

### Aggiornamento delle Dipendenze

Per aggiornare tutte le dipendenze all'ultima versione compatibile:

```bash
npm update
```

Per aggiornare Next.js a una major version:

```bash
npm install next@latest react@latest react-dom@latest
```

### Backup

È consigliabile creare backup regolari del progetto:

```bash
# Crea una cartella di backup con timestamp
mkdir -p ./backup/$(date +%Y%m%d)

# Copia i file sorgente (esclusi node_modules e .next)
cp -R ./src ./public ./package.json ./next.config.js ./tailwind.config.js ./backup/$(date +%Y%m%d)/
```

## Contatti e Supporto

Per domande o problemi relativi al progetto, contatta:

- **Responsabile del progetto**: [Nome] - [<EMAIL>]
- **Supporto tecnico**: [Nome] - [<EMAIL>]

---

MIT 
