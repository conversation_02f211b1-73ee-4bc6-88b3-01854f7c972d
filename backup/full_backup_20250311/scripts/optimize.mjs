// Script per ottimizzare le immagini per Next.js

import sharp from 'sharp';
import fs from 'fs/promises';
import path from 'path';

const SOURCE_DIR = './public/images/services';
const TARGET_DIR = './public/images/services/optimized';

// Assicuriamoci che la directory di destinazione esista
async function ensureDir(dirPath) {
  try {
    await fs.mkdir(dirPath, { recursive: true });
  } catch (err) {
    if (err.code !== 'EEXIST') throw err;
  }
}

// Mappa gli ID dei servizi alle immagini
const serviceImageMap = {
  'complete-renovation': 'renovation.jpg',
  'interior-renovation': 'house-painter.jpg',
  'specialized-services': 'electrical.jpg',
  'bathroom-renovation': 'bathroom.jpg',
  'regular-cleaning': 'wall.jpg',
  'deep-cleaning': 'tiler.jpg',
  'garden-maintenance': 'wooden-doors.jpg'
};

// Ottimizza un'immagine
async function optimizeImage(sourcePath, targetPath, width = 800) {
  console.log(`Ottimizzando: ${sourcePath} -> ${targetPath}`);
  
  try {
    await sharp(sourcePath)
      .resize(width)
      .webp({ quality: 75 }) // Formato WebP con qualità 75%
      .toFile(targetPath);
      
    // Verifica dimensione finale
    const stats = await fs.stat(targetPath);
    const sizeInKb = stats.size / 1024;
    console.log(`  Dimensione ottimizzata: ${sizeInKb.toFixed(2)} KB`);
    
    return true;
  } catch (err) {
    console.error(`Errore nell'ottimizzazione di ${sourcePath}:`, err);
    return false;
  }
}

// Funzione principale
async function main() {
  try {
    // Crea la directory di destinazione
    await ensureDir(TARGET_DIR);
    
    // Ottimizza ogni immagine del servizio
    for (const [serviceId, imageFile] of Object.entries(serviceImageMap)) {
      const sourcePath = path.join(SOURCE_DIR, imageFile);
      const targetPath = path.join(TARGET_DIR, `${serviceId}.webp`);
      
      await optimizeImage(sourcePath, targetPath);
    }
    
    // Crea un'immagine di default
    const defaultSource = path.join(SOURCE_DIR, 'renovation.jpg');
    const defaultTarget = path.join(TARGET_DIR, 'default.webp');
    await optimizeImage(defaultSource, defaultTarget);
    
    console.log('\nOttimizzazione completata! Le immagini sono ora pronte per Next.js');
  } catch (err) {
    console.error('Errore durante il processo di ottimizzazione:', err);
  }
}

// Esegui lo script
main();
