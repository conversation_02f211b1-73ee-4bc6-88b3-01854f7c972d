const sharp = require('sharp');
const fs = require('fs');
const path = require('path');

// Directory delle immagini
const sourceDir = path.join(__dirname, '../public/images/services');
const outputDir = path.join(__dirname, '../public/images/services/optimized');

// Crea la directory di output se non esiste
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

// Mappatura dei nomi file ai service ID
const fileToServiceMap = {
  'bathroom.jpg': 'bathroom-renovation',
  'electrical.jpg': 'specialized-services',
  'house-painter.jpg': 'interior-renovation',
  'interior-design.jpg': 'interior-renovation',
  'renovation.jpg': 'complete-renovation',
  'roof.jpg': 'specialized-services',
  'tiler.jpg': 'specialized-services',
  'wall.jpg': 'interior-renovation',
  'wooden-doors.jpg': 'specialized-services'
};

// Target size in kilobytes
const targetSizeKB = 80;

async function optimizeImage(inputFile, outputFile, targetKB) {
  try {
    // Inizia con qualità 80
    let quality = 80;
    let optimizedBuffer;
    let currentSize = Infinity;
    
    console.log(`Ottimizzazione di ${inputFile}...`);
    
    // Loop di ottimizzazione fino a quando raggiungiamo la dimensione target
    while (currentSize > targetKB * 1024 && quality > 10) {
      optimizedBuffer = await sharp(inputFile)
        .resize(1000, null, { fit: 'inside', withoutEnlargement: true }) // Massimo 1000px di larghezza
        .webp({ quality }) // Usa il formato WebP per migliori prestazioni
        .toBuffer();
      
      currentSize = optimizedBuffer.length;
      console.log(`  Qualità: ${quality}, Dimensione: ${(currentSize / 1024).toFixed(2)}KB`);
      
      // Riduci la qualità se ancora sopra il target
      if (currentSize > targetKB * 1024) {
        quality -= 5;
      }
    }
    
    // Salva l'immagine ottimizzata
    await fs.promises.writeFile(outputFile, optimizedBuffer);
    console.log(`✓ Ottimizzato a ${(currentSize / 1024).toFixed(2)}KB: ${outputFile}`);
    return true;
  } catch (error) {
    console.error(`Errore nell'ottimizzazione di ${inputFile}:`, error);
    return false;
  }
}

async function processAllImages() {
  try {
    // Leggi tutti i file nella directory
    const files = await fs.promises.readdir(sourceDir);
    
    // Filtra solo i file di immagine
    const imageFiles = files.filter(file => {
      const ext = path.extname(file).toLowerCase();
      return ['.jpg', '.jpeg', '.png', '.webp'].includes(ext);
    });
    
    console.log(`Trovate ${imageFiles.length} immagini da ottimizzare...`);
    
    // Processa ogni immagine
    for (const file of imageFiles) {
      const inputPath = path.join(sourceDir, file);
      
      // Determina il nome del file di output basato sulla mappatura
      const serviceId = fileToServiceMap[file] || path.parse(file).name;
      const outputPath = path.join(outputDir, `${serviceId}.webp`);
      
      await optimizeImage(inputPath, outputPath, targetSizeKB);
    }
    
    // Crea un'immagine di default
    const defaultSource = path.join(sourceDir, imageFiles[0]); // Usa la prima immagine come base
    const defaultOutput = path.join(outputDir, 'default.webp');
    await optimizeImage(defaultSource, defaultOutput, targetSizeKB);
    
    console.log('\nProcesso di ottimizzazione completato!');
    console.log(`Le immagini ottimizzate si trovano in: ${outputDir}`);
    console.log('\nPer utilizzare le immagini ottimizzate, modifica il percorso in ServiceCard.tsx:');
    console.log('Da: `/images/services/${id}.svg` a `/images/services/optimized/${id}.webp`');
  } catch (error) {
    console.error('Errore durante il processo di ottimizzazione:', error);
  }
}

// Esegui lo script
processAllImages();
