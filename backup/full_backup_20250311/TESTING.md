# Testing Guide

Questa guida descrive le procedure di testing per il progetto Inparo Web.

## Struttura dei Test

```
src/
└── __tests__/
    ├── components/      # Test dei componenti
    ├── i18n/           # Test delle traduzioni
    └── utils/          # Test delle utility
```

## Tipi di Test

### 1. Unit Test
- Componenti React isolati
- Funzioni di utility
- Helper per le traduzioni
- Validatori e formattatori

### 2. Integration Test
- Navigazione multilingua
- Routing dinamico
- Caricamento traduzioni
- Interazioni tra componenti

### 3. E2E Test
- Flussi utente completi
- Cambio lingua
- Form di contatto
- Navigazione responsive

## Componenti da Testare

### LanguageSelector
- [ ] Cambio lingua corretto
- [ ] Mantenimento del percorso
- [ ] UI responsive
- [ ] Animazioni
- [ ] Accessibilità

### Navigation
- [ ] Menu responsive
- [ ] Link corretti
- [ ] Traduzioni
- [ ] <PERSON><PERSON><PERSON> scroll
- [ ] Mobile menu

### Footer
- [ ] Link funzionanti
- [ ] Traduzioni
- [ ] Social media
- [ ] Contatti

## Comandi

```bash
# Esegui tutti i test
npm test

# Esegui test con coverage
npm run test:coverage

# Esegui test in watch mode
npm run test:watch

# Esegui E2E test
npm run test:e2e
```

## Best Practices

1. **Test Isolation**
   - Ogni test deve essere indipendente
   - Usare mock per le dipendenze
   - Reset dello stato tra i test

2. **Naming Conventions**
   - `[component].test.tsx` per componenti
   - `[utility].test.ts` per utility
   - Descrizioni chiare dei test

3. **Coverage**
   - Mirare a >80% coverage
   - Focus su logica di business
   - Test dei casi edge

4. **Accessibilità**
   - Test ARIA labels
   - Test keyboard navigation
   - Test screen readers

## Tools

- Jest
- React Testing Library
- Cypress (E2E)
- MSW (API mocking)
- jest-axe (accessibility)

## CI/CD Integration

I test vengono eseguiti automaticamente:
- Ad ogni push
- Prima del merge
- Durante il deployment

## Debugging

```bash
# Debug test specifici
npm test -- --debug

# Test singolo file
npm test -- [file-path]

# Update snapshot
npm test -- -u
```

## Note per i Contributor

1. Scrivere test per ogni nuova feature
2. Mantenere i test aggiornati
3. Documentare casi particolari
4. Seguire le best practices
5. Verificare la coverage
