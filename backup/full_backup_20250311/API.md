# API Documentation

## Endpoints

### Contenuti Multilingua

#### GET /api/content/:lang
Recupera tutti i contenuti per una specifica lingua

#### GET /api/content/:lang/:section
Recupera i contenuti di una specifica sezione per lingua

### Analytics

#### GET /api/analytics/summary
Recupera il riepilogo delle metriche principali:
- Visite per lingua
- Conversioni
- Tempo medio sulla pagina
- Pagine più visitate

#### POST /api/analytics/event
Registra un nuovo evento analytics

### Contatti

#### POST /api/contact
Endpoint per l'invio del form di contatto

## Modelli Dati

### Contenuto
```typescript
interface Content {
  id: string;
  section: string;
  lang: 'de' | 'it' | 'fr';
  content: Record<string, any>;
  updatedAt: Date;
}
```

### Evento Analytics
```typescript
interface AnalyticsEvent {
  id: string;
  type: string;
  lang: string;
  page: string;
  timestamp: Date;
  metadata?: Record<string, any>;
}
```

### Form Contatto
```typescript
interface ContactForm {
  name: string;
  email: string;
  phone?: string;
  message: string;
  service: string;
  lang: string;
}
```
